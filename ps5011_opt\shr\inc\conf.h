/**************************************************************************/
/*                                                                        */
/*  Copyright (C) 2000-2016, Phison Electronics Corp., Inc.               */
/*  All rights reserved                                                   */
/*                                                                        */
/*  The content of this document is confidential and shall be applied     */
/*  subject to the terms and conditions of the license agreement and      */
/*  other applicable laws. Any unauthorized access, use or disclosure     */
/*  of this document is strictly prohibited and may be punishable         */
/*  under laws.                                                           */
/*                                                                        */
/*  conf.h                                                                */
/*                                                                        */
/*                                                                        */
/*                                                                        */
/**************************************************************************/

#ifndef _CONF_H_
#define _CONF_H_
#include "opt_env.h"
/* ---------------------------------------------------------------------------------------------------
 *   DEVICE/SYSTEM CONFIGUATIONS
 * ---------------------------------------------------------------------------------------------------*/
/*
 * CONFIG CONTROLLER TYPE
 */

#define PS5013_EN (OPT_CATEGORY_CONTROLLER == CONTROLLER_PS5013)
#define S17_EN	(OPT_CATEGORY_CONTROLLER == CONTROLLER_PS5017)
#define PS5021_EN (OPT_CATEGORY_CONTROLLER == CONTROLLER_PS5021)

// define HW type (MODEL, FPGA, and ASIC)
#define HW_MODEL            (0)
#define HW_FPGA             (1)
#define HW_ASIC             (2)


#define BURNER_CODE         (0)         /* 0: burner, 1: firmware */


#ifdef WIN32
#define HW                  (HW_MODEL)
#define ICE_MODE            (1)         /* 1: ice mode, 0: boot rom mode */
#else
// HW and ICE_MODE should be defined in project setting
//#define ICE_MODE            (1)
#endif

#if (HW == HW_FPGA)
#define CONFIG_TWO_FPGA                 (1) /* modify if need */
#else
#define CONFIG_TWO_FPGA                 (0)
#endif



#if (CORE_ID == 0)
#define CONFIG_UART_ENABLE              (1)
#define CONFIG_UART_ENABLE_RX           (CONFIG_UART_ENABLE && 0)
#else /* (CORE_ID == 1) */
#define CONFIG_UART_ENABLE              (1)
#define CONFIG_UART_ENABLE_RX           (CONFIG_UART_ENABLE && 0)
#endif


/**************************************************************************/
/* CPU Config   ICCM0   ICCM1   ICACHE  DCCM0B0 DCCM0B1 DCCM1   DCACHE    */
/* ICCM0_128K    128K    32K     N/A     16K     16K     48K     N/A      */
/* WITH_DDR      112K    N/A     32K     16K     16K     48K     16K      */
/* DCCM1_64K     112K    32K     N/A     16K     16K     64K     N/A      */
/**************************************************************************/
#define CONFIG_ICCM0_128K                      (0)// ICCM expand and disable dcache
#define CONFIG_DCCM1_64K                       (0)// DCCM expand and disable dcache
#if (CONFIG_ICCM0_128K || CONFIG_DCCM1_64K)
#define CONFIG_ICCM1_32K                       (1)// enable ICCM1 and disable icache
#else
#define CONFIG_ICCM1_32K                       (0)
#endif




/* ---------------------------------------------------------------------------------------------------
 *   DEBUG/VERIFICATION CONFIGUATIONS
 * ---------------------------------------------------------------------------------------------------*/


/*
 *  DEBUG_LEVEL: 0(No Debug); 1(Show Error); 2(Show Message); 3(Show DBG Message);
 */
#define CONFIG_DEBUG_LEVEL              (2)





/*
 * ---------------------------------------------------------------------------------------------------
 *   HOST LAYER CONFIGUATIONS
 * ---------------------------------------------------------------------------------------------------
 */

/*
 *
 */
#define CONFIG_HOST_NVME                (1) /* 0: AHCI, 1: NVMe     */

/*
 * TCG ENABLE
 */
#define CONFIG_TCG_SSC                  (0)


/*
 * ---------------------------------------------------------------------------------------------------
 *   FLASH TRANSACTION LAYER (FTL) CONFIGUATIONS
 * ---------------------------------------------------------------------------------------------------
 */

/*
 *
 */
#define CONFIG_PTE_OP_X                         (3) /* 3X pte op */

/*
 *
 */
#define CONFIG_FORCE_DATA_GC_THRESHOLD          (3)         /* TBD: number of free data vb */
#define CONFIG_BG_DATA_GC_THRESHOLD             (3 + 7)    /* TBD: number of free data vb */



/* ---------------------------------------------------------------------------------------------------
 *   FLASH PHYSICAL LAYER (FPL) CONFIGUATIONS
 * ---------------------------------------------------------------------------------------------------*/

/*
 * CONFIG Flash IP
 */

#if ((HW == HW_FPGA) && (CONFIG_TWO_FPGA == 0))
#define CONFIG_NUM_FIP_CE_TOTAL         (16)
#define CONFIG_NUM_FIP_CH               (2)
#define CONFIG_NUM_FIP_CE_PER_CH        (8) //  this should not be differnt from two board
#else
#define CONFIG_NUM_FIP_CE_TOTAL         (S17_EN ? (16): (32))
#define CONFIG_NUM_FIP_CH               (S17_EN ? (2): (4))
#define CONFIG_NUM_FIP_CE_PER_CH        (8)
#endif

#define CONFIG_FIP_MTQ_DEPTH_PER_CE     (8)


/*
 * CONFIG
 */

#define CONFIG_NUM_EXT_DIE              (1)
#define CONFIG_NUM_DIE                  (2)

#define CONFIG_NUM_PLANE_PER_SB         (128)   // include die-page
#define CONFIG_MAX_PLANE_PER_CE         (2)

#define CONFIG_MAX_VB_PER_BANK          (1024 + 512)              /* vb_cnt not limited by power of 2 */

#define CONFIG_MAX_VB_TOTAL             (CONFIG_MAX_VB_PER_BANK * CONFIG_NUM_DIE)




// tmp to define here
#define CONFIG_WORDLINE_PAGE_OFFSET     (3)
#define CONFIG_MAX_BBS_CNT              (32)   // tmp define



/* ---------------------------------------------------------------------------------------------------
 *   OTHER CONFIGUATIONS (HAL, ...)
 * ---------------------------------------------------------------------------------------------------*/



/*
 * MT INDEX TRIT
 */

#define CONFIG_MT_IDX_TRIG                      (1)     /* MT Global Trigger, MT INDEX MODE */
#define CONFIG_INT_IDX_MODE                     (0)     /* INT BY INDEX MODE, INT INDEX MODE */

/*
 * OTHERS
 */

#define CONFIG_CE_BITMAP_MODE                   (0)     // 0: CE index mode. 1: CE bitmap mode.
#define CONFIG_MT_CE_METHOD_3                   (1)
#define CONFIG_MTQ_ERR_INFO_MANAGEMENT          (1)
#define CONFIG_FIP_TEST_DIS_CONV                (1)
#define CONFIG_FIP_TEST_DIS_INV                 (1)     // disable INV for firmware porting
#define CONFIG_FIP_TEST_EN_BYTE_INTERLEAVE      (0)
#define CONFIG_AUTO_UPD_FRM_PTR                 (1)

/*
 * COP0 CONFIG
 */
#define CONFIG_BBMP_ENABLE                      (1)
#define CONFIG_BBMP_IDX_MODE                    (0) //0: binary search mode. 1: index mode
#define CONFIG_VBRMP_ENABLE                     (0)
#define CONFIG_ALWAYS_REC_ERR_TIE_OUT           (1)
#define CONFIG_READ_NOR_CQ_RESPONSE             (1)
#define CONFIG_ENABLE_DEBUG_CH_CE               (1)
#define CONFIG_ENABLE_BYPASS_CONV               (1)   //bypass conversion, disable conversion = 1. with conversion = 0.

/*
 * DEFINE FLASH TYPE
 */
// enum flash type including vendor
#define FLASH_TYPE_TOSHIBA_2D_TLC               (0)
#define FLASH_TYPE_TOSHIBA_3D_TLC               (1)
#define FLASH_TYPE_HYNIX_2D_TLC                 (2)
#define FLASH_TYPE_HYNIX_3D_TLC                 (3)
#define FLASH_TYPE_MICRON_3D_MLC                (4)
#define FLASH_TYPE_MICRON_3D_TLC                (5)
#define FLASH_TYPE_SANDISK_3D_TLC               (6)
#define FLASH_TYPE_TOSHIBA_SLC                  (7)
#define FLASH_TYPE_MICRON_3D_QLC				(8)     //zerio n48r add
#define FLASH_TYPE_BICS5_3D_TLC           		(11)
#define FLASH_TYPE_BICS6_3D_TLC           		(12)
#define FLASH_TYPE_YMTC_3D_TLC           		(13)
#define FLASH_TYPE_HYNIX_3D_QLC                 (14)	//Reip Porting 3D-V7 QLC Add
#define FLASH_TYPE_YMTC_3D_QLC					(15)    //ems add--karl
#define FLASH_TYPE_BICS8_3D_TLC           		(16)    //zerio BICS8 Add
#define FLASH_TYPE_SAMSUNG_3D_TLC				(17)
#define FLASH_TYPE_BICS6_3D_QLC           		(18)    //zerio bics6 qlc add
#define FLASH_TYPE_SAMSUNG_3D_QLC				(19)    //ssv7 qlc mst add--yingxing
#define FLASH_TYPE_INTEL_3D_QLC					(20)

// enum nand flash type excluding vendor
#define NAND_FLASH_TYPE_2D_TLC                  (0)
#define NAND_FLASH_TYPE_3D_MLC                  (1)
#define NAND_FLASH_TYPE_3D_TLC                  (2)
#define NAND_FLASH_TYPE_SLC                        (3)
#define NAND_FLASH_TYPE_3D_QLC					(4)


// enum ftl flash prog data type
#define PROG_DATA_TYPE_2D_TLC                   (0)
#define PROG_DATA_TYPE_3D_MLC                   (1)
#define PROG_DATA_TYPE_3D_TLC                   (2)
#define PROG_DATA_TYPE_3D_TLC_MICRON            (3)
#define PROG_DATA_TYPE_SLC                  (4)
#define PROG_DATA_TYPE_3D_QLC_MICRON			(5)
#define PROG_DATA_TYPE_3D_QLC                   (6)		//Reip Porting 3D-V7 QLC Add

/*
 * CONFIG FLASH TYPE
 */

#define IM_B16A_ID4     0xA1
#define IM_B17A_ID4     0xA6
#define IM_B27A_ID4     0xA2
#define IM_B27B_ID4     0xE6
#define IM_N18A_ID4     0xAA
#define IM_N28A_ID4     0x32
#define IM_140S_ID4     0xEA

#if (OPT_CATEGORY_FLASH == FLASH_B47R_TLC)
#define CONFIG_FLASH_TYPE				(FLASH_TYPE_MICRON_3D_TLC)
#define MicronFlashID4 					(IM_140S_ID4)
#elif (OPT_CATEGORY_FLASH == FLASH_N48R_QLC)//zerio n48r add
#define CONFIG_FLASH_TYPE               (FLASH_TYPE_MICRON_3D_QLC)
#define MicronFlashID4 					(IM_140S_ID4)
#elif (OPT_CATEGORY_FLASH == FLASH_B37R_TLC)
#define CONFIG_FLASH_TYPE               (FLASH_TYPE_MICRON_3D_TLC)
#define MicronFlashID4 					(IM_140S_ID4)
#elif (OPT_CATEGORY_FLASH == FLASH_HYNIX_V6_TLC)
#define CONFIG_FLASH_TYPE               (FLASH_TYPE_HYNIX_3D_TLC)
#define MicronFlashID4 					(0x00)
#elif (OPT_CATEGORY_FLASH == FLASH_HYNIX_V7_TLC)
#define CONFIG_FLASH_TYPE               (FLASH_TYPE_HYNIX_3D_TLC)
#define MicronFlashID4 					(0x00)
#elif (OPT_CATEGORY_FLASH == FLASH_SANDISK_BICS5_TLC)
#define CONFIG_FLASH_TYPE               (FLASH_TYPE_BICS5_3D_TLC)
#define MicronFlashID4 					(0x00)
#elif (OPT_CATEGORY_FLASH == FLASH_SANDISK_BICS6_TLC)
#define CONFIG_FLASH_TYPE               (FLASH_TYPE_BICS6_3D_TLC)
#define MicronFlashID4 					(0x00)
#elif (OPT_CATEGORY_FLASH == FLASH_HYNIX_V7_QLC)	//Reip Porting 3D-V7 QLC Add
#define CONFIG_FLASH_TYPE               (FLASH_TYPE_HYNIX_3D_QLC)
#define MicronFlashID4 					(0x00)
#elif (OPT_CATEGORY_FLASH == FLASH_HYNIX_V8_TLC)//Jeffrey Porting 3D-V8 TLC Add
#define CONFIG_FLASH_TYPE               (FLASH_TYPE_HYNIX_3D_TLC)
#define MicronFlashID4 					(0x00)
#elif (OPT_CATEGORY_FLASH == FLASH_HYNIX_V9_TLC)
#define CONFIG_FLASH_TYPE               (FLASH_TYPE_HYNIX_3D_TLC)
#define MicronFlashID4 					(0x00)
#elif (OPT_CATEGORY_FLASH == FLASH_SANDISK_BICS6_QLC)//zerio bics6 qlc add
#define CONFIG_FLASH_TYPE               (FLASH_TYPE_BICS6_3D_QLC)
#define MicronFlashID4 					(0x00)
#elif (OPT_CATEGORY_FLASH == FLASH_SANDISK_BICS8_TLC)//zerio BICS8 Add
#define CONFIG_FLASH_TYPE               (FLASH_TYPE_BICS8_3D_TLC)
#define MicronFlashID4 					(0x00)
#elif (OPT_CATEGORY_FLASH == FLASH_YMTC_TAS_TLC)
#define CONFIG_FLASH_TYPE               (FLASH_TYPE_YMTC_3D_TLC)
#define MicronFlashID4 					(0x00)
#elif (OPT_CATEGORY_FLASH == FLASH_YMTC_WTS_TLC)//zerio wts add
#define CONFIG_FLASH_TYPE               (FLASH_TYPE_YMTC_3D_TLC)
#define MicronFlashID4 					(0x00)
#elif (OPT_CATEGORY_FLASH == FLASH_YMTC_EMS_QLC)//ems add--karl
#define CONFIG_FLASH_TYPE               (FLASH_TYPE_YMTC_3D_QLC)
#define MicronFlashID4 					(0x00)
#elif (OPT_CATEGORY_FLASH == FLASH_SAMSUNG_V6_TLC || OPT_CATEGORY_FLASH == FLASH_SAMSUNG_V6P_TLC || OPT_CATEGORY_FLASH == FLASH_SAMSUNG_V5_TLC)
#define CONFIG_FLASH_TYPE               (FLASH_TYPE_SAMSUNG_3D_TLC)
#define MicronFlashID4 					(0x00)
#elif (OPT_CATEGORY_FLASH == FLASH_SAMSUNG_V7_TLC || OPT_CATEGORY_FLASH == FLASH_SAMSUNG_V8_TLC)//Samsung v7/v8 mst add--Reip
#define CONFIG_FLASH_TYPE               (FLASH_TYPE_SAMSUNG_3D_TLC)
#define MicronFlashID4 					(0x00)
#elif (OPT_CATEGORY_FLASH == FLASH_SAMSUNG_V7_QLC)//ssv7 qlc mst add--yingxing
#define CONFIG_FLASH_TYPE               (FLASH_TYPE_SAMSUNG_3D_QLC)
#define MicronFlashID4 					(0x00)
#elif (OPT_CATEGORY_FLASH == FLASH_INTEL_N38A_QLC)//ems add--karl
#define CONFIG_FLASH_TYPE               (FLASH_TYPE_INTEL_3D_QLC)
#define MicronFlashID4 					(0x00)
#else /* (OPT_CATEGORY_FLASH == FLASH_B47R_TLC) */
#error "OPT_CATEGORY_FLASH_IS_NONE"
#endif /* (OPT_CATEGORY_FLASH == FLASH_B47R_TLC) */

#define MICRON_140S						(MicronFlashID4 == IM_140S_ID4) //B47R,N48R


#if (CONFIG_FLASH_TYPE == FLASH_TYPE_TOSHIBA_2D_TLC)
#define CONFIG_NAND_FLASH_TYPE                  (NAND_FLASH_TYPE_2D_TLC)
#define CONFIG_PROG_DATA_TYPE                   (PROG_DATA_TYPE_2D_TLC)
#define FSP_PAGE_CNT                            (3)     // 1 for normal flash, 2 for TSB 3D MLC or M3D TLC, 3 for TSB 3D TLC
#define NAND_MAX_PLANE				(2)
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_TOSHIBA_3D_TLC)
#define CONFIG_NAND_FLASH_TYPE                  (NAND_FLASH_TYPE_3D_TLC)
#define CONFIG_PROG_DATA_TYPE                   (PROG_DATA_TYPE_3D_TLC)
#define FSP_PAGE_CNT                            (3)     // 1 for normal flash, 2 for TSB 3D MLC or M3D TLC, 3 for TSB 3D TLC
#define NAND_MAX_PLANE				(2)
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_2D_TLC)
#define CONFIG_NAND_FLASH_TYPE                  (NAND_FLASH_TYPE_2D_TLC)
#define CONFIG_PROG_DATA_TYPE                   (PROG_DATA_TYPE_2D_TLC)
#define FSP_PAGE_CNT                            (3)     // 1 for normal flash, 2 for TSB 3D MLC or M3D TLC, 3 for TSB 3D TLC
#define NAND_MAX_PLANE				(4) //NEED CHECK
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_TLC)
#define CONFIG_NAND_FLASH_TYPE                  (NAND_FLASH_TYPE_3D_TLC)
#define CONFIG_PROG_DATA_TYPE                   (PROG_DATA_TYPE_3D_TLC)
#define FSP_PAGE_CNT                            (3)     // 1 for normal flash, 2 for TSB 3D MLC or M3D TLC, 3 for TSB 3D TLC
#define NAND_MAX_PLANE				(4) //NEED CHECK
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_MLC)
#define CONFIG_NAND_FLASH_TYPE                  (NAND_FLASH_TYPE_3D_MLC)
#define CONFIG_PROG_DATA_TYPE                   (PROG_DATA_TYPE_3D_MLC)
#define FSP_PAGE_CNT                            (1)     // 1 for normal flash, 2 for TSB 3D MLC or M3D TLC, 3 for TSB 3D TLC
#define NAND_MAX_PLANE				(2) //NEED CHECK
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_TLC)
#define CONFIG_NAND_FLASH_TYPE                  (NAND_FLASH_TYPE_3D_TLC)
#define CONFIG_PROG_DATA_TYPE                   (PROG_DATA_TYPE_3D_TLC)//(PROG_DATA_TYPE_3D_TLC_MICRON)
#define FSP_PAGE_CNT                            ((MicronFlashID4 == IM_140S_ID4)? (3):(2))     // 1 for normal flash, 2 for TSB 3D MLC or M3D TLC, 3 for TSB 3D TLC
#define NAND_MAX_PLANE				(4)
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC)//zerio n48r add
#define CONFIG_NAND_FLASH_TYPE                  (NAND_FLASH_TYPE_3D_QLC)
#define CONFIG_PROG_DATA_TYPE                   (PROG_DATA_TYPE_3D_QLC_MICRON)
#define FSP_PAGE_CNT                            (4)     // 1 for normal flash, 2 for TSB 3D MLC or M3D TLC, 3 for TSB 3D TLC
#define NAND_MAX_PLANE				(4)
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_TOSHIBA_SLC)
#define CONFIG_NAND_FLASH_TYPE                  (NAND_FLASH_TYPE_SLC)
#define CONFIG_PROG_DATA_TYPE                   (PROG_DATA_TYPE_SLC)
#define FSP_PAGE_CNT                            (1)     // 1 for normal flash, 2 for TSB 3D MLC or M3D TLC, 3 for TSB 3D TLC
#define NAND_MAX_PLANE				(2) //NEED CHECK
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_BICS5_3D_TLC)
#define CONFIG_NAND_FLASH_TYPE                  (NAND_FLASH_TYPE_3D_TLC)
#define CONFIG_PROG_DATA_TYPE                   (PROG_DATA_TYPE_3D_TLC)
#define FSP_PAGE_CNT                            (3)     // 1 for normal flash, 2 for TSB 3D MLC or M3D TLC, 3 for TSB 3D TLC
#define NAND_MAX_PLANE				(2) //NEED CHECK
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_BICS6_3D_TLC)
#define CONFIG_NAND_FLASH_TYPE                  (NAND_FLASH_TYPE_3D_TLC)
#define CONFIG_PROG_DATA_TYPE                   (PROG_DATA_TYPE_3D_TLC)
#define FSP_PAGE_CNT                            (3)     // 1 for normal flash, 2 for TSB 3D MLC or M3D TLC, 3 for TSB 3D TLC
#define NAND_MAX_PLANE				(4) //NEED CHECK
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_BICS6_3D_QLC)//zerio bics6 qlc add
#define CONFIG_NAND_FLASH_TYPE                  (NAND_FLASH_TYPE_3D_QLC)
#define CONFIG_PROG_DATA_TYPE                   (PROG_DATA_TYPE_3D_QLC)
#define FSP_PAGE_CNT                            (4)     // 1 for normal flash, 2 for TSB 3D MLC or M3D TLC, 3 for TSB 3D TLC
#define NAND_MAX_PLANE				(4) //NEED CHECK
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_BICS8_3D_TLC)//zerio BICS8 Add
#define CONFIG_NAND_FLASH_TYPE                  (NAND_FLASH_TYPE_3D_TLC)
#define CONFIG_PROG_DATA_TYPE                   (PROG_DATA_TYPE_3D_TLC)
#define FSP_PAGE_CNT                            (3)     // 1 for normal flash, 2 for TSB 3D MLC or M3D TLC, 3 for TSB 3D TLC
#define NAND_MAX_PLANE				(4) //NEED CHECK
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_YMTC_3D_TLC)
#define CONFIG_NAND_FLASH_TYPE                  (NAND_FLASH_TYPE_3D_TLC)
#define CONFIG_PROG_DATA_TYPE                   (PROG_DATA_TYPE_3D_TLC)
#define FSP_PAGE_CNT                            (3)     // 1 for normal flash, 2 for TSB 3D MLC or M3D TLC, 3 for TSB 3D TLC
#define NAND_MAX_PLANE				(4) //NEED CHECK
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_YMTC_3D_QLC)//ems add--karl
#define CONFIG_NAND_FLASH_TYPE                  (NAND_FLASH_TYPE_3D_QLC)
#define CONFIG_PROG_DATA_TYPE                   (PROG_DATA_TYPE_3D_QLC_YMTC)
#define FSP_PAGE_CNT                            (4)     // 1 for normal flash, 2 for TSB 3D MLC or M3D TLC, 3 for TSB 3D TLC
#define NAND_MAX_PLANE				(4) //NEED CHECK
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_QLC)	//Reip Porting 3D-V7 QLC Addp
#define CONFIG_NAND_FLASH_TYPE                  (NAND_FLASH_TYPE_3D_QLC)
#define CONFIG_PROG_DATA_TYPE                   (PROG_DATA_TYPE_3D_QLC)
#define FSP_PAGE_CNT                            (4)     // 1 for normal flash, 2 for TSB 3D MLC or M3D TLC, 3 for TSB 3D TLC
#define NAND_MAX_PLANE				(4) //NEED CHECK
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_SAMSUNG_3D_TLC)
#define CONFIG_NAND_FLASH_TYPE                  (NAND_FLASH_TYPE_3D_TLC)
#define CONFIG_PROG_DATA_TYPE                   (PROG_DATA_TYPE_3D_TLC)
#define FSP_PAGE_CNT                            (3)     // 1 for normal flash, 2 for TSB 3D MLC or M3D TLC, 3 for TSB 3D TLC
#if (OPT_CATEGORY_FLASH == FLASH_SAMSUNG_V6_TLC || OPT_CATEGORY_FLASH == FLASH_SAMSUNG_V6P_TLC || OPT_CATEGORY_FLASH == FLASH_SAMSUNG_V5_TLC)
#define NAND_MAX_PLANE				(2) //NEED CHECK
#elif (OPT_CATEGORY_FLASH == FLASH_SAMSUNG_V7_TLC || OPT_CATEGORY_FLASH == FLASH_SAMSUNG_V8_TLC)//Samsung v8 mst add--Reip
#define NAND_MAX_PLANE				(4) //NEED CHECK
#endif
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_SAMSUNG_3D_QLC)//ssv7 qlc mst add--yingxing
#define CONFIG_NAND_FLASH_TYPE                  (NAND_FLASH_TYPE_3D_QLC)
#define CONFIG_PROG_DATA_TYPE                   (PROG_DATA_TYPE_3D_QLC)
#define FSP_PAGE_CNT                            (4)     // 1 for normal flash, 2 for TSB 3D MLC or M3D TLC, 3 for TSB 3D TLC
#define NAND_MAX_PLANE				(4) //NEED CHECK
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_INTEL_3D_QLC)//ems add--karl
#define CONFIG_NAND_FLASH_TYPE                  (NAND_FLASH_TYPE_3D_QLC)
#define CONFIG_PROG_DATA_TYPE                   (PROG_DATA_TYPE_3D_QLC)
#define FSP_PAGE_CNT                            (4)     // 1 for normal flash, 2 for TSB 3D MLC or M3D TLC, 3 for TSB 3D TLC
#define NAND_MAX_PLANE				(4) //NEED CHECK
#else
#error "CONFIG_FLASH_TYPE"
#endif

#if (PS5021_EN)
#define OPT_WRITABLE_COUNT (0x20)
#elif (S17_EN)
#define OPT_WRITABLE_COUNT (0x10)
#else
#define OPT_WRITABLE_COUNT (0xC)
#endif

#define PFA_EN						(TRUE)

#define READ_CMD_DMA_SYNC_CNT_EN            (TRUE)     // 瑕佽畝骞惧�plane灏卞彧鎵撳咕鍊媝lane鐨刢ommand, 涓嶆渻always鎵搈ulti-plane read


#define SNAP_READ_EN            (FALSE)    //鍠磾Snap read鐨勯枊闂�?
#if((MicronFlashID4 == IM_140S_ID4) && (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC))
#define IWL_EN		((PS5021_EN || PS5013_EN) && (OPT_CATEGORY_CUSTOMER == CUSTOMER_MICRON))
#define NEW_IWL_EN	(IWL_EN) //淇敼PS mask鐨勪綔娉�?鍙脯瑭﹂亷N48 IWLx4
#else/*((MicronFlashID4 == IM_140S_ID4) && (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC))*/
#define IWL_EN		(TRUE && (OPT_CATEGORY_CUSTOMER == CUSTOMER_MICRON))
#define NEW_IWL_EN 	(FALSE) //淇敼PS mask鐨勪綔娉�?鍙脯瑭﹂亷N48 IWLx4
#endif/*((MicronFlashID4 == IM_140S_ID4) && (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC))*/

#define IWL_SNAP_READ_ONLY_EN            (FALSE && IWL_EN)    //鍦↖WL_Flow涓� 鍙仛snap read

#define SUPPORT_OPT_3D_RANDOMIZER		(PS5021_EN || S17_EN)

#define SNAP_READ_USE_FPU_POLL		(PS5021_EN || S17_EN)

#define NDEP_READ_EN 	(FALSE)//(MicronFlashID4 == IM_140S_ID4) && (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC)

#define READ_DISTURB_PRDH_EN		((OPT_CATEGORY_CUSTOMER == CUSTOMER_MICRON) && (MicronFlashID4 == IM_140S_ID4) && (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC))

#define COP0_MT_RESOURCE_NOT_ENOUGH_WORKAROUND (PS5021_EN || S17_EN)

/*
 *  Note. In S17 E19 Backup P4K Flow, CMSG trigger MT and Send AXI to Modify P4K IRAM are parallel,
 *  Prevent FIP Execute MT Before AXI Finish,
 *  Program Flow Add FPU Delay Wait AXI Done (Over Maximum AXI Latency),
 *  Read Flow Add OPT Status notice FW wait AXI Done when Receive COP0 CQ.
 */
#define COP0_BACKUP_P4K_WORKAROUND	(PS5021_EN || S17_EN)

/*
 Note. PS5017/PS5021 Function FIP Can Disable FIP Phy when Execute WDMA FPU
 */
#define FIP_BYPASS_WDMA_EN	(PS5021_EN || S17_EN)

/*
 *  Note. E13 MT only has ultra dma disable bit, can not completely avoid executing this MT without ultra DMA,
 *  So disable ultra dma for all mt in a specific period by Andes. (ex: doing ReadVerify)
 */

#define FIP_SUPPORT_MT_VA (PS5021_EN || S17_EN)
#define VA_MAPPING_TABLE_EN	((MicronFlashID4 == IM_140S_ID4) && (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC))

#define VIRTUAL_ADDRESS_EN (MICRON_140S) // nand support or not
#define FIP_SUPPORT_MT_VIRTUAL_ADDRESS_EN ((PS5021_EN || S17_EN) && VIRTUAL_ADDRESS_EN) // IC enable VA mode or not
/*
 * Note. 140S flash in S17 & E21 HW, Virtual Address value cover the high byte address(die address) of row address
 * Workaround method:By die add auto poll sequence
 */
#define FIP_VIRTUAL_ADDRESS_WORKAROUND_EN ((PS5021_EN || S17_EN) && FIP_SUPPORT_MT_VIRTUAL_ADDRESS_EN) // fix VA mode for multi die workaround


/*Debug For IWL info DCCM Debug鏈夐枊鏅�?enable 鐩搁棞妾㈡煡*/
#define DEBUG_DCCCM_BY_QUEUE	(FALSE)
#define DEBUG_QUEUE_MAX	(8)
/* ---------------------------------------------------------------------------------------------------
 *   TEMP
 * ---------------------------------------------------------------------------------------------------*/



// Temporary, for debug
#define NVME_PROCESS_RESET_EVENT (0)

#define NVME_AUTO_FLUSH          (0)

#define VALLEY_CHECK_DEBUG_EN	 (TRUE)




#endif /* _CONF_H_ */

