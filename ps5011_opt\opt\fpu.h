/**************************************************************************/
/*                                                                        */
/*  Copyright (C) 2000-2016, Phison Electronics Corp., Inc.               */
/*  All rights reserved                                                   */
/*                                                                        */
/*  The content of this document is confidential and shall be applied     */
/*  subject to the terms and conditions of the license agreement and      */
/*  other applicable laws. Any unauthorized access, use or disclosure     */
/*  of this document is strictly prohibited and may be punishable         */
/*  under laws.                                                           */
/*                                                                        */
/*  fpu.h                                                                 */
/*                                                                        */
/*                                                                        */
/*                                                                        */
/**************************************************************************/

#ifndef _FPU_H_
#define _FPU_H_

#include "misc/types.h"
#include "conf.h"

#if (PS5021_EN)
#include "E21_fpu.h"
#elif (S17_EN)

#if (CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_TLC)
#include "fpu_hynix_tlc.h"
#define USE_S17_FPU 	(FALSE)
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_BICS5_3D_TLC || CONFIG_FLASH_TYPE == FLASH_TYPE_BICS6_3D_TLC)
#include "fpu_sandisk_tlc.h"
#define USE_S17_FPU 	(FALSE)
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_BICS6_3D_QLC)//zerio bics6 qlc add
#include "fpu_sandisk_qlc.h"
#define USE_S17_FPU 	(FALSE)
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_BICS8_3D_TLC)//zerio BICS8 Add
#include "fpu_sandisk_BICS8_tlc.h"
#define USE_S17_FPU 	(FALSE)
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_YMTC_3D_QLC)//ems add--karl
#include "fpu_ymtc_qlc.h"
#define USE_S17_FPU 	(FALSE)
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_YMTC_3D_TLC)
#include "fpu_ymtc_tlc.h"
#define USE_S17_FPU 	(FALSE)
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_QLC)	//Reip
#include "fpu_hynix_qlc.h"
#define USE_S17_FPU 	(FALSE)
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_TLC)
#include "fpu_micron_tlc.h"
#define USE_S17_FPU 	(FALSE)
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC)//zerio n48r add
#include "fpu_micron_qlc.h"
#define USE_S17_FPU 	(FALSE)
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_SAMSUNG_3D_TLC)
#include "fpu_samsung_tlc.h"
#define USE_S17_FPU 	(FALSE)
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_INTEL_3D_QLC)
#include "fpu_intel_qlc.h"
#define USE_S17_FPU 	(FALSE)
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_SAMSUNG_3D_QLC)//ssv7 qlc mst add--yingxing
#include "fpu_samsung_qlc.h"
#define USE_S17_FPU 	(FALSE)
#else
#include "S17_fpu.h"
#define USE_S17_FPU 	(TRUE)
#endif

#else /* (PS5021_EN) */
#include "E13_fpu.h"
#endif /* (PS5021_EN) */

#endif /* _FPU_H_ */
