/**************************************************************************/
/*                                                                        */
/*  Copyright (C) 2000-2016, Phison Electronics Corp., Inc.               */
/*  All rights reserved                                                   */
/*                                                                        */
/*  The content of this document is confidential and shall be applied     */
/*  subject to the terms and conditions of the license agreement and      */
/*  other applicable laws. Any unauthorized access, use or disclosure     */
/*  of this document is strictly prohibited and may be punishable         */
/*  under laws.                                                           */
/*                                                                        */
/*  opt_main.c                                                            */
/*                                                                        */
/*                                                                        */
/*                                                                        */
/**************************************************************************/

#define _OPT_MAIN_C_

#include "opt_main.h"
#include "opt_arch.h"
#include "opt_const.h"
#include "opt_hal.h"
#include "opt_mt_ext.h"
#include "opt_prog.h"
#include "opt_read.h"
#include "opt_erase.h"
#include "fpu.h"
#include "opt_debug.h"
#include "nds32_intrinsic.h"
#include "opt_api.h"
#include "conf.h"
#include "seedinit_tbl.h"
#include "opt_micron_read_disturb.h"

U32 gdummy_fpu_addr;
U8 gubOPTPreviousJobPtr[JOB_BACKUP_QUEUE_NUM] = {0};
U16 guwLastProgramPageInPlaneBank[OPEN_UNIT_TYPE_NUM][CONFIG_NUM_FIP_CE_TOTAL * 4 * CONFIG_NUM_DIE] = {{0}, {0}}; //GR, GCGR * PLANEBANK NUM
U8 gubLastMixPlaneBMP[CONFIG_NUM_FIP_CE_TOTAL] = {0};

inline BOOL opt_gather_1st_macro_wait_1st_plane(OPT_QUE_MGR_STRUCT_PTR que_mgr, OPT_JOB_STRUCT_PTR job);
inline BOOL opt_gather_1st_macro_wait_other_next_plane(OPT_QUE_MGR_STRUCT_PTR que_mgr, OPT_JOB_STRUCT_PTR job);
inline BOOL opt_gather_next_macro_wait_1st_plane(OPT_QUE_MGR_STRUCT_PTR que_mgr, OPT_JOB_STRUCT_PTR job, OPT_JOB_STRUCT_PTR first_job);
inline BOOL opt_gather_next_macro_wait_other_next_plane(OPT_QUE_MGR_STRUCT_PTR que_mgr, OPT_JOB_STRUCT_PTR job, OPT_JOB_STRUCT_PTR first_job);
#if (!MICRON_140S)
WordLineStatusBypassResult_t OptMicronCheckSwitchWordLineStatusBypass(OPT_JOB_STRUCT_PTR job);
#endif
inline void opt_pop_job_handle(OPT_QUE_MGR_STRUCT_PTR que_mgr);
inline void opt_push_job_handle(OPT_QUE_MGR_STRUCT_PTR que_mgr);
#if(MICRON_140S)
inline void OptGetLastProgramPage(OPT_JOB_STRUCT_PTR job);
#endif

__attribute__((optimize("Os")))
BOOL OPTOtherCmdStopFormMTCheck(U8 ubCnt)
{
	if (_chk_other_cmd_mtq_free_cnt(gOptStruct.cur_que, ubCnt)) {
		if ((MT_TRIGGER_CNT_EMPTY != OPTQB[OPTQB_PROC_Q_MTQ_TRIG_CNT]) || (_chk_reserved_mtq_free_cnt(gOptStruct.cur_que, ubCnt))) {
			return TRUE;
		}
	}
	return FALSE;
}

void OPTErrorAssert(U32 ulErrorCode)
{
	if (DEFAULT_ASSERT_CODE == cpu_comm->opt_assert_dccm) {
		_setvalue( cpu_comm->opt_assert_dccm, ulErrorCode);
		_setvalue( opt_assert->ptn, ulErrorCode);
	}
	if (cpu_comm->ubGeneralLevelAssertEn) {
		while (ulErrorCode);
	}
}

void OPTCriticalErrorAssert(U32 ulErrorCode)
{
	_setvalue( cpu_comm->opt_assert_dccm, ulErrorCode);
	_setvalue( opt_assert->ptn, ulErrorCode);
	while (ulErrorCode);
}
__attribute__((optimize("Os")))
void opt_init(void)
{
	U8 ubIdx;

	// MT num
	OPTQB[R8_OPT_MTP_NOR_NUM] = MTP_NOR_MT_CNT - 1;
	OPTQB[R8_OPT_MTP_QOS_NUM] = MTP_QOS_MT_CNT - 1;
	OPTQB[R8_OPT_MTP_ERR_NUM] = MTP_ERR_MT_CNT - 1;


	// MT base (mtq)
	OPTQW[R16_OPT_MT_BASE] = OPT_D_MTP_NOR_BASE;
	OPTQW[R16_OPT_QOS_MT_BASE] = OPT_D_MTP_QOS_BASE;
	OPTQW[R16_OPT_ERR_MT_BASE] = OPT_D_MTP_ERR_BASE;


	// trigger data base (mtd)
	OPTQW[R16_OPT_TRIGGERDATA_BASE] = OPT_D_MTP_TRIGGERDATA_NOR_BASE;
	OPTQW[R16_OPT_QOS_TRIGGERDATA_BASE] = OPT_D_MTP_TRIGGERDATA_QOS_BASE;
	OPTQW[R16_OPT_ERR_TRIGGERDATA_BASE] = OPT_D_MTP_TRIGGERDATA_ERR_BASE;

	// linked list base
	OPTQW[R16_OPT_MTP_LL_BASE] = OPT_D_MTP_LL_NOR_BASE;
	OPTQW[R16_OPT_MTP_LL_QOS_BASE] = OPT_D_MTP_LL_QOS_BASE;

	// resource base
	OPTQW[R16_OPT_MTP_RSC_NOR_BASE] = OPT_D_MTP_RSC_NOR_BASE;
	OPTQW[R16_OPT_MTP_RSC_QOS_BASE] = OPT_D_MTP_RSC_QOS_BASE;
	OPTQW[R16_OPT_MTP_RSC_ERR_BASE] = OPT_D_MTP_RSC_ERR_BASE;


	OPTQB[R8_OPT_MRSC_INIT] = SET_MRSC_INIT;
	while (OPTQB[R8_OPT_MRSC_INIT]);

	OPTQB[R8_OPT_MRSC_ALCT_RUN] = (SET_MRSC_ALCT_NOR_RUN | SET_MRSC_ALCT_QOS_RUN);

	OPTQB[R8_OPT_ENTRY_MASK_EN] = OPT_SUPPORT_ENTRY_MASK;

	if (IOR_EN) {
		OPTQB[R8_OPT_PS_SGL_MTL_DIE_MODE] = DIRECT_PUSH_INTO_SLC_QUEUE_MODE;
	}
	else {
		OPTQB[R8_OPT_PS_SGL_MTL_DIE_MODE] = SINGLE_DIE_MODE; // for plane search
	}
#if (NEW_IWL_EN)
	OPTQB[R8_OPT_PS_FILTER_EN] &= (~(PS_FILTER_BLOCK_BIT | PS_FILTER_PAGE_BIT));
#else /* (NEW_IWL_EN) */
	OPTQB[R8_OPT_PS_FILTER_EN] &= (~PS_FILTER_BLOCK_BIT); //JIRA-5011 & JIRA-6075, multi plane read for RUT replace physical block.
#endif /* (NEW_IWL_EN) */


	if (READ_DISTURB_PRDH_EN) { // for gen Uniform Random Number (URN)
		OPTQL[R32_OPT_REG_1] |= TIMER_EN;
	}

	/* force compiler to link fpu */
	gdummy_fpu_addr = (U32)(&gFpuEntryList);

	/*
	 * some setting need to be set by fw
	 */
	gOptStruct.disable_cache_read = cpu_comm->disable_cache_read;
	gOptStruct.disable_cache_prog = cpu_comm->disable_cache_prog;

	OPT_CRITICAL_ASSERT(ASSERT_BYTE_ALIGN_ERROR | 0x0, sizeof(OPT_JOB_STRUCT) & 0x07);

	OPT_CLEAR_DCCM_SETUP_FOR_JOB(sizeof(OPT_JOB_STRUCT) >> 3);

	for (ubIdx = 0; ubIdx < CONFIG_NUM_FIP_CE_TOTAL; ubIdx++) {
		gOptStruct.que_mgr[ubIdx].head_job = 0;
		gOptStruct.que_mgr[ubIdx].tail_job = 0;

		OPT_CRITICAL_ASSERT(ASSERT_BYTE_ALIGN_ERROR | 0x1, (((U32) & (gOptStruct.que_mgr[ubIdx].job_handle[0])) | ((U32) & (gOptStruct.que_mgr[ubIdx].job_handle[1]))) & 0x07);

		OPT_CLEAR_DCCM_FOR_JOB((U32)(&gOptStruct.que_mgr[ubIdx].job_handle[0]));
		OPT_CLEAR_DCCM_FOR_JOB((U32)(&gOptStruct.que_mgr[ubIdx].job_handle[1]));
	}

	gOptStruct.run_macro_cmd[OPT_MACRO_CMD_ERASE] = opt_macro_cmd_erase;

	gOptStruct.run_macro_cmd[OPT_MACRO_CMD_NORMAL_READ] = opt_macro_cmd_normal_read;
	gOptStruct.run_macro_cmd[OPT_MACRO_CMD_CACHE_READ_START] = opt_macro_cmd_cache_read_start;
	gOptStruct.run_macro_cmd[OPT_MACRO_CMD_CACHE_READ_END] = opt_macro_cmd_cache_read_end;
	gOptStruct.run_macro_cmd[OPT_MACRO_CMD_CACHE_READ] = opt_macro_cmd_cache_read;

	gOptStruct.run_macro_cmd[OPT_MACRO_CMD_D2_NORMAL_PROG] = opt_macro_cmd_d2_normal_prog;
	gOptStruct.run_macro_cmd[OPT_MACRO_CMD_D2_CACHE_PROG_START] = opt_macro_cmd_d2_cache_prog_start;
	gOptStruct.run_macro_cmd[OPT_MACRO_CMD_D2_CACHE_PROG_END] = opt_macro_cmd_d2_cache_prog_end;
	gOptStruct.run_macro_cmd[OPT_MACRO_CMD_D2_CACHE_PROG] = opt_macro_cmd_d2_cache_prog;

	gOptStruct.run_macro_cmd[OPT_MACRO_CMD_VENDER_CMD] = opt_macro_cmd_vender_cmd;
	gOptStruct.run_macro_cmd[OPT_MACRO_CMD_DUMMY_CMD] = opt_macro_cmd_dummy_cmd;
	gOptStruct.run_macro_cmd[OPT_MACRO_CMD_GET_TEMPERATURE_CMD] = opt_macro_cmd_get_temperature_cmd;
	gOptStruct.run_macro_cmd[OPT_MACRO_CMD_SYNC_OPEN_UNIT_CMD] = opt_macro_cmd_sync_open_unit_cmd;
#if (MicronFlashID4 == IM_N28A_ID4)
	gOptStruct.run_macro_cmd[OPT_MACRO_CMD_VALLEY_CHECK_CMD] = opt_macro_cmd_valley_check_cmd;
#endif /* (MicronFlashID4 == IM_N28A_ID4) */
	gOptStruct.run_macro_cmd[OPT_MACRO_CMD_IOR_NO_DMA] = opt_macro_cmd_ior_no_dma_cmd;

	// pca : block page die CE CH LMU plane entry

	for (ubIdx = 0; ubIdx < COP0_PCA_RULE_NUM; ubIdx++) {
		//page
		gOptStruct.ulPCAMaskPage[ubIdx] = (((1 << COP0_PAGE_LENS(ubIdx)) - 1) << COP0_PAGE_START_POINT(ubIdx));
		//lmu
		gOptStruct.ulPCAMaskLMU[ubIdx] = (((1 << COP0_LMU_LENS(ubIdx)) - 1) << COP0_LMU_START_POINT(ubIdx));
		//die
		gOptStruct.ulPCAMaskDie[ubIdx] = (((1 << COP0_DIE_LENS(ubIdx)) - 1) << COP0_DIE_START_POINT(ubIdx));
		// die IL
		gOptStruct.ulPCAMaskDieInterleave[ubIdx] = (((1 << COP0_DIE_IL_LENS(ubIdx)) - 1) << COP0_DIE_IL_START_POINT(ubIdx));
		//check multiplane mask =>rmp pca is same die il/page/lmu
		gOptStruct.ulPCAMaskMultiPlane[ubIdx] = gOptStruct.ulPCAMaskPage[ubIdx] | gOptStruct.ulPCAMaskDieInterleave[ubIdx] | gOptStruct.ulPCAMaskLMU[ubIdx];
	}

	//block
	gOptStruct.ulPCAMaskBlock = (((1 << COP0_BLOCK_LENS(COP0_PCA_RULE_0)) - 1) << COP0_BLOCK_START_POINT(COP0_PCA_RULE_0));

	gulSwitchQueue_BM = 0;
	gubIWLFlag = FALSE;
	gubMaxPlane = BIT(COP0_PLANE_LENS(COP0_PCA_RULE_0));
	gubEntryPerPlane = BIT(COP0_PLANE_START_POINT(COP0_PCA_RULE_0));
	gubPlaneMask = gubMaxPlane - 1;
	gubMaxCE = BIT(COP0_BANK_LENS(COP0_PCA_RULE_0));
	gubMaxChannel = BIT(COP0_CHANNEL_LENS(COP0_PCA_RULE_0));
	gubMaxDie = BIT(COP0_DIE_LENS(COP0_PCA_RULE_0));

	gubProgramParityQueue = (gubMaxCE * gubMaxChannel) - 1;
#if (OPT_CATEGORY_CUSTOMER == CUSTOMER_MICRON)
	if (cpu_comm->micron_info.ubLUNForMoreUnitEn)
#else /* (OPT_CATEGORY_CUSTOMER == CUSTOMER_MICRON) */
	if (cpu_comm->ubLUNForMoreUnitEn)
#endif
	{
		gubTotalPlaneBank = gubMaxPlane * gubMaxCE * gubMaxChannel;
		gOptStruct.ulPCAMaskFWUnit = gOptStruct.ulPCAMaskBlock | gOptStruct.ulPCAMaskDie[COP0_PCA_RULE_0];
		gOptStruct.ubPCAMaskFWUnitShift = COP0_DIE_START_POINT(COP0_PCA_RULE_0);
	}
	else {
		gubTotalPlaneBank = gubMaxPlane * gubMaxDie * gubMaxCE * gubMaxChannel;
		gOptStruct.ulPCAMaskFWUnit = gOptStruct.ulPCAMaskBlock;
		gOptStruct.ubPCAMaskFWUnitShift = COP0_BLOCK_START_POINT(COP0_PCA_RULE_0);
	}
	gubUnreachLastPagePlaneNum[OPEN_GR_UNIT] = 0;
	gubUnreachLastPagePlaneNum[OPEN_GCGR_UNIT] = 0;
	gubUnreachLastPagePlaneNum[OPEN_SPOR_UNIT] = 0;
#if(OPT_CATEGORY_CUSTOMER == CUSTOMER_MICRON)
	cpu_comm->ulProgLUXTQueueBMP = 0;
	cpu_comm->micron_info.Flag.ubAll = 0;
	cpu_comm->ulWordLineBypassStateBMP = 0;
#if (MicronFlashID4 == IM_N28A_ID4)
	cpu_comm->ulWordLineBypasseXtraPageOverrideAndPrereadBMP = 0;
#endif /* (MicronFlashID4 == IM_N28A_ID4) */
	for (ubIdx = 0; ubIdx < OPEN_UNIT_TYPE_NUM; ubIdx ++) {
		cpu_comm->micron_info.uwOpenUnit[ubIdx] = INVALID_UNIT;
		cpu_comm->micron_info.uwNextOpenUnit[ubIdx] = INVALID_UNIT;
	}
#endif/* (OPT_CATEGORY_CUSTOMER == CUSTOMER_MICRON) */
}


inline void handle_MTpool_stall(U8 ubMTPoolResourceEmpty)
{
	//U8 ubQueueIndex;	// 之後改回來U8
	U32 ubQueueIndex;
	OPT_JOB_STRUCT_PTR job;
	OPT_QUE_MGR_STRUCT_PTR que_mgr;

	que_mgr = gOptStruct.que_mgr;

	if (cpu_comm->uoARM_Stop_FormMT_Req_BM) {
		for (ubQueueIndex = 0; ubQueueIndex < CONFIG_NUM_FIP_CE_TOTAL; ubQueueIndex++) {
			if (hal_optq_check_stop_formMT_req(ubQueueIndex)) {
				hal_optq_StopFormMT_ClearStopFormMTReq(ubQueueIndex);
			}
		}
	}

	if (cpu_comm->uoARM_Stall_Req_BM) {
		for (ubQueueIndex = 0; ubQueueIndex < CONFIG_NUM_FIP_CE_TOTAL; ubQueueIndex++) {
			if (hal_optq_check_ARM_stall_MTpool_req(ubQueueIndex)) {
				job = &que_mgr[ubQueueIndex].job_handle[que_mgr[ubQueueIndex].head_job];
				if (0 == job->cmd_cnt) {
					if (M_GET_MTQ_TRIG_CNT(ubQueueIndex)) {
						// opt queue command sequence告一段落但仍有trig cnt, Andes可能會選不到OPT queue, 需交由ARM來做stall MTpool
						hal_optq_Andes_stop_formMT(ubQueueIndex);
					}
					else {	// opt queue command sequence告一段落且trig cnt都已完成
						//OPTQL[R32_OPT_MTP_STALL] |= (BIT0 << ubQueueIndex);		// Andes stall MT pool
						hal_optq_Andes_stop_formMT(ubQueueIndex);
						hal_optq_Andes_req_directly_stall_MTpool(ubQueueIndex);
					}
					hal_optq_clear_ARM_stall_MTpool_req(ubQueueIndex);
				}
				else {
					if ((OPTQL[R32_OPT_MTQ_FULL] & (BIT0 << ubQueueIndex)) || ubMTPoolResourceEmpty) {	// rig cnt full, Andes也會選不到OPT queue, 需交由ARM來做stall MTpool,    TRIG_CNT >= (FULL_LIMIT (6) + 1)
						hal_optq_StopFormMT_ClearStallReq(ubQueueIndex);
					}
					else {
						if (hal_optq_check_SwitchQueueCnt(ubQueueIndex)) {	// 2nd進入, 代表1st沒辦法做stall request
							hal_optq_StopFormMT_ClearStallReq(ubQueueIndex);
						}
						// else, Andes會在cmd sequence結尾的MT拉mtd->dw0_dat.bits.MTP_stall
					}
				}
			}
		}
	}
}

/*
 * wait cmd coming or process banking or process stall
 */
inline void wait_any_que_valid(void)
{
	while (1) {
		handle_MTpool_stall(FALSE);

		/* check if any que valid */
		__ASM_VOLATILE_MEMORY__();
		OPTQB[OPTQB_STOP_PROC_Q] = STOP_PROC_Q; // the switch queue arbiter does not select PROC_Q, PROC_QOS)
		if (OPTQB[OPTQB_SWITCHQ_ARBITER_VALID] != 0) {
			break;
		}
	}
	__ASM_VOLATILE_MEMORY__();
}

/*
 * switch to a banking que or a valid que
 */
inline U32 switch_cur_que(void)
{
	U8 ubCurrentQueue;
	/* get queue index */
	ubCurrentQueue = OPTQB[OPTQB_SWITCHQ_ARBITER_RESULT];
	/* switch to queue by queue index */
	__ASM_VOLATILE_MEMORY__();
	OPTQB[OPTQB_SWITCHQ] = ubCurrentQueue;
	__ASM_VOLATILE_MEMORY__();
	return ubCurrentQueue;
}

int opt_main(void)
{
	OPT_QUE_MGR_STRUCT_PTR que_mgr;
	U32 cur_que;

	/*
	 * Init
	 */
	cpu_comm = (COP0_COMM_INFO_STRUCT_PTR) ( OPT_DCCM_COMM_BASE);
	read_info = (COP0_READ_INFO_STRUCT_PTR) (OPT_DCCM_READ_INFO_BASE);
	opt_assert = (COP0_ASSERT_STRUCT_PTR) ( OPT_S_ASSERT_BASE);
	mtq = (FPL_MTQ_STRUCT_PTR) (&OPTCMB[OPTCMB_MT_BYTE0]);
	mtd = (COP0_MTD_STRUCT_PTR) (&OPTCML[OPTCM_TRIG_DATA_DW0]);
	gpubReadCntLowByte = (U8 *) ( OPT_DCCM_READ_DISTURB_BASE);
	gpubReadCntHighByte = (ReadCntHighByte_t *) ( OPT_DCCM_READ_DISTURB_BASE + OPT_DCCM_READ_CNT_LOW_BYTE_LENS);
	gpReadDisturbThreshold = (READ_DSITURB_THRESHOLD_STRUCT_PTR) (OPT_DCCM_READ_DISTURB_THRESHOLD_BASE);
#if (READ_DISTURB_PRDH_EN)
	gpMicronReadCnt = (COP0_MICRON_READ_CNT_STRUCT_PTR)(OPT_DCCM_MICRON_READ_CNT_BASE);
	gpMicronScanInfo = (COP0_MICRON_SCAN_INFO_STRUCT_PTR)(OPT_DCCM_MICRON_SCAN_INFO_BASE);
	gpMicronPatchInfo = (COP0_MICRON_PATCH_INFO_STRUCT_PTR)(OPT_DCCM_MICRON_RESTORE_FWPCA_BASE);
#endif /* (READ_DISTURB_PRDH_EN) */
#if (SUPPORT_OPT_3D_RANDOMIZER)
	gpulSeedInitTable = ((U32 *)(OPT_DCCM_SEED_INIT_TABLE_BASE));
#endif /* (SUPPORT_OPT_3D_RANDOMIZER) */
#if ((FIP_SUPPORT_MT_VA) && (VA_MAPPING_TABLE_EN))
	gpubVirtualAddressValue = ((U8 *)(OPT_DCCM_BIN_VALUE_TABLE_BASE));
#endif /* (FIP_SUPPORT_MT_VA) && (VA_MAPPING_TABLE_EN)*/
	memset(&gOptStruct, 0, sizeof(OPT_STRUCT));
	//memset(cpu_comm, 0, sizeof(COP0_COMM_INFO_STRUCT));
	memset(read_info, 0xFF, sizeof(COP0_READ_INFO_STRUCT));
	memset(opt_assert, 0, sizeof(COP0_ASSERT_STRUCT));
	memset(mtq, 0, sizeof(FPL_MTQ_STRUCT));
	memset(mtd, 0, sizeof(COP0_MTD_STRUCT));
	memset(&gOPTRSMgr, 0, sizeof(OPT_RS_MGR_STRUCT));
	memset(&gOPTPreviousJob, 0, JOB_BACKUP_QUEUE_NUM * JOB_BACKUP_LENGTH * sizeof(OPT_JOB_STRUCT));
	memset((void *)OPT_DCCM_READ_DISTURB_BASE, 0, OPT_DCCM_READ_DISTURB_LENS);
	__ASM_VOLATILE_MEMORY__();
	opt_init();
	que_mgr = gOptStruct.que_mgr;

	for (cur_que = 0; cur_que < CONFIG_NUM_FIP_CE_TOTAL; cur_que ++) {
		OPTQB[OPTQB_WRITABLE_COUNT_0 + cur_que] = OPT_WRITABLE_COUNT;   // 為了推很多read cmd看plane search result
	}

	opt_assert->ptn = DEFAULT_ASSERT_CODE;
#if (S17_EN)
	cpu_comm->ulVersion1 = M_GET_FW_VERSION_1;
	cpu_comm->ulVersion2 = M_GET_FW_VERSION_2;
#endif /* (S17_EN) */
	cpu_comm->opt_assert_dccm = DEFAULT_ASSERT_CODE;

	gubIWLFlag = IWL_EN;

	/* main loop */
	while (1) {
		/* wait cmd coming */
		wait_any_que_valid();

		/* switch to a banking que or a valid que */
		cur_que = switch_cur_que();

		if (hal_optq_check_ARM_stall_MTpool_req(cur_que)) {
			gulSwitchQueue_BM |= (BIT0 << cur_que);	// 1st進入
		}

		gOptStruct.disable_cache_read = cpu_comm->disable_cache_read;
		gOptStruct.disable_cache_prog = cpu_comm->disable_cache_prog;
		/* update to gOptStruct structure */
		gOptStruct.cur_que = cur_que;

		// restore lookup ptr
		//OPTQB[OPTQB_LOOKUP_PTR_UPDATE] = que_mgr[cur_que].lookup_ptr;	// COP0 每個opt queue都會保留其lookup ptr
		do {
			__ASM_VOLATILE_MEMORY__();
			opt_parser(&que_mgr[cur_que]);

			__ASM_VOLATILE_MEMORY__();
			M_OPT_SET_WRITABLE_COUNT(cur_que);
		} while (GET_PROG_LUXT_QUEUE(cur_que) || GET_PREREAD_DISABLE_STATE_QUEUE(cur_que));
		// bakcup lookup ptr
		//que_mgr[cur_que].lookup_ptr = OPTQB[OPTQB_LOOKUP_PTR_UPDATE];	// COP0 每個opt queue都會保留其lookup ptr
	} /* main loop */

	return 0;
}

void opt_parser(OPT_QUE_MGR_STRUCT_PTR que_mgr)
{
	OPT_JOB_STRUCT_PTR job;
	U32 state;
	BOOL switch_q;

	if (hal_optq_check_Andes_stop_formMT(gOptStruct.cur_que)) {   // 請求ARM stall MT pool的queue不得再form MT, 所以直接切走換別的queue
		return;
	}

	gubClearARMStallReq = FALSE;
	state = que_mgr->state;
	_WRITE_OPT_DCCM_PATH_DEBUG(SWITCHQ_OP(gOptStruct.cur_que));

	if (OPT_PARSER_STATE_IDLE == state) {
		state = OPT_PARSER_STATE_GATHER;
	}

	switch_q = FALSE;
	while (1) {
		_WRITE_OPT_DCCM_PATH_DEBUG(PARSER_STATE(state));
		if (OPT_PARSER_STATE_GATHER == state) {
			/*
			 * gather for 1st macro or next macro
			 */
			if (que_mgr->head_job == que_mgr->tail_job) {
				/*
				 * gather for 1st macro
				 */
				state = opt_gather_1st_macro(que_mgr);
			}
			else {
				/*
				 * gather for next macro
				 */
				state = opt_gather_next_macro(que_mgr);
			}
			OPT_CRITICAL_ASSERT(ASSERT_FLOW_ERROR | 0x0, OPT_PARSER_STATE_UNDEFINE == state);
		}
		else if (OPT_PARSER_STATE_RUN_MACRO == state) {
			/*
			 * run a macro (for tail_job)
			 */
			job = &que_mgr->job_handle[que_mgr->tail_job];

			if (OPT_MACRO_CMD_STATE_SETUP == job->macro_state) {
				opt_set_macro_cmd(que_mgr);
			}

			if (gOptStruct.run_macro_cmd[job->macro_cmd](que_mgr, job)) {
				/*
				 * finished running a macro cmd
				 */
				opt_push_job_handle(que_mgr);
				if (OPT_MACRO_CMD_STATE_DONE == que_mgr->job_handle[que_mgr->head_job].macro_state) {
					/*
					 * head_job closed
					 */
					job = &que_mgr->job_handle[que_mgr->head_job];
					opt_pop_job_handle(que_mgr);

					if (OPT_BACKUP_PREVIOUS_JOB_DEBUG) {
						memcpy(&gOPTPreviousJob[gOptStruct.cur_que][gubOPTPreviousJobPtr[gOptStruct.cur_que]++], job, sizeof(OPT_JOB_STRUCT));
						if (JOB_BACKUP_LENGTH == gubOPTPreviousJobPtr[gOptStruct.cur_que]) {
							gubOPTPreviousJobPtr[gOptStruct.cur_que] = 0;
						}
					}

					// clear this structure for next time use this struct
					OPT_CLEAR_DCCM_FOR_JOB((U32)job);
					state = OPT_PARSER_STATE_IDLE;
				}
				else {
					/*
					 * need to gather next macro
					 */
					state = OPT_PARSER_STATE_IDLE;
				}
			}
			else {
				/*
				 * check if resource insufficient caused by stall, 以前project會call opt_parser_check_suspend_by_stall( )
				 */
				switch_q = TRUE;
			}
		}
		else { /* if (OPT_PARSER_STATE_CLOSE_MACRO == state) */
			/*
			 * close a macro (for head_job)
			 */
			job = &que_mgr->job_handle[que_mgr->head_job];

			OPT_CRITICAL_ASSERT(ASSERT_FLOW_ERROR | 0x1, OPT_MACRO_CMD_UNDEFINE == job->macro_close_cmd);
			_WRITE_OPT_DCCM_PATH_DEBUG(SET_CMD_OP(job->macro_close_cmd));
			if (gOptStruct.run_macro_cmd[job->macro_close_cmd](que_mgr, job)) {
				/*
				 * closed this job
				 */
				opt_pop_job_handle(que_mgr);

				/*
				 * need to gather next macro
				 */
				state = OPT_PARSER_STATE_IDLE;

				if (OPT_BACKUP_PREVIOUS_JOB_DEBUG) {
					memcpy(&gOPTPreviousJob[gOptStruct.cur_que][gubOPTPreviousJobPtr[gOptStruct.cur_que]++], job, sizeof(OPT_JOB_STRUCT));
					if (JOB_BACKUP_LENGTH == gubOPTPreviousJobPtr[gOptStruct.cur_que]) {
						gubOPTPreviousJobPtr[gOptStruct.cur_que] = 0;
					}
				}
				// clear this structure for next time use this struct
				OPT_CLEAR_DCCM_FOR_JOB((U32)job);
			}
			else {
				/*
				 * check if resource insufficient caused by stall, 以前project會call opt_parser_check_suspend_by_stall( )
				 */
				switch_q = TRUE;
			}
		}

		if (OPT_PARSER_STATE_IDLE == state) {
			/*
			 * time-out, banking, stall
			 */
			switch_q = TRUE;
		}

		if (switch_q) {
			break;
		}
	}

	que_mgr->state = state;
	_WRITE_OPT_DCCM_PATH_DEBUG(PARSER_STATE(que_mgr->state));
}

inline U32 opt_gather_1st_macro(OPT_QUE_MGR_STRUCT_PTR que_mgr)
{
	OPT_JOB_STRUCT_PTR job;
	BOOL finish;
	U8 idx_cmd_type;
	U8 idx_plane_type;
	U32 q_state;

	job = &que_mgr->job_handle[que_mgr->tail_job];

	while (1) {
		if (OPT_GATHER_STATE_WAIT_1ST_PLANE == job->gather_state) {
			finish = opt_gather_1st_macro_wait_1st_plane(que_mgr, job);
		}
		else { /* if (OPT_GATHER_STATE_WAIT_OTHER_NEXT_PLANE == job->gather_state) */
			finish = opt_gather_1st_macro_wait_other_next_plane(que_mgr, job);
		}

		if (finish) {
			break;
		}
	}

	idx_cmd_type = (COP0_JOB_CMD_READ == job->cmd) ? 1 : 0; // other/read cmd
	idx_plane_type = (OPT_GATHER_STATE_WAIT_1ST_PLANE == job->gather_state) ? 0 : 1; // 1st/next plane
	q_state = gather_status_to_parser_state[job->gather_status][idx_cmd_type][GATHER_1ST_MACRO][idx_plane_type];

	return q_state;
}

inline U32 opt_gather_next_macro(OPT_QUE_MGR_STRUCT_PTR que_mgr)
{
	OPT_JOB_STRUCT_PTR job;
	OPT_JOB_STRUCT_PTR first_job;
	BOOL finish;
	U8 idx_cmd_type;
	U8 idx_plane_type;
	U32 q_state;

	first_job = &que_mgr->job_handle[que_mgr->head_job];
	job = &que_mgr->job_handle[que_mgr->tail_job];

	while (1) {
		if (OPT_GATHER_STATE_WAIT_1ST_PLANE == job->gather_state) {
			finish = opt_gather_next_macro_wait_1st_plane(que_mgr, job, first_job);
		}
		else { /* if (OPT_GATHER_STATE_WAIT_OTHER_NEXT_PLANE == job->gather_state) */
			finish = opt_gather_next_macro_wait_other_next_plane(que_mgr, job, first_job);
		}

		if (finish) {
			break;
		}
	}

	idx_cmd_type = (COP0_JOB_CMD_READ == job->cmd) ? 1 : 0; // other/read cmd
	idx_plane_type = (OPT_GATHER_STATE_WAIT_1ST_PLANE == job->gather_state) ? 0 : 1; // 1st/next plane
	q_state = gather_status_to_parser_state[job->gather_status][idx_cmd_type][GATHER_NEXT_MACRO][idx_plane_type];

	return q_state;
}

inline BOOL opt_gather_1st_macro_wait_1st_plane(OPT_QUE_MGR_STRUCT_PTR que_mgr, OPT_JOB_STRUCT_PTR job)
{
	BOOL finish;
	U32 pca, rmp_pca;
	U8 cmd;
	U8 slc_mode;
	U8 plane;
	U8 lmu;
	U8 die;
	U8 state;
	U8 status;
	U8 lookup_state;
	U8 ubQueueCnt;
	U8 ubPlaneIndex;
	U8 ubOPTIndex;
	U8 ubMinGroupID;
	U8 ubMinOPTIdx = OPT_INVALID_OPT_INDEX;
	U8 ubTimeout = FALSE;
	U64 uoPlaneSearchResult = OPT_PS_INVALID_RESULT;

	state = job->gather_state;
	status = job->gather_status;
	finish = TRUE;

	/*
	 * wait 1st plane
	 */
	__ASM_VOLATILE_MEMORY__();

	/*
	 * gather in opt_gather_1st_macro() 1st plane
	 */
	lookup_state = OPTQB[OPTQB_LOOKUP_Q_VALID];
	if (lookup_state & OPTQB_ELEMENT_VALID) {
		hal_optq_wait_lookup_q_valid();
		if (IOR_FLOW_DEBUG || PS_READ_LOOKUP_PTR_CHECK_DEBUG) {
			OPT_CRITICAL_ASSERT(ASSERT_LOOKUP_PTR_ERROR | 0x0, OPTQB[OPTQB_LOOKUP_PTR_UPDATE]);
		}
		_WRITE_OPT_DCCM_PATH_DEBUG(PARSER_1st_OP(OPT_GATHER_STATE_WAIT_1ST_PLANE));
		cmd = OPTQ_PROC_Q_LOOKUP_CMD;
		pca = OPTQL[OPTQ_PROC_Q_LOOKUP_PCA];
		rmp_pca = OPTQL[OPTQ_PROC_Q_LOOKUP_RMP_PCA];
		slc_mode = OPTQ_PROC_Q_LOOKUP_SLC_MODE;
		plane = OPT_LOOKUP_PLANE_ADDR;
		lmu = hal_loopup_page_type();
		die = OPT_LOOKUP_DIE_ADDR;
		__ASM_VOLATILE_MEMORY__();


		if(COP0_JOB_CMD_PROG == job->cmd){
			opt_dccm_log_debug(0xAAAAAA00 | (cmd << 4) | OPTQB[OPTQB_LOOKUP_PTR_UPDATE]);
			opt_dccm_log_debug(pca);
			//opt_dccm_log_debug(VBRMP_AND_BBRMP_BPS);//VBRMP_BPS and BBRMP_BPS
			opt_dccm_log_debug(rmp_pca);
		}


		/* update job info. */
		job->cmd_cnt = 1;
		job->plane_vld = OPT_LOOKUP_PLANE_BIT;
		job->cmd = cmd;
		job->slc_mode = slc_mode;
		job->pca = pca;
		job->rmp_pca = rmp_pca;
		job->lmu = lmu;
		job->ubDie = die;
		job->ubMicronDummyRead = (OPT_CHECK_LOOKUP_NEED_DUMMY_READ) ? TRUE : FALSE;
		job->ubD1 = (OPTQ_PROC_Q_LOOKUP_D1()) ? TRUE : FALSE;
		job->user_define = OPTQ_PROC_Q_LOOKUP_USRDEF_INFO;
		job->page = OPT_LOOKUP_PAGE_ADDR;
#if (NEW_IWL_EN)
		job->ubIWLEn = 0;
#endif /* (NEW_IWL_EN) */

		if (COP0_JOB_CMD_READ == cmd) {
			ubQueueCnt = OPT_CHECK_PLANE_SEARCH_QUEUE_CNT((job->slc_mode | IOR_EN));
			ubTimeout = OPT_CHECK_TIMEOUT();
			if ((OPT_PROCESS_QUEUE_ELEMENT_CNT() != OPT_PLANE_SEARCH_READ_CMD_CNT()) && (OPT_PLANE_SEARCH_NON_READ_PTR() != OPT_PLANE_SEARCH_READ_CMD_CNT())) {
				status = OPT_NEW_GATHER_STATUS_INIT;
			}
			else if ((0 == OPT_CHECK_PLANE_SEARCH_ROUND_NO_WAIT(PLANE_SEARCH_ROUND_IDX0)) && (FALSE == ubTimeout) && (FALSE == OPT_CHECK_TABLE_READ_NO_WAIT)) {
				status = OPT_NEW_GATHER_STATUS_INIT;
			}
			else {
				job->cmd_cnt = 0;
				job->plane_vld = 0;
				job->ubBinValue = OPT_GET_LOOKUP_BIN_VALUE;
				if (IOR_EN) {
					job->ior_ps_cross_group_exist = FALSE;
					job->ior_ps_exe_result_idx = 0;
					job->ubMinGroupID = OPT_INVALID_IOR_GROUP_ID;
				}
				uoPlaneSearchResult = OPTQLL[R64_OPT_PS_RESULT_IDX_0];
				__ASM_VOLATILE_MEMORY__();
				if (PS_READ_LOOKUP_PTR_CHECK_DEBUG) {
					OPT_CRITICAL_ASSERT(ASSERT_LOOKUP_PTR_ERROR | 0x1, OPTQB[OPTQB_LOOKUP_PTR_UPDATE]);
				}
				if (IOR_EN) {
					ubMinGroupID = OPT_INVALID_IOR_GROUP_ID;
					ubMinOPTIdx = OPT_INVALID_OPT_INDEX;
					for (ubPlaneIndex = 0; ubPlaneIndex < gubMaxPlane; ubPlaneIndex++) {
						ubOPTIndex = GET_VALUE(uoPlaneSearchResult, ubPlaneIndex * 8, 8); // 每個opt cmd 用8 bit表示opt index
						if (OPT_INVALID_OPT_INDEX != ubOPTIndex) {
							OPTQB[OPTQB_LOOKUP_PTR_UPDATE] = ubOPTIndex;
							hal_optq_wait_lookup_q_valid();
							if (OPTQ_PROC_Q_LOOKUP_IOR_EN() && ubOPTIndex < ubMinOPTIdx) {
								ubMinGroupID = OPTQ_PROC_Q_LOOKUP_IOR_GRP();
								ubMinOPTIdx = ubOPTIndex;
							}
						}
					}
					job->ubMinGroupID = ubMinGroupID;
				}
#if (NEW_IWL_EN)
				U8 ubPlaneCnt = 0;
				ubPlaneIndex = plane;
				U8 ubSamePagePlaneBMP = (BIT0 << ubPlaneIndex);
				U8 ubIWLPagePlaneBMP	= (gubIWLFlag && (FALSE == OPT_GET_LOOKUP_DISABLE_IWL) && gubMicronSnapReadBMP[OPTQ_PROC_Q_LOOKUP_FRAME_VLD]) ? ubSamePagePlaneBMP : 0;
				U16 uwPageIdx		= OPT_LOOKUP_PAGE_ADDR;
#if (NDEP_READ_EN)
				if ((FALSE == job->ubMicronDummyRead ) && (!M_OPT_CHECK_LOOKUP_NDEP_READ(job->user_define))) //Fix dummy read multi plane bug
#else
				if (FALSE == job->ubMicronDummyRead )	//Fix dummy read multi plane bug
#endif
				{
					for (ubPlaneCnt = 0; ubPlaneCnt < (NAND_MAX_PLANE - 1); ubPlaneCnt++) {
						ubPlaneIndex = (ubPlaneIndex + 1) & gubPlaneMask;
						ubOPTIndex = GET_VALUE(uoPlaneSearchResult, ubPlaneIndex * 8, 8); // 每個opt cmd 用8 bit表示opt index
						if (OPT_INVALID_OPT_INDEX != ubOPTIndex) {
							OPTQB[OPTQB_LOOKUP_PTR_UPDATE] = ubOPTIndex;
							hal_optq_wait_lookup_q_valid();
							if (IOR_EN) {
								if (OPTQ_PROC_Q_LOOKUP_IOR_EN() && OPTQ_PROC_Q_LOOKUP_IOR_GRP() != ubMinGroupID) {
									job->ior_ps_cross_group_exist = TRUE;
									if (IOR_FLOW_DEBUG) {
										OPT_CRITICAL_ASSERT(ASSERT_IOR_ERROR | 0x0, 0 == ubOPTIndex);
									}
									continue;
								}
							}
							if (ubIWLPagePlaneBMP && (FALSE == OPT_GET_LOOKUP_DISABLE_IWL) && gubMicronSnapReadBMP[OPTQ_PROC_Q_LOOKUP_FRAME_VLD]) {
								if (gubMicronIWLGroupBMP[(ubIWLPagePlaneBMP | (BIT0 << ubPlaneIndex))]) {
									ubIWLPagePlaneBMP |= (BIT0 << ubPlaneIndex);
								}
							}
							if (OPT_LOOKUP_PAGE_ADDR == uwPageIdx) {
								ubSamePagePlaneBMP |= (BIT0 << ubPlaneIndex);
							}
						}
					}
				}
				else {
					ubIWLPagePlaneBMP = 0;
				}

				if (ubIWLPagePlaneBMP && (bit_vld_2_bit_cnt[ubIWLPagePlaneBMP] >= bit_vld_2_bit_cnt[ubSamePagePlaneBMP])) {
					//IWL
					job->plane_vld = ubIWLPagePlaneBMP;
					job->ubIWLEn = gubMicronIWLGroupBMP[job->plane_vld];
				}
				else {
					job->plane_vld = ubSamePagePlaneBMP;
				}
				job->cmd_cnt = bit_vld_2_bit_cnt[job->plane_vld];

#else /* (NEW_IWL_EN) */
				for (ubPlaneIndex = 0; ubPlaneIndex < gubMaxPlane; ubPlaneIndex++) {
					ubOPTIndex = GET_VALUE(uoPlaneSearchResult, ubPlaneIndex * 8, 8); // 每個opt cmd 用8 bit表示opt index
					if (OPT_INVALID_OPT_INDEX != ubOPTIndex) {
						if (IOR_EN) {
							OPTQB[OPTQB_LOOKUP_PTR_UPDATE] = ubOPTIndex;
							hal_optq_wait_lookup_q_valid();
							if (OPTQ_PROC_Q_LOOKUP_IOR_EN() && OPTQ_PROC_Q_LOOKUP_IOR_GRP() != ubMinGroupID) {
								job->ior_ps_cross_group_exist = TRUE;
								if (IOR_FLOW_DEBUG) {
									OPT_CRITICAL_ASSERT(ASSERT_IOR_ERROR | 0x0, 0 == ubOPTIndex);
								}
								continue;
							}
						}
						job->cmd_cnt++;
						job->plane_vld |= (BIT0 << ubPlaneIndex);
						if ( job->ubMicronDummyRead ) {	//Fix dummy read multi plane bug
							break;
						}
						#if (NDEP_READ_EN) //Gary modify on 2022/4/14
						if (M_OPT_CHECK_LOOKUP_NDEP_READ(job->user_define)) { //NDEP can't use multi plane read
							break;
						}
						#endif
					}
				}

				if (IOR_EN && job->ior_ps_cross_group_exist) {
					OPTQB[OPTQB_LOOKUP_PTR_UPDATE] = 0; // For Snap Get Lookup FRAME VLD
					hal_optq_wait_lookup_q_valid();
				}

				if (gubIWLFlag && (1 == job->cmd_cnt) && (FALSE == OPT_GET_LOOKUP_DISABLE_IWL)) {
					if (gubMicronSnapReadBMP[OPTQ_PROC_Q_LOOKUP_FRAME_VLD]) {
						job->ubIWLEn = gubMicronIWLGroupBMP[job->plane_vld];
					}
				}
#endif //(NEW_IWL_EN)
				if ((FULL_PLANE_CACHE_READ) && (FALSE == job->ubIWLEn)) {
					if (READ_CMD_DMA_SYNC_CNT_EN && (job->cmd_cnt != gubMaxPlane)) {
						status = OPT_NEW_GATHER_STATUS_FAIL;  // 收到幾個cmd就打幾個plane的cmd
					}
					else {
						if ((1 == ubQueueCnt) && (TRUE == ubTimeout)) {
							status = OPT_NEW_GATHER_STATUS_FAIL;  // 湊不到cache
						}
						else {
							if ((gOptStruct.disable_cache_read) || OPT_CHK_DIS_CACHE_USERDEFINE) {
								// disable cache-read (for QD1 random read or die-interleave)
							}
							else {
								if (IOR_EN && job->ior_ps_cross_group_exist) {
									//No executing cache read
								}
#if (!NEW_IWL_EN)
								else if ((FALSE == OPT_GET_LOOKUP_DISABLE_IWL) && (TRUE == SNAP_READ_EN) && (1 == job->cmd_cnt) && (0 != gubMicronSnapReadBMP[OPTQ_PROC_Q_LOOKUP_FRAME_VLD])) {
									//Use Snap Read performance better than cache
								}
#endif /* (!NEW_IWL_EN) */
								else {
									job->cache_next = TRUE;
								}
							}
							status = OPT_NEW_GATHER_STATUS_SUCCESS;
						}
					}
				}
				else {
#if (NEW_IWL_EN)
					if (job->ubIWLEn) {
						if (IWL_SNAP_READ_ONLY_EN || (1 == job->cmd_cnt)) {
							status = OPT_NEW_GATHER_STATUS_FAIL;  //強制斷掉, 只做Snap Read
						}
						else {
							job->cache_next = TRUE;
							status = OPT_NEW_GATHER_STATUS_SUCCESS;
						}
					}
#else
					if (job->ubIWLEn && IWL_SNAP_READ_ONLY_EN) {
						status = OPT_NEW_GATHER_STATUS_FAIL;  //強制斷掉, 只做Snap Read
					}
#endif /* (NEW_IWL_EN) */
					else if ((1 == ubQueueCnt) && ((TRUE == OPT_CHECK_TABLE_READ_NO_WAIT) || (TRUE == ubTimeout))) {
						status = OPT_NEW_GATHER_STATUS_FAIL;  // 湊不到multi plane
#if DEBUG_DCCCM_BY_QUEUE
						if (gOptStruct.cur_que <= DEBUG_QUEUE_MAX) {
							_WRITE_OPT_DCCM_MT_DEBUG(0xD0 << 24 | (0xFFFFFF & job->rmp_pca));
						}
#endif
					}
					else {
						if ((gOptStruct.disable_cache_read) || OPT_CHK_DIS_CACHE_USERDEFINE) {
							// disable cache-read (for QD1 random read or die-interleave)
						}
						else {
							if (IOR_EN && job->ior_ps_cross_group_exist) {
								//No executing cache read
							}
#if (!NEW_IWL_EN)
							else if (job->ubIWLEn) {
								job->cache_next = TRUE;
							}
							else if ((FALSE == OPT_GET_LOOKUP_DISABLE_IWL) && (TRUE == SNAP_READ_EN) && (1 == job->cmd_cnt) && (gubMicronSnapReadBMP[OPTQ_PROC_Q_LOOKUP_FRAME_VLD])) {
								//Use Snap Read performance better than cache
							}
#endif /* (!NEW_IWL_EN) */
							else {
								job->cache_next = TRUE; // Even if Queue Cnt is 1, always Send Cache Read first,
							}
						}
#if DEBUG_DCCCM_BY_QUEUE

						if (gOptStruct.cur_que <= DEBUG_QUEUE_MAX) {
							_WRITE_OPT_DCCM_MT_DEBUG(0xD1 << 24 | (0xFFFFFF & job->rmp_pca));
						}
#endif
						status = OPT_NEW_GATHER_STATUS_SUCCESS;
					}
				}

				state = OPT_GATHER_STATE_WAIT_READ_NEXT_PLANE;

				OPTQB[OPTQB_LOOKUP_PTR_UPDATE] = 0;
			}
		}
		else if (COP0_JOB_CMD_VENDER_CMD == cmd) {
			job->user_define = OPTQL[OPTQ_PROC_Q_HEAD_USRDEF];
			status = OPT_NEW_GATHER_STATUS_SUCCESS;
			OPTQB[OPTQB_LOOKUP_PTR_UPDATE]++;
		}
		else if (COP0_JOB_CMD_IOR_NO_DMA == cmd) {
			status = OPT_NEW_GATHER_STATUS_SUCCESS;
#if (IOR_EN)
			OPT_CRITICAL_ASSERT(ASSERT_LOOKUP_PTR_ERROR | 0x7, (OPTQB[OPTQB_LOOKUP_PTR_UPDATE]));
			job->ubMinGroupID = OPTQ_PROC_Q_LOOKUP_IOR_GRP();
#endif /* (IOR_EN) */
			OPTQB[OPTQB_LOOKUP_PTR_UPDATE]++;
		}
		else {
			job->page = OPT_LOOKUP_PAGE_ADDR;
			job->lmu = lmu;
			gubLastMixPlaneBMP[gOptStruct.cur_que] = job->plane_vld;
			//Lam Debug
			opt_dccm_log_debug(0xABCD0000 | plane);

			if (COP0_JOB_CMD_PROG == job->cmd) {
				hal_optq_wait_lookup_q_convpage_valid();
				job->RS_parity |= M_OPT_CHK_LOOKUP_RAIDECC_PROGRAM_PARITY;
			}

			if ((FSP_PAGE_CNT > 1) && (FALSE == slc_mode) && (COP0_JOB_CMD_PROG == cmd) ) {
				job->fsp = TRUE;
				if (MICRON_140S) {
					OptGetLastProgramPage(job);
				}
				OPT_CRITICAL_ASSERT(ASSERT_PCA_ERROR | 0x0, (0 != lmu));
			}

			OPT_CRITICAL_ASSERT(ASSERT_PCA_ERROR | 0x1, OPTQ_PROC_Q_LOOKUP_RMP_BPS != OPTQ_PROC_Q_LOOKUP_BBRMP_BPS);
			job->ubRMP_bypass = OPTQ_PROC_Q_LOOKUP_RMP_BPS;
			if (OPT_CHK_LOOKUP_END_PROGRAM_USERDEFINE) {
				/*
				 * end prog/erase
				 */

				/*
				 * last plane, done, can run macro, can cache next
				 */
				job->ubPlaneCnt = job->cmd_cnt; //紀錄真正 job plane cnt是多少

				if (((CONFIG_FLASH_TYPE != FLASH_TYPE_MICRON_3D_QLC) && (TRUE == job->slc_mode) || ((CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC) && (TRUE == job->slc_mode))) && (COP0_JOB_CMD_PROG == cmd) && (FALSE == gOptStruct.disable_cache_prog) && (0 == OPT_CHK_DIS_CACHE_USERDEFINE)) {
					job->cache_next = TRUE;
				}

				status = OPT_NEW_GATHER_STATUS_SUCCESS;
			}
			else {
				/*
				 * need to gather next plane
				 */
				state = OPT_GATHER_STATE_WAIT_OTHER_NEXT_PLANE;
				finish = FALSE;
			}

			OPTQB[OPTQB_LOOKUP_PTR_UPDATE]++;
			__ASM_VOLATILE_MEMORY__();
		}
	}
	else if (lookup_state & OPTQB_LOOKUP_Q_TIMEOUT) {
		/*
		 * should never be here (1st plane of 1st macro should not time-out)
		 */
		status = OPT_NEW_GATHER_STATUS_TIME_OUT;
		_WRITE_OPT_DCCM_PATH_DEBUG(TIMEOUT_OP(0x10));
		OPT_CRITICAL_ASSERT(ASSERT_FLOW_ERROR | 0x2, TRUE);
	}
	else {
		// there is a chance both lookup_valid and time-out are 0 for a small period
		status = OPT_NEW_GATHER_STATUS_INIT;
	}

	job->gather_state = state;
	job->gather_status = status;

	return finish;
}

inline BOOL opt_gather_1st_macro_wait_other_next_plane(OPT_QUE_MGR_STRUCT_PTR que_mgr, OPT_JOB_STRUCT_PTR job)
{
	BOOL finish;
	BOOL success;
	U32 pca, rmp_pca, diff_bit_pca, diff_bit_rmp_pca;
	U16 page;
	U8 cmd;
	U8 slc_mode;
	U8 pca_rule;
	U8 plane_bit, plane;
	U8 lmu;
	U8 state;
	U8 status;
	U8 lookup_state;

	state = job->gather_state;
	status = job->gather_status;
	finish = TRUE;

	/*
	 * wait next plane (look ahead) (may gather next page for fsp as well)
	 */
	__ASM_VOLATILE_MEMORY__();
	lookup_state = OPTQB[OPTQB_LOOKUP_Q_VALID];
	if (lookup_state & OPTQB_ELEMENT_VALID) {
		U8 ubChangePage = FALSE;
		hal_optq_wait_lookup_q_valid();
		_WRITE_OPT_DCCM_PATH_DEBUG(PARSER_1st_OP(OPT_GATHER_STATE_WAIT_OTHER_NEXT_PLANE));
		cmd = OPTQ_PROC_Q_LOOKUP_CMD;
		pca = OPTQL[OPTQ_PROC_Q_LOOKUP_PCA];
		rmp_pca = OPTQL[OPTQ_PROC_Q_LOOKUP_RMP_PCA];
		slc_mode = OPTQ_PROC_Q_LOOKUP_SLC_MODE;
		page = OPT_LOOKUP_PAGE_ADDR;
		plane_bit = OPT_LOOKUP_PLANE_BIT;
		plane = OPT_LOOKUP_PLANE_ADDR;//rmp_pca plane addr
		lmu = hal_loopup_page_type();
		pca_rule = ((slc_mode << 1) | OPTQ_PROC_Q_LOOKUP_D1()); // SLC bit [1], D1 bit [0]
		diff_bit_pca = OPT_CHK_PCA_DIFF_BIT(pca, job->pca);
		diff_bit_rmp_pca = OPT_CHK_PCA_DIFF_BIT(rmp_pca, job->rmp_pca);
		__ASM_VOLATILE_MEMORY__();

		if(COP0_JOB_CMD_PROG == job->cmd){
			opt_dccm_log_debug(0xBBBBBB00 | (cmd << 4) | OPTQB[OPTQB_LOOKUP_PTR_UPDATE]);
			opt_dccm_log_debug(pca);
			//opt_dccm_log_debug(VBRMP_AND_BBRMP_BPS);//VBRMP_BPS and BBRMP_BPS
			opt_dccm_log_debug(0x00000000 | plane_bit);
			opt_dccm_log_debug(rmp_pca);
		}

		success = FALSE;
		if ((cmd == job->cmd) && (slc_mode == job->slc_mode) && OPT_CHK_PCA_IN_SAME_UNIT(diff_bit_pca) && OPT_CHK_PCA_IN_SAME_DIE(diff_bit_rmp_pca, pca_rule)) {
			if (page == job->page) {
				success = TRUE;
			}
			else {
				/*
				 * it is next page of fsp: gather success, too
				 */
				job->page = page;
				job->lmu = lmu;
				ubChangePage = TRUE;
				success = TRUE;
			}
		}

		if (success) {
			if (0 == job->ubPlaneCnt) {
				if (lmu || ubChangePage) {
					// toshiba, lmu = 1時 紀錄
					// other, page改變時 紀錄
					job->ubPlaneCnt = job->cmd_cnt; //紀錄真正 Physical PlaneBank Cnt是多少
				}
				else {//Lam:只用在第一组（LMU = 0）Multi Plane，因为同Block，所以被替换其它LMU的也会同步替换，第一组记录的MixPlaneBMP适用第二、三组
					if (gubLastMixPlaneBMP[gOptStruct.cur_que] & plane_bit) { // 與前一組mix plane BMP重疊
						job->ubMixPlaneBMP |= BIT(job->cmd_cnt - 1); // 斷 multi-plane 斷的位置，ABAD => AB/AD => 0101
						gubLastMixPlaneBMP[gOptStruct.cur_que] = plane_bit;//断过之后重新记录
					}
					else {
						gubLastMixPlaneBMP[gOptStruct.cur_que] |= plane_bit;
					}
				}
			}
			job->cmd_cnt++;
			job->plane_vld |= plane_bit;
			if (COP0_JOB_CMD_PROG == job->cmd) {
				hal_optq_wait_lookup_q_convpage_valid();
				job->RS_parity |= M_OPT_CHK_LOOKUP_RAIDECC_PROGRAM_PARITY;
			}

			OPT_CRITICAL_ASSERT(ASSERT_PCA_ERROR | 0x2, (OPTQ_PROC_Q_LOOKUP_RMP_BPS != OPTQ_PROC_Q_LOOKUP_BBRMP_BPS));
			OPT_CRITICAL_ASSERT(ASSERT_PCA_ERROR | 0x3, (OPTQ_PROC_Q_LOOKUP_RMP_BPS != job->ubRMP_bypass));

			if (OPT_CHK_LOOKUP_END_PROGRAM_USERDEFINE) {
				/*
				 * end prog/erase
				 */

				/*
				 * last plane, done, can run macro, can cache next
				 */
				if (0 == job->ubPlaneCnt) {
					job->ubPlaneCnt = job->cmd_cnt; //紀錄真正 job plane cnt是多少
				}

				// 1st macro湊到的結果不含plane A的話, 就不能打cache program
#if (CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_TLC || CONFIG_FLASH_TYPE == FLASH_TYPE_BICS5_3D_TLC || CONFIG_FLASH_TYPE == FLASH_TYPE_BICS6_3D_TLC || CONFIG_FLASH_TYPE == FLASH_TYPE_BICS8_3D_TLC || CONFIG_FLASH_TYPE == FLASH_TYPE_BICS6_3D_QLC) // only Support SLC Cache//zerio bics6 qlc add
				if ((TRUE == job->slc_mode)	&& (COP0_JOB_CMD_PROG == cmd) && (FALSE == gOptStruct.disable_cache_prog) && (FALSE == OPT_CHK_DIS_CACHE_USERDEFINE) && (job->plane_vld & BIT0) && (FALSE == job->ubMixPlaneBMP)) {
					job->cache_next = TRUE;
				}
#else
				if (((CONFIG_FLASH_TYPE != FLASH_TYPE_MICRON_3D_QLC) || ((CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC) && (TRUE == job->slc_mode))) 
					&& (COP0_JOB_CMD_PROG == cmd) && (FALSE == gOptStruct.disable_cache_prog) && (FALSE == OPT_CHK_DIS_CACHE_USERDEFINE) && (job->plane_vld & BIT0) && (FALSE == job->ubMixPlaneBMP)) {
					job->cache_next = TRUE;
				}
#endif
				if(job->ubMixPlaneBMP){
					job->ubMixPlaneBMP = job->ubMixPlaneBMP | BIT(0) | BIT(1) | BIT(2);//If Mixplane occour,force to do Sigle Plane.
					job->cache_next = FALSE;
				}

				opt_dccm_log_debug(0xEEEEEEEE);
				opt_dccm_log_debug(0x00000000 | job->ubMixPlaneBMP);
				status = OPT_NEW_GATHER_STATUS_SUCCESS;
			}
			else {
				/*
				 * continue gather next plane
				 */
				finish = FALSE;
			}

			OPTQB[OPTQB_LOOKUP_PTR_UPDATE]++;
			__ASM_VOLATILE_MEMORY__();
		}
		else {
			if ((COP0_JOB_CMD_ERASE == job->cmd) || job->slc_mode || (FSP_PAGE_CNT <= 1)) {	// erase, SLC program or non-TLC sample才可以斷
				/*
				 * last plane, done, can run macro, can cache next
				 */
				if (0 == job->ubPlaneCnt) {
					job->ubPlaneCnt = job->cmd_cnt; //紀錄真正 job plane cnt是多少
				}
#if (CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_TLC || CONFIG_FLASH_TYPE == FLASH_TYPE_BICS5_3D_TLC || CONFIG_FLASH_TYPE == FLASH_TYPE_BICS6_3D_TLC || CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_QLC || CONFIG_FLASH_TYPE == FLASH_TYPE_BICS8_3D_TLC || CONFIG_FLASH_TYPE == FLASH_TYPE_BICS6_3D_QLC) // only Support SLC Cache	//Reip Porting 3D-V7 QLC Add//zerio bics6 qlc add
				if ((TRUE == job->slc_mode) && (COP0_JOB_CMD_PROG == cmd) && (FALSE == gOptStruct.disable_cache_prog) && (FALSE == OPT_CHK_DIS_CACHE_USERDEFINE) && (FALSE == job->ubMixPlaneBMP)) {
					job->cache_next = TRUE;
				}
#else
				if (((CONFIG_FLASH_TYPE != FLASH_TYPE_MICRON_3D_QLC) || ((CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC) && (TRUE == job->slc_mode))) && (COP0_JOB_CMD_PROG == cmd) && (FALSE == gOptStruct.disable_cache_prog) && (FALSE == OPT_CHK_DIS_CACHE_USERDEFINE) && (FALSE == job->ubMixPlaneBMP)) {
					job->cache_next = TRUE;
				}
#endif
				status = OPT_NEW_GATHER_STATUS_SUCCESS;
			}
			else {
				// ASSERT!!
				OPT_CRITICAL_ASSERT(ASSERT_FLOW_ERROR | 0x3, TRUE);
			}
		}

	}
	else if ((COP0_JOB_CMD_PROG == job->cmd) && cpu_comm->SeqProg) {
		status = OPT_NEW_GATHER_STATUS_INIT;
	}
	else if (lookup_state & OPTQB_LOOKUP_Q_TIMEOUT) {
		status = OPT_NEW_GATHER_STATUS_TIME_OUT;
		_WRITE_OPT_DCCM_PATH_DEBUG(TIMEOUT_OP(0x12));
	}
	else {
		// there is a chance both lookup_valid and time-out are 0 for a small period
		status = OPT_NEW_GATHER_STATUS_INIT;
	}

	job->gather_state = state;
	job->gather_status = status;

	return finish;
}

inline BOOL opt_gather_next_macro_wait_1st_plane(OPT_QUE_MGR_STRUCT_PTR que_mgr, OPT_JOB_STRUCT_PTR job, OPT_JOB_STRUCT_PTR first_job)
{
	BOOL finish;
	BOOL success;
	BOOL PlaneSearchSuccess;
	U32 pca, rmp_pca, diff_bit_pca, diff_bit_rmp_pca;
	U8 cmd;
	U8 slc_mode;
	U8 pca_rule;
	U8 plane;
	U8 lmu;
	U8 die;
	U8 state;
	U8 status;
	U8 lookup_state;
	U8 ubQueueCnt;
	U8 ubPlaneIndex;
	U8 ubOPTIndex;
	U8 ubMinGroupID = OPT_INVALID_IOR_GROUP_ID;
	U8 ubMinOPTIdx = OPT_INVALID_OPT_INDEX;
	U64 uoPlaneSearchResult = OPT_PS_INVALID_RESULT;
	U8 lookup_ptr_bk;
	U8 ubTimeout = FALSE;
	U8 ubDisableCacheRead = FALSE;
	U8 ubIWLPagePlaneBMP = 0;
	U16 uwPageIdxArray[NAND_MAX_PLANE] = {0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF};
	state = job->gather_state;
	status = job->gather_status;
	finish = TRUE;
	PlaneSearchSuccess = FALSE;

	/*
	 * wait 1st plane
	 */
	__ASM_VOLATILE_MEMORY__();


	// 找next round result的1st valid opt element
	lookup_ptr_bk = OPTQB[OPTQB_LOOKUP_PTR_UPDATE];

	if (COP0_JOB_CMD_READ == first_job->cmd) {
		ubQueueCnt = OPT_CHECK_PLANE_SEARCH_QUEUE_CNT((first_job->slc_mode | IOR_EN));

		ubTimeout = OPT_CHECK_TIMEOUT();
		__ASM_VOLATILE_MEMORY__();
		if ((OPT_PROCESS_QUEUE_ELEMENT_CNT() != OPT_PLANE_SEARCH_READ_CMD_CNT()) && (OPT_PLANE_SEARCH_NON_READ_PTR() != OPT_PLANE_SEARCH_READ_CMD_CNT())) {
			uoPlaneSearchResult = OPT_PS_INVALID_RESULT;
		}
		else if ((ubQueueCnt <= 2) && (0 == OPT_CHECK_PLANE_SEARCH_ROUND_NO_WAIT (PLANE_SEARCH_ROUND_IDX1)) && (FALSE == ubTimeout) && (FALSE == OPT_CHECK_TABLE_READ_NO_WAIT)) {
			uoPlaneSearchResult = OPT_PS_INVALID_RESULT;
		}
		else if (ubQueueCnt > 1) {
			uoPlaneSearchResult = OPTQLL[R64_OPT_PS_RESULT_IDX_0 + OPTQB[R8_OPT_PS_RESULT_MLC_QUEUE_1 + ((IOR_EN ? TRUE : first_job->slc_mode) * 4)]];

			__ASM_VOLATILE_MEMORY__();
			if (OPT_PS_INVALID_RESULT != uoPlaneSearchResult) {
				U8 lookup_ptr_bk = OPTQB[OPTQB_LOOKUP_PTR_UPDATE];
				U8 first_plane_opt_idx = OPT_INVALID_OPT_INDEX;
				ubMinGroupID = OPT_INVALID_IOR_GROUP_ID;
				ubMinOPTIdx = OPT_INVALID_OPT_INDEX;
				PlaneSearchSuccess = TRUE;
				for (ubPlaneIndex = 0; ubPlaneIndex < gubMaxPlane; ubPlaneIndex++) {
					ubOPTIndex = GET_VALUE(uoPlaneSearchResult, ubPlaneIndex * 8, 8); // 每個opt cmd 用8 bit表示opt index
					if (NEW_IWL_EN) {
						if (OPT_INVALID_OPT_INDEX != ubOPTIndex) {
							OPTQB[OPTQB_LOOKUP_PTR_UPDATE] = ubOPTIndex;
							hal_optq_wait_lookup_q_valid();
							if (IOR_EN) {
								if (OPTQ_PROC_Q_LOOKUP_SLC_MODE != first_job->slc_mode) {
									PlaneSearchSuccess = FALSE;
									OPTQB[OPTQB_LOOKUP_PTR_UPDATE] = lookup_ptr_bk;
									break;
								}
								if (OPTQ_PROC_Q_LOOKUP_IOR_EN() && ubOPTIndex < ubMinOPTIdx) {
									ubMinOPTIdx = ubOPTIndex;
									ubMinGroupID = OPTQ_PROC_Q_LOOKUP_IOR_GRP();
								}
							}
							// find the min opt idx
							if (ubOPTIndex < first_plane_opt_idx) {
								first_plane_opt_idx = ubOPTIndex;
							}
							uwPageIdxArray[OPT_LOOKUP_PLANE_ADDR] = OPT_LOOKUP_PAGE_ADDR;

							if (gubIWLFlag) {
								if (gubMicronSnapReadBMP[OPTQ_PROC_Q_LOOKUP_FRAME_VLD]) {
									if (gubMicronIWLGroupBMP[(ubIWLPagePlaneBMP | (BIT0 << ubPlaneIndex))]) {
										ubIWLPagePlaneBMP |= (BIT0 << ubPlaneIndex);
									}
								}
							}
						}
					}
					else if (OPT_INVALID_OPT_INDEX != ubOPTIndex) {
						if (OPT_INVALID_OPT_INDEX == first_plane_opt_idx) {
							first_plane_opt_idx = ubOPTIndex;
						}
						if (IOR_EN) {
							OPTQB[OPTQB_LOOKUP_PTR_UPDATE] = ubOPTIndex;
							hal_optq_wait_lookup_q_valid();
							if (OPTQ_PROC_Q_LOOKUP_SLC_MODE != first_job->slc_mode) {
								PlaneSearchSuccess = FALSE;
								OPTQB[OPTQB_LOOKUP_PTR_UPDATE] = lookup_ptr_bk;
								break;
							}
							if (OPTQ_PROC_Q_LOOKUP_IOR_EN() && ubOPTIndex < ubMinOPTIdx) {
								first_plane_opt_idx = ubOPTIndex;
								ubMinOPTIdx = ubOPTIndex;
								ubMinGroupID = OPTQ_PROC_Q_LOOKUP_IOR_GRP();
							}
						}
						else {
							break;
						}
					}
				}
				if (IOR_FLOW_DEBUG) {
					OPT_CRITICAL_ASSERT(ASSERT_IOR_ERROR | 0x1, (OPT_INVALID_IOR_GROUP_ID != ubMinGroupID) && (TRUE == PlaneSearchSuccess) && (OPT_INVALID_OPT_INDEX == ubMinOPTIdx));
				}
				if (PlaneSearchSuccess) {
					OPTQB[OPTQB_LOOKUP_PTR_UPDATE] = first_plane_opt_idx;
					hal_optq_wait_lookup_q_valid();
					__ASM_VOLATILE_MEMORY__();
				}
			}
		}
	}

	/*
	 * gather in opt_gather_next_macro() 1st plane
	 */
	lookup_state = OPTQB[OPTQB_LOOKUP_Q_VALID];
	if (lookup_state & OPTQB_ELEMENT_VALID) {
		hal_optq_wait_lookup_q_valid();
		_WRITE_OPT_DCCM_PATH_DEBUG(PARSER_2nd_OP(OPT_GATHER_STATE_WAIT_1ST_PLANE));
		cmd = OPTQ_PROC_Q_LOOKUP_CMD;
		pca = OPTQL[OPTQ_PROC_Q_LOOKUP_PCA];
		rmp_pca = OPTQL[OPTQ_PROC_Q_LOOKUP_RMP_PCA];
		slc_mode = OPTQ_PROC_Q_LOOKUP_SLC_MODE;
		plane = OPT_LOOKUP_PLANE_ADDR;
		lmu = hal_loopup_page_type();
		die = OPT_LOOKUP_DIE_ADDR;
		ubDisableCacheRead = OPT_CHK_DIS_CACHE_USERDEFINE;
		job->ubD1 = (OPTQ_PROC_Q_LOOKUP_D1()) ? TRUE : FALSE;

		pca_rule = ((slc_mode << 1) | OPTQ_PROC_Q_LOOKUP_D1()); // SLC bit [1], D1 bit [0]
		diff_bit_pca = OPT_CHK_PCA_DIFF_BIT(pca, first_job->pca);
		diff_bit_rmp_pca  = OPT_CHK_PCA_DIFF_BIT(rmp_pca, first_job->rmp_pca);
		success = FALSE;

		if(COP0_JOB_CMD_PROG == job->cmd){
			opt_dccm_log_debug(0x33333300 | (cmd << 4) | OPTQB[OPTQB_LOOKUP_PTR_UPDATE]);
			opt_dccm_log_debug(pca);
			//opt_dccm_log_debug(VBRMP_AND_BBRMP_BPS);//VBRMP_BPS and BBRMP_BPS
			opt_dccm_log_debug(rmp_pca);
		}


		__ASM_VOLATILE_MEMORY__();

		if (COP0_JOB_CMD_READ == cmd) {
			if (OPTQ_PROC_Q_LOOKUP_IOR_EN() && (TRUE == PlaneSearchSuccess) && (OPTQ_PROC_Q_LOOKUP_IOR_GRP() != ubMinGroupID)) {
				success = FALSE; //4
			}
			else {
				if (die == first_job->ubDie) {
					success = PlaneSearchSuccess;
				}
				else {
					success = FALSE;
					OPTQB[OPTQB_LOOKUP_PTR_UPDATE] = lookup_ptr_bk;
				}
			}
		}
		else if ((COP0_JOB_CMD_PROG == cmd) || (COP0_JOB_CMD_ERASE == cmd)) {
			if ((cmd == first_job->cmd) &&
				(slc_mode == first_job->slc_mode) &&
				OPT_CHK_PCA_IN_SAME_BLOCK(diff_bit_rmp_pca, pca_rule) &&
				OPT_CHK_PCA_IN_SAME_DIE(diff_bit_rmp_pca, pca_rule)) {
				success = TRUE;
			}
		}
		else {
			/*
			 * any other cmd means a read or a prog cache fail
			 */
		}

		if (success) {
			job->cmd_cnt = 1;
			job->plane_vld = OPT_LOOKUP_PLANE_BIT;
			job->cmd = cmd;
			job->slc_mode = slc_mode;
			job->pca = pca;
			job->rmp_pca = rmp_pca;
			job->lmu = lmu;
			job->ubMinGroupID = ubMinGroupID;
			job->ubDie = die;
#if(NEW_IWL_EN)
			job->ubIWLEn = 0;
#endif /* (NEW_IWL_EN) */
			job->page = OPT_LOOKUP_PAGE_ADDR;
			if (COP0_JOB_CMD_READ == cmd) {
				job->cmd_cnt = 0;
				job->plane_vld = 0;
				job->ubBinValue = OPT_GET_LOOKUP_BIN_VALUE;
				if (IOR_EN) {
					job->ior_ps_exe_result_idx = 1;
				}
#if (NEW_IWL_EN)
				if (ubIWLPagePlaneBMP) {
					if (FALSE == gubMicronSnapReadBMP[OPTQ_PROC_Q_LOOKUP_FRAME_VLD]) {
						ubIWLPagePlaneBMP = 0;
					}
				}

#if (IOR_EN)
				U8 ubPlaneMaskBMP = 0;
				for (ubPlaneIndex = 0; ubPlaneIndex < gubMaxPlane; ubPlaneIndex++) {
					ubOPTIndex = GET_VALUE(uoPlaneSearchResult, ubPlaneIndex * 8, 8); // 每個opt cmd 用8 bit表示opt index
					if (OPT_INVALID_OPT_INDEX != ubOPTIndex) {

						OPTQB[OPTQB_LOOKUP_PTR_UPDATE] = ubOPTIndex;
						hal_optq_wait_lookup_q_valid();
						if (OPTQ_PROC_Q_LOOKUP_IOR_EN() && OPTQ_PROC_Q_LOOKUP_IOR_GRP() != ubMinGroupID) {
							job->ior_ps_cross_group_exist = TRUE;
							ubPlaneMaskBMP |= BIT(ubPlaneIndex);
						}
					}
				}
				ubIWLPagePlaneBMP &= ~ubPlaneMaskBMP;
#endif //IOR_EN

				if (0 == ubIWLPagePlaneBMP) {
					U8 ubSamePagePlaneBMP	= OPT_LOOKUP_PLANE_BIT;
					U8 ubPlaneCnt = 0;
					ubPlaneIndex = plane;
					U16 uwPageIdx	= OPT_LOOKUP_PAGE_ADDR;
					for (ubPlaneCnt = 0; ubPlaneCnt < (NAND_MAX_PLANE - 1); ubPlaneCnt++) {
						ubPlaneIndex = (ubPlaneIndex + 1) & gubPlaneMask;
						if (uwPageIdxArray[ubPlaneIndex] == uwPageIdx) {
							ubSamePagePlaneBMP |= (BIT0 << ubPlaneIndex);
						}
					}
#if(IOR_EN)
					ubSamePagePlaneBMP &= ~ubPlaneMaskBMP;
#endif /* (IOR_EN) */
					job->plane_vld = ubSamePagePlaneBMP;
				}
				else {
					job->plane_vld = ubIWLPagePlaneBMP;
					job->ubIWLEn = gubMicronIWLGroupBMP[job->plane_vld];
				}
				job->cmd_cnt = bit_vld_2_bit_cnt[job->plane_vld];
#else /* (NEW_IWL_EN) */
				for (ubPlaneIndex = 0; ubPlaneIndex < gubMaxPlane; ubPlaneIndex++) {
					ubOPTIndex = GET_VALUE(uoPlaneSearchResult, ubPlaneIndex * 8, 8); // 每個opt cmd 用8 bit表示opt index
					if (OPT_INVALID_OPT_INDEX != ubOPTIndex) {
						if (IOR_EN) {
							OPTQB[OPTQB_LOOKUP_PTR_UPDATE] = ubOPTIndex;
							hal_optq_wait_lookup_q_valid();
							if (OPTQ_PROC_Q_LOOKUP_IOR_EN() && (OPTQ_PROC_Q_LOOKUP_IOR_GRP() != ubMinGroupID)) {
								job->ior_ps_cross_group_exist = TRUE;
								continue;
							}
						}
						job->cmd_cnt++;
						job->plane_vld |= (BIT0 << ubPlaneIndex);
					}
				}
#endif /* (NEW_IWL_EN) */
				if (IOR_FLOW_DEBUG) {
					OPT_CRITICAL_ASSERT(ASSERT_IOR_ERROR | 0x2, 0 == job->cmd_cnt);
				}

				if (first_job->ubIWLEn) {
#if (!NEW_IWL_EN)
					if (IOR_EN && job->ior_ps_cross_group_exist) {
						OPTQB[OPTQB_LOOKUP_PTR_UPDATE] = ubMinOPTIdx; // For Snap Get LOOKUP FRAME VLD
						hal_optq_wait_lookup_q_valid();
					}
					if ((1 == job->cmd_cnt)) {
						if (gubMicronSnapReadBMP[OPTQ_PROC_Q_LOOKUP_FRAME_VLD]) {
							job->ubIWLEn = gubMicronIWLGroupBMP[job->plane_vld];
						}
					}
#endif /* (!NEW_IWL_EN) */
					if (job->ubIWLEn && (!ubDisableCacheRead)) {
						job->cache_prev = TRUE;
						job->cache_next = TRUE;
						status = OPT_NEW_GATHER_STATUS_SUCCESS;

						state = OPT_GATHER_STATE_WAIT_READ_NEXT_PLANE;
					}
					else {
						status = OPT_NEW_GATHER_STATUS_FAIL;
						state = OPT_GATHER_STATE_WAIT_1ST_PLANE;
					}
#if DEBUG_DCCCM_BY_QUEUE

					if (gOptStruct.cur_que <= DEBUG_QUEUE_MAX) {
						_WRITE_OPT_DCCM_MT_DEBUG((job->cache_next << 24) | (job->cache_prev << 16) | (job->ubIWLEn << 8) | (first_job->ubIWLEn));
						_WRITE_OPT_DCCM_MT_DEBUG(0xE1 << 24 | (0xFFFFFF & job->rmp_pca));
					}
#endif
				}
				else {
					if (FULL_PLANE_CACHE_READ) {
						if (ubDisableCacheRead || (READ_CMD_DMA_SYNC_CNT_EN && (job->cmd_cnt != gubMaxPlane))) {
							status = OPT_NEW_GATHER_STATUS_FAIL;  // CLOSE_MACRO先結束原本的cmd sequence, 然後 收到幾個cmd就打幾個plane的cmd
							state = OPT_GATHER_STATE_WAIT_1ST_PLANE;
						}
						else {
							job->cache_prev = TRUE;
							job->cache_next = TRUE;
							status = OPT_NEW_GATHER_STATUS_SUCCESS;
							state = OPT_GATHER_STATE_WAIT_READ_NEXT_PLANE;
						}

					}
#if (NEW_IWL_EN)
					else if (ubDisableCacheRead || ((IWL_EN || SNAP_READ_EN) && job->ubIWLEn)) {
#else /* (NEW_IWL_EN) */
					else if (ubDisableCacheRead || ((IWL_EN || SNAP_READ_EN) && (1 == job->cmd_cnt) && (0 != gubMicronSnapReadBMP[OPTQ_PROC_Q_LOOKUP_FRAME_VLD]))) {
#endif /* (NEW_IWL_EN) */
						//20201119 Use IWL or Snap Read, random read performance better than cache when FULL_PLANE_CACHE_READ is FALSE.
						status = OPT_NEW_GATHER_STATUS_FAIL;  // CLOSE_MACRO先結束原本的cmd sequence, 然後 收到幾個cmd就打幾個plane的cmd
						state = OPT_GATHER_STATE_WAIT_1ST_PLANE;
					}
					else {
						job->cache_prev = TRUE;
						job->cache_next = TRUE;
						status = OPT_NEW_GATHER_STATUS_SUCCESS;
						state = OPT_GATHER_STATE_WAIT_READ_NEXT_PLANE;
					}
				}
				//else
				/*
					plane search 湊cmd遇到 cache fail未更改success變數從TRUE變 FALSE
					lookup ptr依舊會清為0

					但在plane search關閉時 success變數為FALSE時lookup ptr依舊不做任何更動

				*/
				OPTQB[OPTQB_LOOKUP_PTR_UPDATE] = 0;
			}
			else {
				job->lmu = lmu;
				if ((FSP_PAGE_CNT > 1) && (FALSE == slc_mode) && (COP0_JOB_CMD_PROG == cmd)) {
					job->fsp = TRUE;
					if (MICRON_140S) {
						OptGetLastProgramPage(job);
					}
					OPT_CRITICAL_ASSERT(ASSERT_PCA_ERROR | 0x4, (0 != lmu));
				}

				if (COP0_JOB_CMD_PROG == job->cmd) {
					hal_optq_wait_lookup_q_convpage_valid();
					job->RS_parity |= M_OPT_CHK_LOOKUP_RAIDECC_PROGRAM_PARITY;
				}

				OPT_CRITICAL_ASSERT(ASSERT_PCA_ERROR | 0x5, (OPTQ_PROC_Q_LOOKUP_RMP_BPS != OPTQ_PROC_Q_LOOKUP_BBRMP_BPS));
				job->ubRMP_bypass = OPTQ_PROC_Q_LOOKUP_RMP_BPS;
				OPT_CRITICAL_ASSERT(ASSERT_PCA_ERROR | 0x6, (job->ubRMP_bypass != first_job->ubRMP_bypass));

				if (OPT_CHK_LOOKUP_END_PROGRAM_USERDEFINE) {
					/*
					 * end prog/erase
					 */

					/*
					 * last plane, done, can run macro, cache prev, can cache next
					 */
					if (job->plane_vld == first_job->plane_vld) {
						job->cache_prev = TRUE;
						job->cache_next = TRUE;
						status = OPT_NEW_GATHER_STATUS_SUCCESS;//paser_state:-->RUN_MACRO
						//cpu_comm->uoCacheProgCnt++;
					}
					else {
						opt_dccm_log_debug(0x4444FFFF);
						status = OPT_NEW_GATHER_STATUS_FAIL;
					}
				}
				else {
					/*
					 * need to gather next plane
					 */
					state = OPT_GATHER_STATE_WAIT_OTHER_NEXT_PLANE;
					finish = FALSE;
				}

				if (OPT_NEW_GATHER_STATUS_FAIL != status) {		// 沒有cache fail才將lookup ptr往後
					OPTQB[OPTQB_LOOKUP_PTR_UPDATE]++;
					__ASM_VOLATILE_MEMORY__();
				}
			}
		}
		else {
			if ((COP0_JOB_CMD_READ == first_job->cmd) && (FALSE == ubTimeout)) {
				status = OPT_NEW_GATHER_STATUS_INIT;//paser_state:-->IDLE-->Gather
			}
			else {
				/*
				 * cache fail, can't cache prev, close prev macro
				 */
				status = OPT_NEW_GATHER_STATUS_TIME_OUT;//OPT_NEW_GATHER_STATUS_FAIL;
			}
		}
	}
	else if ((COP0_JOB_CMD_PROG == first_job->cmd) && cpu_comm->SeqProg) {
		//opt_dccm_log_debug(0xF2F2F2F2);
		status = OPT_NEW_GATHER_STATUS_INIT;//paser_state:-->IDLE-->Gather
	}
	else if (lookup_state & OPTQB_LOOKUP_Q_TIMEOUT) {
		opt_dccm_log_debug(0xF3F3F3F3);
		status = OPT_NEW_GATHER_STATUS_TIME_OUT;
		_WRITE_OPT_DCCM_PATH_DEBUG(TIMEOUT_OP(0x20));
	}
	else {
		// there is a chance both lookup_valid and time-out are 0 for a small period
		opt_dccm_log_debug(0xF4F4F4F4);
		status = OPT_NEW_GATHER_STATUS_INIT;
	}

	job->gather_state = state;
	job->gather_status = status;

	return finish;
}

inline BOOL opt_gather_next_macro_wait_other_next_plane(OPT_QUE_MGR_STRUCT_PTR que_mgr, OPT_JOB_STRUCT_PTR job, OPT_JOB_STRUCT_PTR first_job)
{
	BOOL finish;
	BOOL success;
	U32 pca, rmp_pca, diff_bit_pca, diff_bit_rmp_pca;
	U16 page;
	U8 cmd;
	U8 slc_mode;
	U8 pca_rule;
	U8 plane_bit;
	U8 lmu;
	U8 state;
	U8 status;
	U8 lookup_state;

	state = job->gather_state;
	status = job->gather_status;
	finish = TRUE;

	/*
	 * wait next plane (look ahead) (may gather next page for fsp as well)
	 */
	__ASM_VOLATILE_MEMORY__();
	lookup_state = OPTQB[OPTQB_LOOKUP_Q_VALID];
	if (lookup_state & OPTQB_ELEMENT_VALID) {
		hal_optq_wait_lookup_q_valid();
		_WRITE_OPT_DCCM_PATH_DEBUG(PARSER_2nd_OP(OPT_GATHER_STATE_WAIT_OTHER_NEXT_PLANE));
		cmd = OPTQ_PROC_Q_LOOKUP_CMD;
		pca = OPTQL[OPTQ_PROC_Q_LOOKUP_PCA];
		rmp_pca = OPTQL[OPTQ_PROC_Q_LOOKUP_RMP_PCA];
		slc_mode = OPTQ_PROC_Q_LOOKUP_SLC_MODE;
		page = OPT_LOOKUP_PAGE_ADDR;
		plane_bit = OPT_LOOKUP_PLANE_BIT;
		lmu = hal_loopup_page_type();
		pca_rule = ((slc_mode << 1) | OPTQ_PROC_Q_LOOKUP_D1()); // SLC bit [1], D1 bit [0]
		diff_bit_pca = OPT_CHK_PCA_DIFF_BIT(pca, job->pca);
		diff_bit_rmp_pca = OPT_CHK_PCA_DIFF_BIT(rmp_pca, job->rmp_pca);

		if(COP0_JOB_CMD_PROG == job->cmd){
			opt_dccm_log_debug(0x44444400 | (cmd << 4) | OPTQB[OPTQB_LOOKUP_PTR_UPDATE]);
			opt_dccm_log_debug(pca);
			//opt_dccm_log_debug(VBRMP_AND_BBRMP_BPS);//VBRMP_BPS and BBRMP_BPS
			opt_dccm_log_debug(rmp_pca);
		}


		__ASM_VOLATILE_MEMORY__();
		success = FALSE;

		if ((cmd == job->cmd) && (slc_mode == job->slc_mode) && OPT_CHK_PCA_IN_SAME_UNIT(diff_bit_pca) && OPT_CHK_PCA_IN_SAME_DIE(diff_bit_rmp_pca, pca_rule)) {
			if (page == job->page) {
				success = TRUE;
			}
			else {
				/*
				 * it is next page of fsp: gather success, too
				 */
				job->page = page;
				job->lmu = lmu;

				success = TRUE;
			}
		}

		if (success) {
			job->cmd_cnt++;
			job->plane_vld |= plane_bit;

			if (COP0_JOB_CMD_PROG == job->cmd) {
				hal_optq_wait_lookup_q_convpage_valid();
				job->RS_parity |= M_OPT_CHK_LOOKUP_RAIDECC_PROGRAM_PARITY;
			}

			OPT_CRITICAL_ASSERT(ASSERT_PCA_ERROR | 0x7, (OPTQ_PROC_Q_LOOKUP_RMP_BPS != OPTQ_PROC_Q_LOOKUP_BBRMP_BPS));
			OPT_CRITICAL_ASSERT(ASSERT_PCA_ERROR | 0x8, (OPTQ_PROC_Q_LOOKUP_RMP_BPS != job->ubRMP_bypass));

			if (OPT_CHK_LOOKUP_END_PROGRAM_USERDEFINE) {
				/*
				 * end prog/erase
				 */

				/*
				 * last plane, done, can run macro, cache prev, can cache next
				 */

				if (job->plane_vld == first_job->plane_vld) {
					job->cache_prev = TRUE;
					job->cache_next = TRUE;
					status = OPT_NEW_GATHER_STATUS_SUCCESS;
					opt_dccm_log_debug(0xCCCCCCCC);
				}
				else {
					opt_dccm_log_debug(0x4444FFFF);//OPT_NEW_GATHER_STATUS_FAIL
					status = OPT_NEW_GATHER_STATUS_FAIL;

					OPTQB[OPTQB_LOOKUP_PTR_UPDATE] = first_job->cmd_cnt;  // 不能湊cache的prog, lookup ptr要往回到前面, 避免lookup ptr亂跳

					state = OPT_GATHER_STATE_WAIT_1ST_PLANE; // 1plane B接2p時, 若沒改回去的話, 等2p要做時會直接跑到1st_macro_wait_other_next_plane, 應該要重新從1st_macro_wait_1st_plane開始

					__ASM_VOLATILE_MEMORY__();
				}

			}
			else {
				finish = FALSE;
			}

			if (OPT_NEW_GATHER_STATUS_FAIL != status) {	// 沒有cache fail才將lookup ptr往後
				OPTQB[OPTQB_LOOKUP_PTR_UPDATE]++;
				__ASM_VOLATILE_MEMORY__();
			}
		}
		else {
			if ((COP0_JOB_CMD_ERASE == job->cmd) || job->slc_mode || (FSP_PAGE_CNT <= 1)) {	// erase, SLC program or non-TLC sample才可以斷
				/*
				 * last plane, done, can run macro, cache prev, can cache next
				 */

				job->cache_prev = TRUE;
				job->cache_next = TRUE;

				status = OPT_NEW_GATHER_STATUS_SUCCESS;
			}
			else {
				// ASSERT!!
				OPT_CRITICAL_ASSERT(ASSERT_FLOW_ERROR | 0x4, TRUE);
			}
		}
	}
	else if ((COP0_JOB_CMD_PROG == job->cmd) && cpu_comm->SeqProg) {
		status = OPT_NEW_GATHER_STATUS_INIT;
	}
	else if (lookup_state & OPTQB_LOOKUP_Q_TIMEOUT) {
		status = OPT_NEW_GATHER_STATUS_TIME_OUT;
		_WRITE_OPT_DCCM_PATH_DEBUG(TIMEOUT_OP(0x22));
		gubLastMixPlaneBMP[gOptStruct.cur_que] = job->plane_vld; // Gary add, update gubLastMixPlaneBMP to current plane_vld when time out
	}
	else {
		// there is a chance both lookup_valid and time-out are 0 for a small period
		status = OPT_NEW_GATHER_STATUS_INIT;
	}

	job->gather_state = state;
	job->gather_status = status;

	return finish;
}

inline void opt_set_macro_cmd(OPT_QUE_MGR_STRUCT_PTR que_mgr)
{
	OPT_JOB_STRUCT_PTR job;
	U8 job_cmd;
	U8 macro_cmd;

	job = &que_mgr->job_handle[que_mgr->tail_job];
	job_cmd = job->cmd;


	if (COP0_JOB_CMD_READ == job_cmd) {
		macro_cmd = OPT_MACRO_CMD_NORMAL_READ;
		job->macro_close_cmd = OPT_MACRO_CMD_CACHE_READ_END;
	}
	else if (COP0_JOB_CMD_ERASE == job_cmd) {
		macro_cmd = OPT_MACRO_CMD_ERASE;
	}
	else if (COP0_JOB_CMD_VENDER_CMD == job_cmd) {
		if (OPT_CHK_HEAD_DUMMY_CMD) {
			macro_cmd = OPT_MACRO_CMD_DUMMY_CMD;
		}
		else if (OPT_CHK_HEAD_GET_TEMPERATURE_CMD) {
			macro_cmd = OPT_MACRO_CMD_GET_TEMPERATURE_CMD;
		}
		else if (OPT_CHECK_HEAD_SYNC_OPEN_UNIT_CMD) {
			macro_cmd = OPT_MACRO_CMD_SYNC_OPEN_UNIT_CMD;
		}
#if (MicronFlashID4 == IM_N28A_ID4)
		else if (OPT_CHECK_HEAD_VALLEY_CHECK_CMD) {
			macro_cmd = OPT_MACRO_CMD_VALLEY_CHECK_CMD;
		}
#endif /* (MicronFlashID4 == IM_N28A_ID4) */
		else {
			macro_cmd = OPT_MACRO_CMD_VENDER_CMD;
		}
	}
	else if (COP0_JOB_CMD_IOR_NO_DMA == job_cmd) {
		macro_cmd = OPT_MACRO_CMD_IOR_NO_DMA;
	}
	else {   /*if (job_cmd == COP0_JOB_CMD_PROG)*/
		/*
		 * only 4-plane flash need split-prog
		 */
		macro_cmd = OPT_MACRO_CMD_D2_NORMAL_PROG;
		job->macro_close_cmd = OPT_MACRO_CMD_D2_CACHE_PROG_END;
	}

	macro_cmd += job->cache_next | (job->cache_prev << 1);

	// need to update lookup pointer for cache cmd since it was pointed to next cmd's next cmd now ?
	//!!!!!!!!

	job->macro_cmd = macro_cmd;
	job->macro_state = OPT_MACRO_CMD_STATE_INIT;
	_WRITE_OPT_DCCM_PATH_DEBUG(SET_CMD_OP(job->macro_cmd));
	_WRITE_OPT_DCCM_PCA_DEBUG(job->pca);
	_WRITE_OPT_DCCM_PCA_DEBUG(job->rmp_pca);
	_WRITE_OPT_DCCM_PATH_DEBUG(FORM_PLANE_INFO(job->plane_vld, 0));
}

BOOL opt_mt_group_ior_no_dma_cmd(OPT_JOB_STRUCT_PTR job)
{
#if IOR_EN
	U8 ubFIPCurrentGroupID;

	ubFIPCurrentGroupID = (R32_OPT_FCON[R32_OPT_FCON_IOR_CFG] & IDX_VAL_MASK);

	if (_chk_read_mtq_free_cnt(gOptStruct.cur_que, MT_1_MT_TRIG)) {
		if ((MT_TRIGGER_CNT_EMPTY != OPTQB[OPTQB_PROC_Q_MTQ_TRIG_CNT]) ||
			((ubFIPCurrentGroupID != job->ubMinGroupID) || (_chk_reserved_mtq_free_cnt(gOptStruct.cur_que, MT_1_MT_TRIG)))) {  	// non-current group read不能拿保留的resource
			job->ubReturn = TRUE;
			return FALSE;
		}
	}

	hal_optq_wait_curq_valid();
	OPTQW[OPTMTW_FORMMT] = OPTMTW_FORMMT_FROM_HEAD |
		OPTMTW_FORMMT_ATTR_SEL_TMP |
		OPTMTW_FORMMT_COPY_ONE_PLANE_FSA |
		OPTMTW_FORMMT_FSA0_FROM_HEAD |
		OPTMTW_FORMMT_DMA_MT_TRIG;
	hal_optmt_wait_form_mt();
	//---------- TRIGGER DATA ----------//
	mtd->dw0_dat.bits.par_rls = 1; //need to release pram after mt trigger.
	mtd->dw0_dat.bits.cq_atr_b1_cq_format = 0;

	M_SET_OPTCMB_TRIG_DATA_PCA_SEL(PCA_SEL_iFSA0_ENA);

	mtq->dw5.bits.fpu_ptr = FPU_OFFSET(gFpuEntryList.fpu_entry_dma_r_05_e0 + 10);
	mtq->dw3.bits.no_read_dma = 1;
	mtq->dw0.bits.busy = 0;

	__ASM_VOLATILE_MEMORY__();
	_WRITE_OPT_DCCM_PCA_DEBUG(mtq->dw10_iFSA0);
	hal_optmt_trigger();
	hal_optq_q_pop();
#endif
	return TRUE;
}

BOOL opt_macro_cmd_ior_no_dma_cmd(OPT_QUE_MGR_STRUCT_PTR que_mgr, OPT_JOB_STRUCT_PTR job)
{
#if IOR_EN
	if (OPT_MACRO_CMD_STATE_INIT == job->macro_state) {
		job->macro_state = OPT_MACRO_CMD_STATE_RUN_NON_CACHE_1;
	}

	if (OPT_MACRO_CMD_STATE_RUN_NON_CACHE_1 == job->macro_state) {
		if (opt_mt_group_ior_no_dma_cmd(job)) {
			job->macro_state = OPT_MACRO_CMD_STATE_DONE;

			return TRUE;
		}
	}
#endif
	return FALSE;
}

BOOL opt_mt_group_dummy_cmd(OPT_JOB_STRUCT_PTR job)
{
	if (OPTOtherCmdStopFormMTCheck(MT_1_MT_TRIG)) {
		return FALSE;
	}

	hal_optq_wait_curq_valid();
	OPTQW[OPTMTW_FORMMT] = OPTMTW_FORMMT_FROM_HEAD |
		OPTMTW_FORMMT_ATTR_SEL_TMP  |
		OPTMTW_FORMMT_COPY_ONE_PLANE_FSA |
		OPTMTW_FORMMT_FSA0_FROM_HEAD |
		OPTMTW_FORMMT_CMD_MT_TRIG;
	hal_optmt_wait_form_mt();

	mtq->dw5.bits.fpu_ptr = FPU_OFFSET(gFpuEntryList.fpu_entry_nop);
	mtq->dw0.bits.busy = 0;
	if (COP0_MT_RESOURCE_NOT_ENOUGH_WORKAROUND) {
		M_OPT_SET_MT_TEMPLATE_FIRST_OP();
	}

	// 避免ultra W/R開啟時, 導致這筆CQ比ultraDMA前面一筆先完成
	mtq->dw0.bits.ultra_w_en = 0;
	mtq->dw1.bits.dis_udma = 1;

	mtd->dw0_dat.all |= (OPTCM_TRIG_DATA_PAR_RLS | OPT_SET_TRIG_DATA_FSA0_VLD | OPTCM_TRIG_DATA_PCA_SEL_FSA0);

	//mtd->dw1_dat.bits.nor_cq_rsp = X; // Andes不特別設定, 由ARM決定要不要回CQ

	__ASM_VOLATILE_MEMORY__();

	hal_optmt_trigger();
	hal_optq_q_pop();

	return TRUE;
}

BOOL opt_macro_cmd_dummy_cmd(OPT_QUE_MGR_STRUCT_PTR que_mgr, OPT_JOB_STRUCT_PTR job)
{
	if (OPT_MACRO_CMD_STATE_INIT == job->macro_state) {
		job->macro_state = OPT_MACRO_CMD_STATE_RUN_NON_CACHE_1;
	}

	if (OPT_MACRO_CMD_STATE_RUN_NON_CACHE_1 == job->macro_state) {
		if (opt_mt_group_dummy_cmd(job)) {
			job->macro_state = OPT_MACRO_CMD_STATE_DONE;

			return TRUE;
		}
	}

	return FALSE;
}

BOOL opt_mt_group_get_temperature_cmd(OPT_JOB_STRUCT_PTR job)
{
	if (_chk_other_cmd_mtq_free_cnt(gOptStruct.cur_que, MT_2_MT_TRIG)) {
		if ((MT_TRIGGER_CNT_EMPTY != OPTQB[OPTQB_PROC_Q_MTQ_TRIG_CNT]) || (_chk_reserved_mtq_free_cnt(gOptStruct.cur_que, MT_2_MT_TRIG))) {
			return FALSE;
		}
	}

	/* issue temperature cmd */
	hal_optq_wait_curq_valid();
	OPTQW[OPTMTW_FORMMT] = OPTMTW_FORMMT_FROM_HEAD |
		OPTMTW_FORMMT_ATTR_SEL_TMP  |
		OPTMTW_FORMMT_COPY_ONE_PLANE_FSA |
		OPTMTW_FORMMT_FSA0_FROM_HEAD |
		OPTMTW_FORMMT_CMD_MT_TRIG;
	hal_optmt_wait_form_mt();

	mtq->dw5.bits.fpu_ptr = FPU_OFFSET(gFpuEntryList.fpu_entry_getfeature_temperature_die0);

	// 避免ultra W/R開啟時, 導致這筆CQ比ultraDMA前面一筆先完成
	mtq->dw0.bits.ultra_w_en = FALSE;
	mtq->dw1.bits.dis_udma = TRUE;
	// <- E17_porting
	mtd->dw0_dat.all |= (/*OPTCM_TRIG_DATA_FSA0_VLD |*/ OPTCM_TRIG_DATA_PCA_SEL_FSA0);
#if ((CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_TLC) || (CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_QLC))	//Reip Porting 3D-V7 QLC Add
	// **********-284
	mtq->dw0.bits.busy = FALSE;
	mtq->dw1.bits.mtq_dly_en = TRUE;
	mtd->dw3_dat.bits.mtq_dly = 1;
#endif
	// -> E17_porting
	mtd->dw0_dat.bits.par_rls = FALSE;
	mtd->dw2_mt_cfg1.bits.mtp_gro_pri_def = TRUE;  // cmd下完會讓CE busy的MT優先做 (high priority)

	mtd->dw1_dat.bits.nor_cq_rsp = FALSE;

	__ASM_VOLATILE_MEMORY__();

	hal_optmt_trigger();

	/* get temperature value */
	OPTQW[OPTMTW_FORMMT] = OPTMTW_FORMMT_FROM_HEAD  |
		OPTMTW_FORMMT_ATTR_SEL_TMP  |
		OPTMTW_FORMMT_COPY_ONE_PLANE_FSA |
		OPTMTW_FORMMT_FSA0_FROM_HEAD |
		OPTMTW_FORMMT_MULTIPLE_PCA_USE_BIT2_BIT3 |
		OPTMTW_FORMMT_DMA_MT_TRIG |
		OPTMTW_FORMMT_PAGE_VLD_SEL_0  |
		OPTMTW_FORMMT_HW_AUTOGEN_FRAME;
	hal_optmt_wait_form_mt();

	mtq->dw5.bits.fpu_ptr = FPU_OFFSET(gFpuEntryList.fpu_entry_getfeature_dma);
	mtq->dw0.bits.busy = FALSE;

	// for temperature cmd
	mtq->dw1.bits.conv_bps = TRUE;
	mtq->dw1.bits.ldpc_cor_en = FALSE;
	mtq->dw4.bits.crc_chk_dis = TRUE;
	mtq->dw9.bits.cmp_en = FALSE;

	mtd->dw0_dat.bits.par_rls = TRUE;
	mtd->dw0_dat.bits.cq_atr_b1_cq_format = CQ_ATR_B1_CQ_FORMAT_NOT_RD;
	mtd->dw0_dat.bits.pca_sel = PCA_SEL_iFSA0_ENA;

	//mtd->dw1_dat.bits.nor_cq_rsp = X; // Andes不特別設定, 由ARM決定要不要回CQ

	__ASM_VOLATILE_MEMORY__();

	hal_optmt_trigger();

	hal_optq_q_pop();

	return TRUE;
}

BOOL opt_macro_cmd_get_temperature_cmd(OPT_QUE_MGR_STRUCT_PTR que_mgr, OPT_JOB_STRUCT_PTR job)
{
	if (OPT_MACRO_CMD_STATE_INIT == job->macro_state) {
		job->macro_state = OPT_MACRO_CMD_STATE_RUN_NON_CACHE_1;
	}

	if (OPT_MACRO_CMD_STATE_RUN_NON_CACHE_1 == job->macro_state) {
		if (opt_mt_group_get_temperature_cmd(job)) {
			job->macro_state = OPT_MACRO_CMD_STATE_DONE;

			return TRUE;
		}
	}

	return FALSE;
}
__attribute__((optimize("Os")))
BOOL opt_mt_group_sync_open_unit_cmd(OPT_JOB_STRUCT_PTR job)
{
	U8 ubPlaneBankIdx, ubPlaneBankOffset, ubSyncUnitType, ubPollingSequenceSelect;
	U16 uwUnitByVCA, uwPageByVCA, uwFPU;
	U32 ulPCA;

	if (OPTOtherCmdStopFormMTCheck(MT_1_MT_TRIG)) {
		return FALSE;
	}

	hal_optq_wait_curq_valid();
	OPTQW[OPTMTW_FORMMT] = OPTMTW_FORMMT_FROM_HEAD |
		OPTMTW_FORMMT_ATTR_SEL_TMP  |
		OPTMTW_FORMMT_COPY_ONE_PLANE_FSA |
		OPTMTW_FORMMT_FSA0_FROM_HEAD |
		OPTMTW_FORMMT_CMD_MT_TRIG;

	ulPCA = OPTQL[OPTQ_PROC_Q_HEAD_PCA];

	if (OPTQ_PROC_Q_HEAD_USRDEF_INFO & COP0_USERDEFINE_MICRON_SYNC_OPEN_UNIT) {
		ubSyncUnitType = OPEN_GR_UNIT;
	}
	else if (OPTQ_PROC_Q_HEAD_USRDEF_INFO & COP0_USERDEFINE_MICRON_SYNC_OPEN_SPOR_OLDGRUNIT) {
		ubSyncUnitType = OPEN_SPOR_UNIT;
	}
	else {
		ubSyncUnitType = OPEN_GCGR_UNIT;
	}

	OPT_CRITICAL_ASSERT(ASSERT_WORDLINE_BYPASS_ERROR | 0x0, OPTQ_PROC_Q_HEAD_SLC_MODE);
#if(OPT_CATEGORY_CUSTOMER == CUSTOMER_MICRON)
	if (cpu_comm->micron_info.Flag.ubAll & BIT(ubSyncUnitType)) {
		if (FW_INVALID_UNIT == cpu_comm->micron_info.uwNextOpenUnit[ubSyncUnitType]) {
			uwUnitByVCA = FW_INVALID_UNIT;
		}
		else {
			uwUnitByVCA = (gOptStruct.ulPCAMaskFWUnit & ulPCA) >> gOptStruct.ubPCAMaskFWUnitShift;
		}
		OPT_CRITICAL_ASSERT(ASSERT_WORDLINE_BYPASS_ERROR | 0x1, uwUnitByVCA != cpu_comm->micron_info.uwNextOpenUnit[ubSyncUnitType]);
		OPT_CRITICAL_ASSERT(ASSERT_WORDLINE_BYPASS_ERROR | 0x2, 0 != gubUnreachLastPagePlaneNum[ubSyncUnitType]); //前一個必須寫完
		cpu_comm->micron_info.uwOpenUnit[ubSyncUnitType] = cpu_comm->micron_info.uwNextOpenUnit[ubSyncUnitType];
		cpu_comm->micron_info.uwNextOpenUnit[ubSyncUnitType] = INVALID_UNIT;
		cpu_comm->micron_info.Flag.ubAll &= ~ BIT(ubSyncUnitType);
		if (ubSyncUnitType != OPEN_SPOR_UNIT) {
			gubUnreachLastPagePlaneNum[ubSyncUnitType] = gubTotalPlaneBank;
		}
		//Reset LastProgramPlaneIndex
		for (ubPlaneBankIdx = 0; ubPlaneBankIdx < gubTotalPlaneBank; ubPlaneBankIdx++) {
			// when Sync Unit is SPOR, need initialize it as largest page as first time to avoid scan spor read error.
			guwLastProgramPageInPlaneBank[ubSyncUnitType][ubPlaneBankIdx] = (ubSyncUnitType == OPEN_SPOR_UNIT) ? (cpu_comm->micron_info.uwD3PageSize - 1) : 0;
		}
	}
	if (FW_INVALID_UNIT == cpu_comm->micron_info.uwOpenUnit[ubSyncUnitType]) {
		uwPageByVCA = 0;
	}
	else {
		uwPageByVCA = (ulPCA >> COP0_PAGE_START_POINT(0))&BITMSK(COP0_PAGE_LENS(0), 0);
	}
	if (cpu_comm->micron_info.ubLUNForMoreUnitEn) {
		ubPlaneBankOffset = gOptStruct.cur_que * gubMaxPlane;
	}
	else {
		ubPlaneBankOffset = gOptStruct.cur_que * gubMaxPlane * gubMaxDie + gubMaxPlane * job->ubDie;
	}
	for (ubPlaneBankIdx = 0; ubPlaneBankIdx < gubMaxPlane; ubPlaneBankIdx++) {
		guwLastProgramPageInPlaneBank[ubSyncUnitType][ ubPlaneBankOffset + ubPlaneBankIdx] = uwPageByVCA;
	}
	if ((cpu_comm->micron_info.uwD3PageSize - 1) == uwPageByVCA) {
		gubUnreachLastPagePlaneNum[ubSyncUnitType] -= gubMaxPlane;  //Every PlaneBank only hit once!!
		if (0 == gubUnreachLastPagePlaneNum[ubSyncUnitType]) {
			cpu_comm->micron_info.uwOpenUnit[ubSyncUnitType] = INVALID_UNIT;
		}
	}
#endif /* (OPT_CATEGORY_CUSTOMER == CUSTOMER_MICRON) */
	hal_optmt_wait_form_mt();

	mtq->dw3.bits.upd_pol_seq = TRUE;
	mtq->dw0.bits.busy = TRUE;
	// 避免ultra W/R開啟時, 導致這筆CQ比ultraDMA前面一筆先完成
	mtq->dw0.bits.ultra_w_en = FALSE;
	mtq->dw1.bits.dis_udma = TRUE;

	mtd->dw0_dat.all |= (OPT_SET_TRIG_DATA_FSA0_VLD | OPTCM_TRIG_DATA_PCA_SEL_FSA0);
	mtd->dw0_dat.bits.par_rls = FALSE;
	mtd->dw2_mt_cfg1.bits.mtp_gro_pri_def = TRUE;  // cmd下完會讓CE busy的MT優先做 (high priority)

	uwFPU = FPU_OFFSET(gFpuEntryList.fpu_entry_addr_gen_die);
	ubPollingSequenceSelect = M_FPU_GET_POLL_SELECT_READ_STATUS_BUSY_20(M_OPT_GET_MTD_DIE_NUM());
	M_SET_OPTCM_FPU_POL_SEQ(uwFPU, ubPollingSequenceSelect);

	if (COP0_MT_RESOURCE_NOT_ENOUGH_WORKAROUND) {
		M_OPT_SET_MT_TEMPLATE_FIRST_OP();
	}
	__ASM_VOLATILE_MEMORY__();

	hal_optmt_trigger();
	hal_optq_q_pop();
	return TRUE;
}

BOOL opt_macro_cmd_sync_open_unit_cmd(OPT_QUE_MGR_STRUCT_PTR que_mgr, OPT_JOB_STRUCT_PTR job)
{
	if (OPT_MACRO_CMD_STATE_INIT == job->macro_state) {
		job->macro_state = OPT_MACRO_CMD_STATE_RUN_NON_CACHE_1;
	}

	if (OPT_MACRO_CMD_STATE_RUN_NON_CACHE_1 == job->macro_state) {
		if (opt_mt_group_sync_open_unit_cmd(job)) {
			job->macro_state = OPT_MACRO_CMD_STATE_DONE;

			return TRUE;
		}
	}

	return FALSE;
}

__attribute__((optimize("Os")))
BOOL opt_mt_group_valley_check_cmd(OPT_JOB_STRUCT_PTR job)
{
#if (MicronFlashID4 == IM_N28A_ID4)
	U8 ubCmdStep = 0, ubNeedPatch, ubBreak = FALSE, ubQueue = gOptStruct.cur_que;
	U8 ubWordLineBypassSelect, ubPollingSequenceSelect, ubValleyCheckOnly, ubOPTIdx = 0;
	U16 uwFPU;
	U32 ulFSA[MICRON_N28_MAX_PLANE_NUM - 1];

	hal_optq_wait_curq_valid();
	/* execute valley check procedure */
	ubValleyCheckOnly = (OPT_CHECK_HEAD_VALLEY_CHECK_CMD ? TRUE : FALSE);

	ubWordLineBypassSelect = GET_WORDLINEBYPASS_EXTRA_PAGE_OVERRIDE_STATE_QUEUE(ubQueue) ? WordLineBypasseXtraPageOverride : GET_WORDLINEBYPASS_STATE_QUEUE(ubQueue);

	if (ubValleyCheckOnly) {
		if (OPT_PROCESS_QUEUE_ELEMENT_CNT() < MICRON_N28_MAX_PLANE_NUM) {//Wait 4 plane valley check cmd.
			return FALSE;
		}
		if (VALLEY_CHECK_DEBUG_EN) {
			OPTQB[OPTQB_LOOKUP_PTR_UPDATE] = 3;
			hal_optq_wait_lookup_q_valid();
			OPT_CRITICAL_ASSERT(ASSERT_LOOKUP_PTR_ERROR | 0x6, ((OPT_CHECK_LOOKUP_VALLEY_CHECK_CMD) ? FALSE : TRUE));
		}
		OPTQB[OPTQB_LOOKUP_PTR_UPDATE] = 0;
	}
	hal_optq_wait_lookup_q_valid();

	ubNeedPatch = (OPT_CHECK_LOOKUP_ENABLE_PREREAD_CMD && (!OPT_CHECK_LOOKUP_DISABLE_PREREAD_CMD)); //Real Enable Pre-read

	__ASM_VOLATILE_MEMORY__();
	do {
		/* 0 for disable pre-read, 1 for enable pre-read, (2~7 for set valley check En + prog*4 + set valley Dis), 9 for patch */
		OPTQW[OPTMTW_FORMMT] = OPTMTW_FORMMT_FROM_LOOKUP |
			OPTMTW_FORMMT_ATTR_SEL_TMP	|
			OPTMTW_FORMMT_MULTIPLE_PCA_USE_BIT2_BIT3 |
			OPTMTW_FORMMT_COPY_ONE_PLANE_FSA |
			OPTMTW_FORMMT_FSA0_FROM_LOOKUP |
			OPTMTW_FORMMT_CMD_MT_TRIG;

		hal_optmt_wait_form_mt();

		ubPollingSequenceSelect = M_FPU_GET_POLL_SELECT_READ_STATUS_BUSY_20(M_OPT_GET_MTD_DIE_NUM());
		mtd->dw0_dat.all |= (OPTCM_TRIG_DATA_PCA_SEL_FSA0);
		mtd->dw0_dat.bits.par_rls = FALSE;
		mtd->dw2_mt_cfg1.all |= (OPTCM_TRIG_DATA_GRO_PRI_DEF | OPTCM_TRIG_DATA_MT_HARD_LOCK);
		mtq->dw0.all &= (OPTCM_MT_ULTRA_WRITE_DISABLE & OPTCM_MT_PFA_INT_DISABLE);
		mtq->dw1.all |= OPTCM_MT_ULTRA_DMA_DISABLE;

		if ((0 == ubCmdStep) || ubNeedPatch) { // Disable Pre-read 2 type and Enable Pre-read, and with Valley Check are CMDStep == 0 case, ubNeedPatch is for Valley Check case
			mtd->dw1_dat.bits.nor_cq_rsp = FALSE;
		}
		if (ubNeedPatch) { // Enable Preread and Valley Check will goto this flow.
			/* issue patch Word Line Bypass Cmd */
			CLEAR_PREREAD_DISABLE_STATE_QUEUE(ubQueue);
			uwFPU = guwWordLineBypass[WordLineBypassWithEnablePreRead][ubWordLineBypassSelect]; // 0 for enable preread
			if (ubValleyCheckOnly) {
				mtd->dw0_dat.bits.par_rls = TRUE;
			}
			mtd->dw2_mt_cfg1.all &= OPTCM_TRIG_DATA_MT_HARD_LOCK_CLEAR; // Valley Check only mode need clear hard lock.
			if (COP0_MT_RESOURCE_NOT_ENOUGH_WORKAROUND) {
				M_OPT_SET_MT_TEMPLATE_ALLOW_SWITCH();
			}
			ubBreak = TRUE;
		}
		else if (OPT_CHECK_LOOKUP_DISABLE_PREREAD_CMD) { // Disable pre-read 02/06 only goto this flow
			/* issue set feature disable pre-read Cmd */
			SET_PREREAD_DISABLE_STATE_QUEUE(ubQueue);
			uwFPU = guwWordLineBypass[(OPT_CHECK_LOOKUP_ENABLE_PREREAD_CMD ? WordLineBypassWithDisableLowerAndUpperPagePreRead : WordLineBypassWithDisableeXtraPagePreRead)][ubWordLineBypassSelect]; // 1 for disable preread
			if ((COP0_MT_RESOURCE_NOT_ENOUGH_WORKAROUND) && (FALSE == OPT_CHECK_LOOKUP_ENABLE_PREREAD_CMD)) {
				M_OPT_SET_MT_TEMPLATE_FIRST_OP();
			}
			ubBreak = TRUE;
		}
		else {
			/* issue Valley Check Program cmd 85h 5addr(Plane0) 11h 85h 5addr(Plane1) 11h 85h 5addr(Plane2) 11h 85h 5addr(Plane3) 10h*/
			switch (ubCmdStep) {
			case 0:
				uwFPU = guwValleyCheckCmd[0];
				if (COP0_MT_RESOURCE_NOT_ENOUGH_WORKAROUND) {
					M_OPT_SET_MT_TEMPLATE_FIRST_OP();
				}
				break;
			case 4:
				ubPollingSequenceSelect = M_FPU_GET_POLL_SELECT_READ_STATUS_70_BUSY_20_CURR_01(M_OPT_GET_MTD_DIE_NUM());
				mtq->dw0.bits.pfa_int_en = TRUE;
				ubNeedPatch = TRUE; /* Use Need Patch issue Set feature Valley Check Enable Cmd */
				mtq->dw12_iFSA1 = ulFSA[0];
				mtq->dw13_iFSA2 = ulFSA[1];
				mtq->dw14_iFSA3 = ulFSA[2];
				/* 0: issue Set feature Valley Check Enable Cmd. 1: issue Valley Check Program cmd 85h 5addr(Plane0) 11h 85h 5addr(Plane1) 11h 85h 5addr(Plane2) 11h 85h 5addr(Plane3) 10h*/
				uwFPU = guwValleyCheckCmd[1];
				break;
			default: // 1 to 3 used to get FSA
				mtd->dw0_dat.bits.par_rls = TRUE;
				uwFPU = FPU_OFFSET(gFpuEntryList.fpu_entry_nop);
				ulFSA[ubOPTIdx] = mtq->dw10_iFSA0;
				++ubOPTIdx;
				break;
			}
			++ubCmdStep;
		}
		mtq->dw5.all |= (uwFPU | M_SET_OPTCM_POLLING_SEQUENCE_SELECT(ubPollingSequenceSelect));
		hal_optmt_trigger();
		if (ubValleyCheckOnly) {
			OPTQB[OPTQB_LOOKUP_PTR_UPDATE] = ubOPTIdx;
			hal_optq_wait_lookup_q_valid();
		}
	} while (!ubBreak);
	if (ubValleyCheckOnly) { // Only Valley Check pop CMD other is from program
		for (ubOPTIdx = 0; ubOPTIdx < MICRON_N28_MAX_PLANE_NUM; ubOPTIdx++) {
			hal_optq_q_pop();
		}
	}
#endif /* (MicronFlashID4 == IM_N28A_ID4) */
	return TRUE;
}

BOOL opt_macro_cmd_valley_check_cmd(OPT_QUE_MGR_STRUCT_PTR que_mgr, OPT_JOB_STRUCT_PTR job)
{
#if (MicronFlashID4 == IM_N28A_ID4)
	if (OPT_MACRO_CMD_STATE_INIT == job->macro_state) {
		job->macro_state = OPT_MACRO_CMD_STATE_RUN_NON_CACHE_1;
	}

	if (OPT_MACRO_CMD_STATE_RUN_NON_CACHE_1 == job->macro_state) {
		if (OPTOtherCmdStopFormMTCheck(MT_1_MT_TRIG * ValleyCheckMTPResource)) { // for valley check flow SetFeature + 3*NOP + 4PlaneValleyCheck + SetFeature
			return FALSE;
		}
		if (opt_mt_group_valley_check_cmd(job)) {
			job->macro_state = OPT_MACRO_CMD_STATE_DONE;
			return TRUE;
		}
	}
	return FALSE;
#else
	return TRUE;
#endif /* (MicronFlashID4 == IM_N28A_ID4) */
}

__attribute__((optimize("Os")))
BOOL opt_mt_group_vender_cmd(OPT_JOB_STRUCT_PTR job)
{
	U32 user_def;
	U16 uwFPU;
	if (OPTOtherCmdStopFormMTCheck(MT_1_MT_TRIG)) {
		return FALSE;
	}

	hal_optq_wait_curq_valid();
	OPTQW[OPTMTW_FORMMT] = OPTMTW_FORMMT_FROM_HEAD |
		OPTMTW_FORMMT_ATTR_SEL_TMP |
		OPTMTW_FORMMT_COPY_ONE_PLANE_FSA |
		OPTMTW_FORMMT_FSA0_FROM_HEAD |
		OPTMTW_FORMMT_DMA_MT_TRIG;

	user_def = OPTQ_PROC_Q_HEAD_USRDEF_INFO;
	hal_optmt_wait_form_mt();

	switch (user_def) {
	case 0:
		uwFPU = FPU_OFFSET(gFpuEntryList.fpu_entry_prog_to_flash_cache);
#if (PS5021_EN || S17_EN)
		M_SET_OPTCM_POLLING_SEQUENCE_SELECT(M_FPU_GET_POLL_SELECT_READ_STATUS_BUSY_20(M_OPT_GET_MTD_DIE_NUM()));
		mtq->dw3.bits.upd_pol_seq = 1;
#endif /* (PS5021_EN || S17_EN) */
		break;

	case 1:
		uwFPU = FPU_OFFSET(gFpuEntryList.fpu_entry_read_from_flash_cache);
		mtq->dw0.bits.busy = 0;
		mtd->dw0_dat.bits.cq_atr_b1_cq_format = CQ_ATR_B1_CQ_FORMAT_RD;
		break;

	default:
#if (PS5021_EN || S17_EN)
		uwFPU = FPU_OFFSET(gFpuEntryList.fpu_entry_addr_gen_die);
#endif /* (PS5021_EN || S17_EN) */
		break;
	}

	__ASM_VOLATILE_MEMORY__();

	mtd->dw0_dat.all |= (OPTCM_TRIG_DATA_PAR_RLS | OPTCM_TRIG_DATA_PCA_SEL_FSA0);
	mtd->dw1_dat.bits.nor_cq_rsp = 1;
	mtq->dw5.all |= uwFPU;
	if (COP0_MT_RESOURCE_NOT_ENOUGH_WORKAROUND) {
		M_OPT_SET_MT_TEMPLATE_FIRST_OP();
	}
	hal_optmt_trigger();
	hal_optq_q_pop();

	return TRUE;
}

BOOL opt_macro_cmd_vender_cmd(OPT_QUE_MGR_STRUCT_PTR que_mgr, OPT_JOB_STRUCT_PTR job)
{
	if (OPT_MACRO_CMD_STATE_INIT == job->macro_state) {
		job->macro_state = OPT_MACRO_CMD_STATE_RUN_VENDER_CMD;
	}

	if (OPT_MACRO_CMD_STATE_RUN_VENDER_CMD == job->macro_state) {
		if (opt_mt_group_vender_cmd(job)) {
			job->macro_state = OPT_MACRO_CMD_STATE_DONE;

			return TRUE;
		}
	}

	return FALSE;
}

inline void opt_pop_job_handle(OPT_QUE_MGR_STRUCT_PTR que_mgr)
{
	que_mgr->head_job = (que_mgr->head_job + 1) & (OPT_JOB_HANDLE_CNT - 1);
}

inline void opt_push_job_handle(OPT_QUE_MGR_STRUCT_PTR que_mgr)
{
	que_mgr->tail_job = (que_mgr->tail_job + 1) & (OPT_JOB_HANDLE_CNT - 1);
}


#if(MICRON_140S)
inline void OptGetLastProgramPage(OPT_JOB_STRUCT_PTR job)
{
#if (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC)
	if (job->page < IM_N48_SECTION_1) {
		job->uwExpectProgramPage = job->page + 1;//MLC
	}
	else if (job->page < IM_N48_SECTION_2) {
		job->uwExpectProgramPage = job->page + 3;//QLC
	}
	else if (job->page < IM_N48_SECTION_3) {
		job->uwExpectProgramPage = job->page + 1;//MLC
	}
	else if (job->page < IM_N48_SECTION_4) {
		job->uwExpectProgramPage = job->page + 3;//QLC
	}
	else if (job->page < IM_N48_SECTION_5) {
		job->uwExpectProgramPage = job->page + 1;//MLC
	}
	else {
		OPT_CRITICAL_ASSERT(ASSERT_PCA_ERROR | 0xA, TRUE);
	}
#else
#if (OPT_CATEGORY_FLASH == FLASH_B47R_TLC)
	if (job->page < IM_B47R_SECTION_1) {
		job->uwExpectProgramPage = job->page;//SLC
	}
	else if (job->page < IM_B47R_SECTION_2) {
		job->uwExpectProgramPage = job->page + 2;//TLC
	}
	else if (job->page < IM_B47R_SECTION_3) {
		job->uwExpectProgramPage = job->page + 1;//MLC
	}
	else if (job->page < IM_B47R_SECTION_4) {
		job->uwExpectProgramPage = job->page + 2;//TLC
	}
	else if (job->page < IM_B47R_SECTION_5) {
		job->uwExpectProgramPage = job->page;//SLC
	}
	else {
		OPT_CRITICAL_ASSERT(ASSERT_PCA_ERROR | 0xA, TRUE);
	}
#elif (OPT_CATEGORY_FLASH == FLASH_B37R_TLC)
	if (job->page < IM_B37R_SECTION_1) {
		job->uwExpectProgramPage = job->page;//SLC
	}
	else if (job->page < IM_B37R_SECTION_2) {
		job->uwExpectProgramPage = job->page + 2;//TLC
	}
	else if (job->page < IM_B37R_SECTION_3) {
		job->uwExpectProgramPage = job->page + 1;//MLC
	}
	else if (job->page < IM_B37R_SECTION_4) {
		job->uwExpectProgramPage = job->page + 2;//TLC
	}
	else if (job->page < IM_B37R_SECTION_5) {
		job->uwExpectProgramPage = job->page;//SLC
	}
	else {
		OPT_CRITICAL_ASSERT(ASSERT_PCA_ERROR | 0xA, TRUE);
	}
#endif
	///opt_dccm_log_debug(job->pca);
	///opt_dccm_log_debug(job->rmp_pca);
	///opt_dccm_log_debug(job->ubExpectProgramPage<<16| job->page);
#endif/*(CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC)*/
}
#endif/*(MICRON_140S)*/

