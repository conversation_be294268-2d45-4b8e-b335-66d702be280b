<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<?fileVersion 4.0.0?><cproject storage_type_id="org.eclipse.cdt.core.XmlProjectDescriptionStorage">
	<storageModule moduleId="org.eclipse.cdt.core.settings">
		<cconfiguration id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301">
			<storageModule buildSystemId="org.eclipse.cdt.managedbuilder.core.configurationDataProvider" id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301" moduleId="org.eclipse.cdt.core.settings" name="PS5017_Hynix_V7">
				<externalSettings/>
				<extensions>
					<extension id="com.andestech.ide.cdt.managedbuilder.core.CROSS_GNU_ELF" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="org.eclipse.cdt.core.GmakeErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.CWDLocator" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GCCErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GASErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GLDErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
				</extensions>
			</storageModule>
			<storageModule moduleId="cdtBuildSystem" version="4.0.0">
				<configuration artifactExtension="adx" artifactName="${ProjName}" buildArtefactType="nds.buildArtefactType.exe" buildProperties="org.eclipse.cdt.build.core.buildType=org.eclipse.cdt.build.core.buildType.debug,org.eclipse.cdt.build.core.buildArtefactType=nds.buildArtefactType.exe" description="" errorParsers="org.eclipse.cdt.core.GmakeErrorParser;org.eclipse.cdt.core.CWDLocator;org.eclipse.cdt.core.GCCErrorParser;org.eclipse.cdt.core.GASErrorParser;org.eclipse.cdt.core.GLDErrorParser" id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301" name="PS5017_Hynix_V7" parent="config.nds32le-elf-mculib-v3m.exe.debug" postannouncebuildStep="" postbuildStep="" preannouncebuildStep="" prebuildStep="">
					<folderInfo id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301." name="/" resourcePath="">
						<toolChain errorParsers="" id="nds.nds32le-elf-mculib-v3m.exe.debug.1914739597" name="nds32le-elf-mculib-v3m" superClass="nds.nds32le-elf-mculib-v3m.exe.debug">
							<option id="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET.1880587793" name="RSE_TARGET" superClass="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET" value="rse://LOCALHOST/~toolchains/nds32le-elf-mculib-v3m" valueType="string"/>
							<targetPlatform binaryParser="com.andestech.ide.cdt.managedbuilder.core.CROSS_GNU_ELF" id="target.nds32le-elf-mculib-v3m.platform.exe.debug.1756572495" name="Debug Platform" superClass="target.nds32le-elf-mculib-v3m.platform.exe.debug"/>
							<builder buildPath="${workspace_loc:/ps5008_opt/Debug}" errorParsers="org.eclipse.cdt.core.GmakeErrorParser;org.eclipse.cdt.core.CWDLocator" id="target.nds32le-elf-mculib-v3m.builder.exe.debug.1292143416" keepEnvironmentInBuildfile="false" managedBuildOn="true" name="Andes Make Builder" parallelBuildOn="false" parallelizationNumber="-1" superClass="target.nds32le-elf-mculib-v3m.builder.exe.debug"/>
							<tool id="tool.nds32le-elf-mculib-v3m.archiver.base.1025790736" name="Andes Archiver" superClass="tool.nds32le-elf-mculib-v3m.archiver.base"/>
							<tool command="$(CROSS_COMPILE)gcc" commandLinePattern="${COMMAND} ${FLAGS} ${OUTPUT_FLAG} ${OUTPUT_PREFIX}${OUTPUT} ${INPUTS}" errorParsers="org.eclipse.cdt.core.GCCErrorParser" id="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.1972503159" name="Andes C Compiler" superClass="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug">
								<option id="nds32le-elf-mculib-v3m.c.compiler.exe.debug.option.debugging.level.122520094" name="Debug Level" superClass="nds32le-elf-mculib-v3m.c.compiler.exe.debug.option.debugging.level" value="nds.c.debugging.level.max" valueType="enumerated"/>
								<option id="nds.c.compiler.option.include.paths.1486870730" name="Include paths (-I)" superClass="nds.c.compiler.option.include.paths" valueType="includePath">
									<listOptionValue builtIn="false" value="&quot;..\opt\inc&quot;"/>
									<listOptionValue builtIn="false" value="&quot;..\opt&quot;"/>
									<listOptionValue builtIn="false" value="&quot;..\shr\inc&quot;"/>
									<listOptionValue builtIn="false" value="&quot;..\startup\inc&quot;"/>
								</option>
								<option id="nds32le-elf-mculib-v3m.c.compiler.exe.debug.option.optimization.level.1063658887" name="Optimization Level" superClass="nds32le-elf-mculib-v3m.c.compiler.exe.debug.option.optimization.level" value="nds.c.optimization.level.more" valueType="enumerated"/>
								<option id="nds.c.compiler.option.optimization.removesections.function.1545694439" name="Remove unused function sections (-ffunction-sections)" superClass="nds.c.compiler.option.optimization.removesections.function" value="true" valueType="boolean"/>
								<option id="nds.c.compiler.option.optimization.removesections.data.689784657" name="Remove unused data sections (-fdata-sections)" superClass="nds.c.compiler.option.optimization.removesections.data" value="true" valueType="boolean"/>
								<option id="nds.c.compiler.option.cmodel.1828138157" name="Code Model" superClass="nds.c.compiler.option.cmodel" value="nds.c.compiler.option.cmodel.medium" valueType="enumerated"/>
								<option id="nds.c.compiler.option.preprocessor.def.symbols.696746629" name="Defined symbols (-D)" superClass="nds.c.compiler.option.preprocessor.def.symbols" valueType="definedSymbols">
									<listOptionValue builtIn="false" value="CATEGORY_CUSTOMER=CUSTOMER_HYNIX"/>
									<listOptionValue builtIn="false" value="CATEGORY_FLASH=FLASH_HYNIX_V7_TLC"/>
									<listOptionValue builtIn="false" value="CATEGORY_CONTROLLER=CONTROLLER_PS5017"/>
								</option>
								<inputType id="tool.nds.c.compiler.input.186281490" superClass="tool.nds.c.compiler.input"/>
							</tool>
							<tool command="$(CROSS_COMPILE)gcc" commandLinePattern="${COMMAND} ${FLAGS} ${OUTPUT_FLAG} ${OUTPUT_PREFIX}${OUTPUT} ${INPUTS}" errorParsers="org.eclipse.cdt.core.GLDErrorParser" id="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug.419912460" name="Andes C Linker" superClass="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug">
								<option defaultValue="-O0" id="nds.c.link.option.optimization.level.1753121826" name="Optimization Level" superClass="nds.c.link.option.optimization.level" value="-O2" valueType="string"/>
								<option defaultValue="true" id="nds32le-elf-mculib-v3m.c.link.option.noshared.base.1748259525" name="No shared libraries (-static)" superClass="nds32le-elf-mculib-v3m.c.link.option.noshared.base" valueType="boolean"/>
								<option defaultValue="" id="nds32le-elf-mculib-v3m.c.link.exe.debug.option.ldscript.1358269676" name="Linker Script (-T)" superClass="nds32le-elf-mculib-v3m.c.link.exe.debug.option.ldscript" value="..\LinkerScript\ps5008_hw.ld" valueType="string"/>
								<option id="nds.c.linker.option.cmodel.1266613064" name="Code Model" superClass="nds.c.linker.option.cmodel" value="-mcmodel=medium" valueType="string"/>
								<option id="nds.c.link.option.map.901475267" name="Write a map file. (-Map)" superClass="nds.c.link.option.map" value="true" valueType="boolean"/>
								<option id="nds.c.link.option.virtualhosting.1655141690" name="Virtual Hosting (-mvh)" superClass="nds.c.link.option.virtualhosting" value="false" valueType="boolean"/>
								<option id="nds.c.link.option.nostart.500842" name="Do not use standard start files (-nostartfiles)" superClass="nds.c.link.option.nostart" value="true" valueType="boolean"/>
								<option id="nds.c.link.option.removesections.487456938" name="Remove unused sections (-ffunction-sections or -fdata-sections)" superClass="nds.c.link.option.removesections" value="true" valueType="boolean"/>
								<inputType id="tool.nds.c.linker.input.2003662232" superClass="tool.nds.c.linker.input">
									<additionalInput kind="additionalinputdependency" paths="$(USER_OBJS)"/>
									<additionalInput kind="additionalinput" paths="$(LIBS)"/>
								</inputType>
							</tool>
							<tool command="$(CROSS_COMPILE)as" commandLinePattern="${COMMAND} ${FLAGS} ${OUTPUT_FLAG} ${OUTPUT_PREFIX}${OUTPUT} ${INPUTS}" errorParsers="org.eclipse.cdt.core.GASErrorParser" id="tool.nds32le-elf-mculib-v3m.assembler.exe.debug.1087200191" name="Andes Assembler" superClass="tool.nds32le-elf-mculib-v3m.assembler.exe.debug">
								<option defaultValue="-g" id="nds.both.asm.option.flags.317293769" name="Assembler flags" superClass="nds.both.asm.option.flags" valueType="string"/>
								<inputType id="tool.nds.assembler.input.740758926" superClass="tool.nds.assembler.input"/>
							</tool>
							<tool command="$(CROSS_COMPILE)nm" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot; ${OUTPUT_FLAG} ${OUTPUT}" errorParsers="" id="tool.nds32le-elf-mculib-v3m.nm.exe.debug.931363266" name="NM (symbol listing)" superClass="tool.nds32le-elf-mculib-v3m.nm.exe.debug">
								<option id="nds.nm.option.sortsymbol.1727266768" name="Sort symbols numerically by address. (-n)" superClass="nds.nm.option.sortsymbol" value="true" valueType="boolean"/>
								<option id="nds.nm.option.printfilename.1429304201" name="Print name of the input file before every symbol. (-A)" superClass="nds.nm.option.printfilename" value="false" valueType="boolean"/>
							</tool>
							<tool command="$(CROSS_COMPILE)readelf" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot; ${OUTPUT_FLAG} ${OUTPUT}" errorParsers="" id="tool.nds32le-elf-mculib-v3m.readelf.exe.debug.1565125382" name="Readelf (ELF info listing)" superClass="tool.nds32le-elf-mculib-v3m.readelf.exe.debug"/>
							<tool command="$(CROSS_COMPILE)objdump" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot; ${OUTPUT_FLAG} ${OUTPUT}" errorParsers="" id="tool.nds32le-elf-mculib-v3m.objdump.exe.debug.998389990" name="Objdump (disassembly)" superClass="tool.nds32le-elf-mculib-v3m.objdump.exe.debug">
								<option id="nds.objdump.option.disable.1481337479" name="Disable. (Do not auto-generate output file.)" superClass="nds.objdump.option.disable" value="false" valueType="boolean"/>
								<option id="nds.objdump.option.linenumber.955286336" name="Include line numbers and filenames in output. (-l)" superClass="nds.objdump.option.linenumber" value="true" valueType="boolean"/>
								<option id="nds.objdump.option.otherflags.393971447" name="Other flags" superClass="nds.objdump.option.otherflags" value="-S" valueType="string"/>
							</tool>
							<tool command="$(CROSS_COMPILE)objcopy" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot; ${OUTPUT}" errorParsers="" id="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug.883024916" name="Objcopy (object content copy)" superClass="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug">
								<option id="nds.objcopy.option.disable.1395999153" name="Disable. (Do not auto-generate output file.)" superClass="nds.objcopy.option.disable" value="false" valueType="boolean"/>
								<option id="nds.objcopy.option.stripall.824963064" name="Remove all symbol and relocation information. (-S)" superClass="nds.objcopy.option.stripall" value="false" valueType="boolean"/>
								<option id="nds.objcopy.option.otherflags.1056207870" name="Other flags" superClass="nds.objcopy.option.otherflags" value="-R .flh_iram_fpu.data" valueType="string"/>
							</tool>
							<tool command="$(CROSS_COMPILE)size" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot;" errorParsers="" id="tool.nds32le-elf-mculib-v3m.size.exe.debug.1828137964" name="Size (section size listing)" superClass="tool.nds32le-elf-mculib-v3m.size.exe.debug"/>
							<tool command="$(ANDESIGHT_ROOT)/utils/nds_ldsag" commandLinePattern="${COMMAND} ${FLAGS} ${INPUTS} -o $(LDSAG_OUT)" errorParsers="" id="tool.nds32le-elf-mculib-v3m.ldsag.base.112260332" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.base"/>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.624789652" name="Andes C++ Compiler" superClass="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug">
								<option id="nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.option.debugging.level.1281788207" name="Debug Level" superClass="nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.option.debugging.level" value="nds.cpp.compiler.debugging.level.max" valueType="enumerated"/>
							</tool>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug.1780906243" name="Andes C++ Linker" superClass="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug">
								<option defaultValue="-O0" id="nds.cpp.link.option.optimization.level.686371289" name="Optimization Level" superClass="nds.cpp.link.option.optimization.level" valueType="string"/>
								<option defaultValue="true" id="nds32le-elf-mculib-v3m.cpp.link.option.noshared.base.598300593" name="No shared libraries (-static)" superClass="nds32le-elf-mculib-v3m.cpp.link.option.noshared.base" valueType="boolean"/>
							</tool>
							<tool command="$(ANDESIGHT_ROOT)/utils/nds_ldsag" commandLinePattern="${COMMAND} ${FLAGS} ${INPUTS} -o $(LDSAG_OUT)" errorParsers="" id="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug.645287824" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug"/>
						</toolChain>
					</folderInfo>
					<folderInfo id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.opt_master_only" name="/" resourcePath="opt_master_only">
						<toolChain errorParsers="" id="nds.nds32le-elf-mculib-v3m.exe.debug.1074400922" name="nds32le-elf-mculib-v3m" superClass="nds.nds32le-elf-mculib-v3m.exe.debug" unusedChildren="">
							<option id="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET.423936908.313979601.1887125436.1009016960.1709021572" name="RSE_TARGET" superClass="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET.423936908"/>
							<targetPlatform id="target.nds32le-elf-mculib-v3m.platform.exe.debug.1243554117" name="Debug Platform" superClass="target.nds32le-elf-mculib-v3m.platform.exe.debug"/>
							<tool id="tool.nds32le-elf-mculib-v3m.archiver.base.1451113987" name="Andes Archiver" superClass="tool.nds32le-elf-mculib-v3m.archiver.base.1025790736"/>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.1012147492" name="Andes C++ Compiler" superClass="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.624789652">
								<option id="nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.option.debugging.level.1163531502" name="Debug Level" superClass="nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.option.debugging.level" value="nds.cpp.compiler.debugging.level.max" valueType="enumerated"/>
							</tool>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug.1105776962" name="Andes C++ Linker" superClass="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug.1780906243">
								<option defaultValue="-O0" id="nds.cpp.link.option.optimization.level.1833217380" name="Optimization Level" superClass="nds.cpp.link.option.optimization.level" valueType="string"/>
								<option defaultValue="true" id="nds32le-elf-mculib-v3m.cpp.link.option.noshared.base.1683445025" name="No shared libraries (-static)" superClass="nds32le-elf-mculib-v3m.cpp.link.option.noshared.base" valueType="boolean"/>
							</tool>
							<tool errorParsers="org.eclipse.cdt.core.GCCErrorParser" id="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.1420744797" name="Andes C Compiler" superClass="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.1972503159">
								<inputType id="tool.nds.c.compiler.input.119893064" superClass="tool.nds.c.compiler.input"/>
							</tool>
							<tool id="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug.204724052" name="Andes C Linker" superClass="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug.419912460"/>
							<tool errorParsers="org.eclipse.cdt.core.GASErrorParser" id="tool.nds32le-elf-mculib-v3m.assembler.exe.debug.116685936" name="Andes Assembler" superClass="tool.nds32le-elf-mculib-v3m.assembler.exe.debug.1087200191">
								<inputType id="tool.nds.assembler.input.1324296081" superClass="tool.nds.assembler.input"/>
							</tool>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.nm.exe.debug.843854363" name="NM (symbol listing)" superClass="tool.nds32le-elf-mculib-v3m.nm.exe.debug.931363266"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.readelf.exe.debug.1923877579" name="Readelf (ELF info listing)" superClass="tool.nds32le-elf-mculib-v3m.readelf.exe.debug.1565125382"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.objdump.exe.debug.1983232582" name="Objdump (disassembly)" superClass="tool.nds32le-elf-mculib-v3m.objdump.exe.debug.998389990"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug.1432744732" name="Objcopy (object content copy)" superClass="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug.883024916"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.size.exe.debug.268822219" name="Size (section size listing)" superClass="tool.nds32le-elf-mculib-v3m.size.exe.debug.1828137964"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug.1206516328" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug.645287824"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.ldsag.base.226841195" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.base.112260332"/>
						</toolChain>
					</folderInfo>
					<sourceEntries>
						<entry excluding="opt_master_only|Src_KIC|shr/inc/shr_types.h" flags="VALUE_WORKSPACE_PATH" kind="sourcePath" name=""/>
					</sourceEntries>
				</configuration>
			</storageModule>
			<storageModule moduleId="org.eclipse.cdt.core.externalSettings"/>
		</cconfiguration>
		<cconfiguration id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********">
			<storageModule buildSystemId="org.eclipse.cdt.managedbuilder.core.configurationDataProvider" id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********" moduleId="org.eclipse.cdt.core.settings" name="PS5017_BISC5">
				<externalSettings/>
				<extensions>
					<extension id="com.andestech.ide.cdt.managedbuilder.core.CROSS_GNU_ELF" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="org.eclipse.cdt.core.GmakeErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.CWDLocator" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GCCErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GASErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GLDErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
				</extensions>
			</storageModule>
			<storageModule moduleId="cdtBuildSystem" version="4.0.0">
				<configuration artifactExtension="adx" artifactName="${ProjName}" buildArtefactType="nds.buildArtefactType.exe" buildProperties="org.eclipse.cdt.build.core.buildType=org.eclipse.cdt.build.core.buildType.debug,org.eclipse.cdt.build.core.buildArtefactType=nds.buildArtefactType.exe" description="" errorParsers="org.eclipse.cdt.core.GmakeErrorParser;org.eclipse.cdt.core.CWDLocator;org.eclipse.cdt.core.GCCErrorParser;org.eclipse.cdt.core.GASErrorParser;org.eclipse.cdt.core.GLDErrorParser" id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********" name="PS5017_BISC5" parent="config.nds32le-elf-mculib-v3m.exe.debug" postannouncebuildStep="" postbuildStep="" preannouncebuildStep="" prebuildStep="">
					<folderInfo id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********." name="/" resourcePath="">
						<toolChain errorParsers="" id="nds.nds32le-elf-mculib-v3m.exe.debug.1733884099" name="nds32le-elf-mculib-v3m" superClass="nds.nds32le-elf-mculib-v3m.exe.debug">
							<option id="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET.683971888" name="RSE_TARGET" superClass="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET" value="rse://LOCALHOST/~toolchains/nds32le-elf-mculib-v3m" valueType="string"/>
							<targetPlatform binaryParser="com.andestech.ide.cdt.managedbuilder.core.CROSS_GNU_ELF" id="target.nds32le-elf-mculib-v3m.platform.exe.debug.501789045" name="Debug Platform" superClass="target.nds32le-elf-mculib-v3m.platform.exe.debug"/>
							<builder buildPath="${workspace_loc:/ps5008_opt/Debug}" errorParsers="org.eclipse.cdt.core.GmakeErrorParser;org.eclipse.cdt.core.CWDLocator" id="target.nds32le-elf-mculib-v3m.builder.exe.debug.778739416" keepEnvironmentInBuildfile="false" managedBuildOn="true" name="Andes Make Builder" parallelBuildOn="false" parallelizationNumber="-1" superClass="target.nds32le-elf-mculib-v3m.builder.exe.debug"/>
							<tool id="tool.nds32le-elf-mculib-v3m.archiver.base.1881986643" name="Andes Archiver" superClass="tool.nds32le-elf-mculib-v3m.archiver.base"/>
							<tool command="$(CROSS_COMPILE)gcc" commandLinePattern="${COMMAND} ${FLAGS} ${OUTPUT_FLAG} ${OUTPUT_PREFIX}${OUTPUT} ${INPUTS}" errorParsers="org.eclipse.cdt.core.GCCErrorParser" id="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.827151844" name="Andes C Compiler" superClass="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug">
								<option id="nds32le-elf-mculib-v3m.c.compiler.exe.debug.option.debugging.level.458271027" name="Debug Level" superClass="nds32le-elf-mculib-v3m.c.compiler.exe.debug.option.debugging.level" value="nds.c.debugging.level.max" valueType="enumerated"/>
								<option id="nds.c.compiler.option.include.paths.927103191" name="Include paths (-I)" superClass="nds.c.compiler.option.include.paths" valueType="includePath">
									<listOptionValue builtIn="false" value="&quot;..\opt\inc&quot;"/>
									<listOptionValue builtIn="false" value="&quot;..\opt&quot;"/>
									<listOptionValue builtIn="false" value="&quot;..\shr\inc&quot;"/>
									<listOptionValue builtIn="false" value="&quot;..\startup\inc&quot;"/>
								</option>
								<option id="nds32le-elf-mculib-v3m.c.compiler.exe.debug.option.optimization.level.1210517897" name="Optimization Level" superClass="nds32le-elf-mculib-v3m.c.compiler.exe.debug.option.optimization.level" value="nds.c.optimization.level.more" valueType="enumerated"/>
								<option id="nds.c.compiler.option.optimization.removesections.function.801795087" name="Remove unused function sections (-ffunction-sections)" superClass="nds.c.compiler.option.optimization.removesections.function" value="true" valueType="boolean"/>
								<option id="nds.c.compiler.option.optimization.removesections.data.1737223845" name="Remove unused data sections (-fdata-sections)" superClass="nds.c.compiler.option.optimization.removesections.data" value="true" valueType="boolean"/>
								<option id="nds.c.compiler.option.cmodel.503681945" name="Code Model" superClass="nds.c.compiler.option.cmodel" value="nds.c.compiler.option.cmodel.medium" valueType="enumerated"/>
								<option id="nds.c.compiler.option.preprocessor.def.symbols.133622906" name="Defined symbols (-D)" superClass="nds.c.compiler.option.preprocessor.def.symbols" valueType="definedSymbols">
									<listOptionValue builtIn="false" value="CATEGORY_CUSTOMER=CUSTOMER_SANDISK"/>
									<listOptionValue builtIn="false" value="CATEGORY_FLASH=FLASH_SANDISK_BICS5_TLC"/>
									<listOptionValue builtIn="false" value="CATEGORY_CONTROLLER=CONTROLLER_PS5017"/>
								</option>
								<inputType id="tool.nds.c.compiler.input.2142548829" superClass="tool.nds.c.compiler.input"/>
							</tool>
							<tool command="$(CROSS_COMPILE)gcc" commandLinePattern="${COMMAND} ${FLAGS} ${OUTPUT_FLAG} ${OUTPUT_PREFIX}${OUTPUT} ${INPUTS}" errorParsers="org.eclipse.cdt.core.GLDErrorParser" id="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug.1256607902" name="Andes C Linker" superClass="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug">
								<option defaultValue="-O0" id="nds.c.link.option.optimization.level.1542694390" name="Optimization Level" superClass="nds.c.link.option.optimization.level" value="-O2" valueType="string"/>
								<option defaultValue="true" id="nds32le-elf-mculib-v3m.c.link.option.noshared.base.1310650612" name="No shared libraries (-static)" superClass="nds32le-elf-mculib-v3m.c.link.option.noshared.base" valueType="boolean"/>
								<option defaultValue="" id="nds32le-elf-mculib-v3m.c.link.exe.debug.option.ldscript.1957593352" name="Linker Script (-T)" superClass="nds32le-elf-mculib-v3m.c.link.exe.debug.option.ldscript" value="..\LinkerScript\ps5008_hw.ld" valueType="string"/>
								<option id="nds.c.linker.option.cmodel.1875668398" name="Code Model" superClass="nds.c.linker.option.cmodel" value="-mcmodel=medium" valueType="string"/>
								<option id="nds.c.link.option.map.1265684814" name="Write a map file. (-Map)" superClass="nds.c.link.option.map" value="true" valueType="boolean"/>
								<option id="nds.c.link.option.virtualhosting.391709093" name="Virtual Hosting (-mvh)" superClass="nds.c.link.option.virtualhosting" value="false" valueType="boolean"/>
								<option id="nds.c.link.option.nostart.1301621678" name="Do not use standard start files (-nostartfiles)" superClass="nds.c.link.option.nostart" value="true" valueType="boolean"/>
								<option id="nds.c.link.option.removesections.1610175876" name="Remove unused sections (-ffunction-sections or -fdata-sections)" superClass="nds.c.link.option.removesections" value="true" valueType="boolean"/>
								<inputType id="tool.nds.c.linker.input.1067304317" superClass="tool.nds.c.linker.input">
									<additionalInput kind="additionalinputdependency" paths="$(USER_OBJS)"/>
									<additionalInput kind="additionalinput" paths="$(LIBS)"/>
								</inputType>
							</tool>
							<tool command="$(CROSS_COMPILE)as" commandLinePattern="${COMMAND} ${FLAGS} ${OUTPUT_FLAG} ${OUTPUT_PREFIX}${OUTPUT} ${INPUTS}" errorParsers="org.eclipse.cdt.core.GASErrorParser" id="tool.nds32le-elf-mculib-v3m.assembler.exe.debug.1698045143" name="Andes Assembler" superClass="tool.nds32le-elf-mculib-v3m.assembler.exe.debug">
								<option defaultValue="-g" id="nds.both.asm.option.flags.851690899" name="Assembler flags" superClass="nds.both.asm.option.flags" valueType="string"/>
								<inputType id="tool.nds.assembler.input.149049923" superClass="tool.nds.assembler.input"/>
							</tool>
							<tool command="$(CROSS_COMPILE)nm" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot; ${OUTPUT_FLAG} ${OUTPUT}" errorParsers="" id="tool.nds32le-elf-mculib-v3m.nm.exe.debug.503701275" name="NM (symbol listing)" superClass="tool.nds32le-elf-mculib-v3m.nm.exe.debug">
								<option id="nds.nm.option.sortsymbol.1951868648" name="Sort symbols numerically by address. (-n)" superClass="nds.nm.option.sortsymbol" value="true" valueType="boolean"/>
								<option id="nds.nm.option.printfilename.451017292" name="Print name of the input file before every symbol. (-A)" superClass="nds.nm.option.printfilename" value="false" valueType="boolean"/>
							</tool>
							<tool command="$(CROSS_COMPILE)readelf" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot; ${OUTPUT_FLAG} ${OUTPUT}" errorParsers="" id="tool.nds32le-elf-mculib-v3m.readelf.exe.debug.758154237" name="Readelf (ELF info listing)" superClass="tool.nds32le-elf-mculib-v3m.readelf.exe.debug"/>
							<tool command="$(CROSS_COMPILE)objdump" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot; ${OUTPUT_FLAG} ${OUTPUT}" errorParsers="" id="tool.nds32le-elf-mculib-v3m.objdump.exe.debug.1495289918" name="Objdump (disassembly)" superClass="tool.nds32le-elf-mculib-v3m.objdump.exe.debug">
								<option id="nds.objdump.option.disable.491568161" name="Disable. (Do not auto-generate output file.)" superClass="nds.objdump.option.disable" value="false" valueType="boolean"/>
								<option id="nds.objdump.option.linenumber.966867333" name="Include line numbers and filenames in output. (-l)" superClass="nds.objdump.option.linenumber" value="true" valueType="boolean"/>
								<option id="nds.objdump.option.otherflags.1809266673" name="Other flags" superClass="nds.objdump.option.otherflags" value="-S" valueType="string"/>
							</tool>
							<tool command="$(CROSS_COMPILE)objcopy" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot; ${OUTPUT}" errorParsers="" id="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug.111883809" name="Objcopy (object content copy)" superClass="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug">
								<option id="nds.objcopy.option.disable.1942044580" name="Disable. (Do not auto-generate output file.)" superClass="nds.objcopy.option.disable" value="false" valueType="boolean"/>
								<option id="nds.objcopy.option.stripall.178876624" name="Remove all symbol and relocation information. (-S)" superClass="nds.objcopy.option.stripall" value="false" valueType="boolean"/>
								<option id="nds.objcopy.option.otherflags.448858235" name="Other flags" superClass="nds.objcopy.option.otherflags" value="-R .flh_iram_fpu.data" valueType="string"/>
							</tool>
							<tool command="$(CROSS_COMPILE)size" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot;" errorParsers="" id="tool.nds32le-elf-mculib-v3m.size.exe.debug.739926459" name="Size (section size listing)" superClass="tool.nds32le-elf-mculib-v3m.size.exe.debug"/>
							<tool command="$(ANDESIGHT_ROOT)/utils/nds_ldsag" commandLinePattern="${COMMAND} ${FLAGS} ${INPUTS} -o $(LDSAG_OUT)" errorParsers="" id="tool.nds32le-elf-mculib-v3m.ldsag.base.1544018627" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.base"/>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.1806058080" name="Andes C++ Compiler" superClass="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug">
								<option id="nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.option.debugging.level.452916479" name="Debug Level" superClass="nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.option.debugging.level" value="nds.cpp.compiler.debugging.level.max" valueType="enumerated"/>
							</tool>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug.1590264643" name="Andes C++ Linker" superClass="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug">
								<option defaultValue="-O0" id="nds.cpp.link.option.optimization.level.1670940138" name="Optimization Level" superClass="nds.cpp.link.option.optimization.level" valueType="string"/>
								<option defaultValue="true" id="nds32le-elf-mculib-v3m.cpp.link.option.noshared.base.2011691000" name="No shared libraries (-static)" superClass="nds32le-elf-mculib-v3m.cpp.link.option.noshared.base" valueType="boolean"/>
							</tool>
							<tool command="$(ANDESIGHT_ROOT)/utils/nds_ldsag" commandLinePattern="${COMMAND} ${FLAGS} ${INPUTS} -o $(LDSAG_OUT)" errorParsers="" id="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug.1724978159" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug"/>
						</toolChain>
					</folderInfo>
					<folderInfo id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********.182389461" name="/" resourcePath="PS5017_BISC5">
						<toolChain id="nds.nds32le-elf-mculib-v3m.exe.debug.1111808482" name="nds32le-elf-mculib-v3m" superClass="nds.nds32le-elf-mculib-v3m.exe.debug" unusedChildren="">
							<option id="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET.683971888.2125547364" name="RSE_TARGET" superClass="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET.683971888"/>
							<targetPlatform id="target.nds32le-elf-mculib-v3m.platform.exe.debug" name="Debug Platform" superClass="target.nds32le-elf-mculib-v3m.platform.exe.debug"/>
							<tool id="tool.nds32le-elf-mculib-v3m.archiver.base.902969524" name="Andes Archiver" superClass="tool.nds32le-elf-mculib-v3m.archiver.base.1881986643"/>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.840480209" name="Andes C++ Compiler" superClass="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.1806058080"/>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug.1107269077" name="Andes C++ Linker" superClass="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug.1590264643"/>
							<tool id="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.681795810" name="Andes C Compiler" superClass="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.827151844">
								<option id="nds.c.compiler.option.preprocessor.def.symbols.1814300822" name="Defined symbols (-D)" superClass="nds.c.compiler.option.preprocessor.def.symbols" valueType="definedSymbols">
									<listOptionValue builtIn="false" value="CATEGORY_CUSTOMER=CUSTOMER_SANDISK"/>
									<listOptionValue builtIn="false" value="CATEGORY_FLASH=FLASH_SANDISK_BICS5_TLC"/>
									<listOptionValue builtIn="false" value="CATEGORY_CONTROLLER=CONTROLLER_PS5017"/>
								</option>
								<inputType id="tool.nds.c.compiler.input.226782767" superClass="tool.nds.c.compiler.input"/>
							</tool>
							<tool id="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug.837247208" name="Andes C Linker" superClass="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug.1256607902"/>
							<tool id="tool.nds32le-elf-mculib-v3m.assembler.exe.debug.834762155" name="Andes Assembler" superClass="tool.nds32le-elf-mculib-v3m.assembler.exe.debug.1698045143">
								<inputType id="tool.nds.assembler.input.1179359108" superClass="tool.nds.assembler.input"/>
							</tool>
							<tool id="tool.nds32le-elf-mculib-v3m.nm.exe.debug.865524284" name="NM (symbol listing)" superClass="tool.nds32le-elf-mculib-v3m.nm.exe.debug.503701275"/>
							<tool id="tool.nds32le-elf-mculib-v3m.readelf.exe.debug.1638575185" name="Readelf (ELF info listing)" superClass="tool.nds32le-elf-mculib-v3m.readelf.exe.debug.758154237"/>
							<tool id="tool.nds32le-elf-mculib-v3m.objdump.exe.debug.197089051" name="Objdump (disassembly)" superClass="tool.nds32le-elf-mculib-v3m.objdump.exe.debug.1495289918"/>
							<tool id="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug.192861542" name="Objcopy (object content copy)" superClass="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug.111883809"/>
							<tool id="tool.nds32le-elf-mculib-v3m.size.exe.debug.1634058049" name="Size (section size listing)" superClass="tool.nds32le-elf-mculib-v3m.size.exe.debug.739926459"/>
							<tool id="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug.783634200" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug.1724978159"/>
							<tool id="tool.nds32le-elf-mculib-v3m.ldsag.base.1720421764" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.base.1544018627"/>
						</toolChain>
					</folderInfo>
					<folderInfo id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********.opt_master_only" name="/" resourcePath="opt_master_only">
						<toolChain errorParsers="" id="nds.nds32le-elf-mculib-v3m.exe.debug.2022038378" name="nds32le-elf-mculib-v3m" superClass="nds.nds32le-elf-mculib-v3m.exe.debug" unusedChildren="">
							<option id="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET.423936908.313979601.1887125436.1009016960.1709021572.1412875331" name="RSE_TARGET" superClass="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET.423936908"/>
							<targetPlatform id="target.nds32le-elf-mculib-v3m.platform.exe.debug.1519382475" name="Debug Platform" superClass="target.nds32le-elf-mculib-v3m.platform.exe.debug"/>
							<tool id="tool.nds32le-elf-mculib-v3m.archiver.base.316466169" name="Andes Archiver" superClass="tool.nds32le-elf-mculib-v3m.archiver.base.1881986643"/>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.1651334428" name="Andes C++ Compiler" superClass="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.1806058080">
								<option id="nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.option.debugging.level.1069237635" name="Debug Level" superClass="nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.option.debugging.level" value="nds.cpp.compiler.debugging.level.max" valueType="enumerated"/>
							</tool>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug.1093835982" name="Andes C++ Linker" superClass="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug.1590264643">
								<option defaultValue="-O0" id="nds.cpp.link.option.optimization.level.698429035" name="Optimization Level" superClass="nds.cpp.link.option.optimization.level" valueType="string"/>
								<option defaultValue="true" id="nds32le-elf-mculib-v3m.cpp.link.option.noshared.base.793721127" name="No shared libraries (-static)" superClass="nds32le-elf-mculib-v3m.cpp.link.option.noshared.base" valueType="boolean"/>
							</tool>
							<tool errorParsers="org.eclipse.cdt.core.GCCErrorParser" id="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.1612613713" name="Andes C Compiler" superClass="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.827151844">
								<inputType id="tool.nds.c.compiler.input.2090576446" superClass="tool.nds.c.compiler.input"/>
							</tool>
							<tool id="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug.672781080" name="Andes C Linker" superClass="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug.1256607902"/>
							<tool errorParsers="org.eclipse.cdt.core.GASErrorParser" id="tool.nds32le-elf-mculib-v3m.assembler.exe.debug.2138246217" name="Andes Assembler" superClass="tool.nds32le-elf-mculib-v3m.assembler.exe.debug.1698045143">
								<inputType id="tool.nds.assembler.input.864135679" superClass="tool.nds.assembler.input"/>
							</tool>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.nm.exe.debug.1676011391" name="NM (symbol listing)" superClass="tool.nds32le-elf-mculib-v3m.nm.exe.debug.503701275"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.readelf.exe.debug.2067968127" name="Readelf (ELF info listing)" superClass="tool.nds32le-elf-mculib-v3m.readelf.exe.debug.758154237"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.objdump.exe.debug.404240376" name="Objdump (disassembly)" superClass="tool.nds32le-elf-mculib-v3m.objdump.exe.debug.1495289918"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug.97488225" name="Objcopy (object content copy)" superClass="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug.111883809"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.size.exe.debug.337572318" name="Size (section size listing)" superClass="tool.nds32le-elf-mculib-v3m.size.exe.debug.739926459"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug.216112092" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug.1724978159"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.ldsag.base.157057483" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.base.1544018627"/>
						</toolChain>
					</folderInfo>
					<folderInfo id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********.624963808" name="/" resourcePath="opt">
						<toolChain id="nds.nds32le-elf-mculib-v3m.exe.debug.2052703303" name="nds32le-elf-mculib-v3m" superClass="nds.nds32le-elf-mculib-v3m.exe.debug" unusedChildren="">
							<option id="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET.683971888.1815694159" name="RSE_TARGET" superClass="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET.683971888"/>
							<targetPlatform id="target.nds32le-elf-mculib-v3m.platform.exe.debug" name="Debug Platform" superClass="target.nds32le-elf-mculib-v3m.platform.exe.debug"/>
							<tool id="tool.nds32le-elf-mculib-v3m.archiver.base.253349685" name="Andes Archiver" superClass="tool.nds32le-elf-mculib-v3m.archiver.base.1881986643"/>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.1492077212" name="Andes C++ Compiler" superClass="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.1806058080"/>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug.1837362299" name="Andes C++ Linker" superClass="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug.1590264643"/>
							<tool id="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.1179133390" name="Andes C Compiler" superClass="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.827151844">
								<inputType id="tool.nds.c.compiler.input.462383104" superClass="tool.nds.c.compiler.input"/>
							</tool>
							<tool id="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug.1981459935" name="Andes C Linker" superClass="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug.1256607902"/>
							<tool id="tool.nds32le-elf-mculib-v3m.assembler.exe.debug.2060718499" name="Andes Assembler" superClass="tool.nds32le-elf-mculib-v3m.assembler.exe.debug.1698045143">
								<inputType id="tool.nds.assembler.input.1472575248" superClass="tool.nds.assembler.input"/>
							</tool>
							<tool id="tool.nds32le-elf-mculib-v3m.nm.exe.debug.293295506" name="NM (symbol listing)" superClass="tool.nds32le-elf-mculib-v3m.nm.exe.debug.503701275"/>
							<tool id="tool.nds32le-elf-mculib-v3m.readelf.exe.debug.2104567504" name="Readelf (ELF info listing)" superClass="tool.nds32le-elf-mculib-v3m.readelf.exe.debug.758154237"/>
							<tool id="tool.nds32le-elf-mculib-v3m.objdump.exe.debug.35434637" name="Objdump (disassembly)" superClass="tool.nds32le-elf-mculib-v3m.objdump.exe.debug.1495289918"/>
							<tool id="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug.1516834818" name="Objcopy (object content copy)" superClass="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug.111883809"/>
							<tool id="tool.nds32le-elf-mculib-v3m.size.exe.debug.1514319513" name="Size (section size listing)" superClass="tool.nds32le-elf-mculib-v3m.size.exe.debug.739926459"/>
							<tool id="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug.858166719" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug.1724978159"/>
							<tool id="tool.nds32le-elf-mculib-v3m.ldsag.base.337364853" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.base.1544018627"/>
						</toolChain>
					</folderInfo>
					<sourceEntries>
						<entry excluding="opt_master_only|Src_KIC|shr/inc/shr_types.h" flags="VALUE_WORKSPACE_PATH" kind="sourcePath" name=""/>
					</sourceEntries>
				</configuration>
			</storageModule>
			<storageModule moduleId="org.eclipse.cdt.core.externalSettings"/>
		</cconfiguration>
		<cconfiguration id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********.134906153">
			<storageModule buildSystemId="org.eclipse.cdt.managedbuilder.core.configurationDataProvider" id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********.134906153" moduleId="org.eclipse.cdt.core.settings" name="PS5017_BICS6">
				<externalSettings/>
				<extensions>
					<extension id="com.andestech.ide.cdt.managedbuilder.core.CROSS_GNU_ELF" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="org.eclipse.cdt.core.GmakeErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.CWDLocator" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GCCErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GASErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GLDErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
				</extensions>
			</storageModule>
			<storageModule moduleId="cdtBuildSystem" version="4.0.0">
				<configuration artifactExtension="adx" artifactName="${ProjName}" buildArtefactType="nds.buildArtefactType.exe" buildProperties="org.eclipse.cdt.build.core.buildType=org.eclipse.cdt.build.core.buildType.debug,org.eclipse.cdt.build.core.buildArtefactType=nds.buildArtefactType.exe" description="" errorParsers="org.eclipse.cdt.core.GmakeErrorParser;org.eclipse.cdt.core.CWDLocator;org.eclipse.cdt.core.GCCErrorParser;org.eclipse.cdt.core.GASErrorParser;org.eclipse.cdt.core.GLDErrorParser" id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********.134906153" name="PS5017_BICS6" parent="config.nds32le-elf-mculib-v3m.exe.debug" postannouncebuildStep="" postbuildStep="" preannouncebuildStep="" prebuildStep="">
					<folderInfo id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********.134906153." name="/" resourcePath="">
						<toolChain errorParsers="" id="nds.nds32le-elf-mculib-v3m.exe.debug.1387787612" name="nds32le-elf-mculib-v3m" superClass="nds.nds32le-elf-mculib-v3m.exe.debug">
							<option id="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET.1123643588" name="RSE_TARGET" superClass="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET" value="rse://LOCALHOST/~toolchains/nds32le-elf-mculib-v3m" valueType="string"/>
							<targetPlatform binaryParser="com.andestech.ide.cdt.managedbuilder.core.CROSS_GNU_ELF" id="target.nds32le-elf-mculib-v3m.platform.exe.debug.960464129" name="Debug Platform" superClass="target.nds32le-elf-mculib-v3m.platform.exe.debug"/>
							<builder buildPath="${workspace_loc:/ps5008_opt/Debug}" errorParsers="org.eclipse.cdt.core.GmakeErrorParser;org.eclipse.cdt.core.CWDLocator" id="target.nds32le-elf-mculib-v3m.builder.exe.debug.875367846" keepEnvironmentInBuildfile="false" managedBuildOn="true" name="Andes Make Builder" parallelBuildOn="false" parallelizationNumber="-1" superClass="target.nds32le-elf-mculib-v3m.builder.exe.debug"/>
							<tool id="tool.nds32le-elf-mculib-v3m.archiver.base.1266178970" name="Andes Archiver" superClass="tool.nds32le-elf-mculib-v3m.archiver.base"/>
							<tool command="$(CROSS_COMPILE)gcc" commandLinePattern="${COMMAND} ${FLAGS} ${OUTPUT_FLAG} ${OUTPUT_PREFIX}${OUTPUT} ${INPUTS}" errorParsers="org.eclipse.cdt.core.GCCErrorParser" id="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.1245150152" name="Andes C Compiler" superClass="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug">
								<option id="nds32le-elf-mculib-v3m.c.compiler.exe.debug.option.debugging.level.2064750641" name="Debug Level" superClass="nds32le-elf-mculib-v3m.c.compiler.exe.debug.option.debugging.level" value="nds.c.debugging.level.max" valueType="enumerated"/>
								<option id="nds.c.compiler.option.include.paths.1680496783" name="Include paths (-I)" superClass="nds.c.compiler.option.include.paths" valueType="includePath">
									<listOptionValue builtIn="false" value="&quot;..\opt\inc&quot;"/>
									<listOptionValue builtIn="false" value="&quot;..\opt&quot;"/>
									<listOptionValue builtIn="false" value="&quot;..\shr\inc&quot;"/>
									<listOptionValue builtIn="false" value="&quot;..\startup\inc&quot;"/>
								</option>
								<option id="nds32le-elf-mculib-v3m.c.compiler.exe.debug.option.optimization.level.958115610" name="Optimization Level" superClass="nds32le-elf-mculib-v3m.c.compiler.exe.debug.option.optimization.level" value="nds.c.optimization.level.more" valueType="enumerated"/>
								<option id="nds.c.compiler.option.optimization.removesections.function.972827438" name="Remove unused function sections (-ffunction-sections)" superClass="nds.c.compiler.option.optimization.removesections.function" value="true" valueType="boolean"/>
								<option id="nds.c.compiler.option.optimization.removesections.data.1043971459" name="Remove unused data sections (-fdata-sections)" superClass="nds.c.compiler.option.optimization.removesections.data" value="true" valueType="boolean"/>
								<option id="nds.c.compiler.option.cmodel.917356520" name="Code Model" superClass="nds.c.compiler.option.cmodel" value="nds.c.compiler.option.cmodel.medium" valueType="enumerated"/>
								<option id="nds.c.compiler.option.preprocessor.def.symbols.907867609" name="Defined symbols (-D)" superClass="nds.c.compiler.option.preprocessor.def.symbols" valueType="definedSymbols">
									<listOptionValue builtIn="false" value="CATEGORY_CUSTOMER=CUSTOMER_SANDISK"/>
									<listOptionValue builtIn="false" value="CATEGORY_FLASH=FLASH_SANDISK_BICS6_TLC"/>
									<listOptionValue builtIn="false" value="CATEGORY_CONTROLLER=CONTROLLER_PS5017"/>
								</option>
								<inputType id="tool.nds.c.compiler.input.147853853" superClass="tool.nds.c.compiler.input"/>
							</tool>
							<tool command="$(CROSS_COMPILE)gcc" commandLinePattern="${COMMAND} ${FLAGS} ${OUTPUT_FLAG} ${OUTPUT_PREFIX}${OUTPUT} ${INPUTS}" errorParsers="org.eclipse.cdt.core.GLDErrorParser" id="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug.1707562758" name="Andes C Linker" superClass="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug">
								<option defaultValue="-O0" id="nds.c.link.option.optimization.level.222696967" name="Optimization Level" superClass="nds.c.link.option.optimization.level" value="-O2" valueType="string"/>
								<option defaultValue="true" id="nds32le-elf-mculib-v3m.c.link.option.noshared.base.435214422" name="No shared libraries (-static)" superClass="nds32le-elf-mculib-v3m.c.link.option.noshared.base" valueType="boolean"/>
								<option defaultValue="" id="nds32le-elf-mculib-v3m.c.link.exe.debug.option.ldscript.1117708747" name="Linker Script (-T)" superClass="nds32le-elf-mculib-v3m.c.link.exe.debug.option.ldscript" value="..\LinkerScript\ps5008_hw.ld" valueType="string"/>
								<option id="nds.c.linker.option.cmodel.1103443512" name="Code Model" superClass="nds.c.linker.option.cmodel" value="-mcmodel=medium" valueType="string"/>
								<option id="nds.c.link.option.map.390691841" name="Write a map file. (-Map)" superClass="nds.c.link.option.map" value="true" valueType="boolean"/>
								<option id="nds.c.link.option.virtualhosting.1376246374" name="Virtual Hosting (-mvh)" superClass="nds.c.link.option.virtualhosting" value="false" valueType="boolean"/>
								<option id="nds.c.link.option.nostart.345390924" name="Do not use standard start files (-nostartfiles)" superClass="nds.c.link.option.nostart" value="true" valueType="boolean"/>
								<option id="nds.c.link.option.removesections.135585074" name="Remove unused sections (-ffunction-sections or -fdata-sections)" superClass="nds.c.link.option.removesections" value="true" valueType="boolean"/>
								<inputType id="tool.nds.c.linker.input.1489537376" superClass="tool.nds.c.linker.input">
									<additionalInput kind="additionalinputdependency" paths="$(USER_OBJS)"/>
									<additionalInput kind="additionalinput" paths="$(LIBS)"/>
								</inputType>
							</tool>
							<tool command="$(CROSS_COMPILE)as" commandLinePattern="${COMMAND} ${FLAGS} ${OUTPUT_FLAG} ${OUTPUT_PREFIX}${OUTPUT} ${INPUTS}" errorParsers="org.eclipse.cdt.core.GASErrorParser" id="tool.nds32le-elf-mculib-v3m.assembler.exe.debug.967803047" name="Andes Assembler" superClass="tool.nds32le-elf-mculib-v3m.assembler.exe.debug">
								<option defaultValue="-g" id="nds.both.asm.option.flags.1569397799" name="Assembler flags" superClass="nds.both.asm.option.flags" valueType="string"/>
								<inputType id="tool.nds.assembler.input.1272681676" superClass="tool.nds.assembler.input"/>
							</tool>
							<tool command="$(CROSS_COMPILE)nm" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot; ${OUTPUT_FLAG} ${OUTPUT}" errorParsers="" id="tool.nds32le-elf-mculib-v3m.nm.exe.debug.1467064680" name="NM (symbol listing)" superClass="tool.nds32le-elf-mculib-v3m.nm.exe.debug">
								<option id="nds.nm.option.sortsymbol.1307769432" name="Sort symbols numerically by address. (-n)" superClass="nds.nm.option.sortsymbol" value="true" valueType="boolean"/>
								<option id="nds.nm.option.printfilename.1711506470" name="Print name of the input file before every symbol. (-A)" superClass="nds.nm.option.printfilename" value="false" valueType="boolean"/>
							</tool>
							<tool command="$(CROSS_COMPILE)readelf" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot; ${OUTPUT_FLAG} ${OUTPUT}" errorParsers="" id="tool.nds32le-elf-mculib-v3m.readelf.exe.debug.1134038235" name="Readelf (ELF info listing)" superClass="tool.nds32le-elf-mculib-v3m.readelf.exe.debug"/>
							<tool command="$(CROSS_COMPILE)objdump" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot; ${OUTPUT_FLAG} ${OUTPUT}" errorParsers="" id="tool.nds32le-elf-mculib-v3m.objdump.exe.debug.1400357168" name="Objdump (disassembly)" superClass="tool.nds32le-elf-mculib-v3m.objdump.exe.debug">
								<option id="nds.objdump.option.disable.1835770175" name="Disable. (Do not auto-generate output file.)" superClass="nds.objdump.option.disable" value="false" valueType="boolean"/>
								<option id="nds.objdump.option.linenumber.1464929170" name="Include line numbers and filenames in output. (-l)" superClass="nds.objdump.option.linenumber" value="true" valueType="boolean"/>
								<option id="nds.objdump.option.otherflags.519346253" name="Other flags" superClass="nds.objdump.option.otherflags" value="-S" valueType="string"/>
							</tool>
							<tool command="$(CROSS_COMPILE)objcopy" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot; ${OUTPUT}" errorParsers="" id="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug.1628402834" name="Objcopy (object content copy)" superClass="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug">
								<option id="nds.objcopy.option.disable.2021405680" name="Disable. (Do not auto-generate output file.)" superClass="nds.objcopy.option.disable" value="false" valueType="boolean"/>
								<option id="nds.objcopy.option.stripall.237277134" name="Remove all symbol and relocation information. (-S)" superClass="nds.objcopy.option.stripall" value="false" valueType="boolean"/>
								<option id="nds.objcopy.option.otherflags.967375341" name="Other flags" superClass="nds.objcopy.option.otherflags" value="-R .flh_iram_fpu.data" valueType="string"/>
							</tool>
							<tool command="$(CROSS_COMPILE)size" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot;" errorParsers="" id="tool.nds32le-elf-mculib-v3m.size.exe.debug.965966949" name="Size (section size listing)" superClass="tool.nds32le-elf-mculib-v3m.size.exe.debug"/>
							<tool command="$(ANDESIGHT_ROOT)/utils/nds_ldsag" commandLinePattern="${COMMAND} ${FLAGS} ${INPUTS} -o $(LDSAG_OUT)" errorParsers="" id="tool.nds32le-elf-mculib-v3m.ldsag.base.1007425525" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.base"/>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.2074098597" name="Andes C++ Compiler" superClass="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug">
								<option id="nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.option.debugging.level.2375063" name="Debug Level" superClass="nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.option.debugging.level" value="nds.cpp.compiler.debugging.level.max" valueType="enumerated"/>
							</tool>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug.1041927909" name="Andes C++ Linker" superClass="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug">
								<option defaultValue="-O0" id="nds.cpp.link.option.optimization.level.153382396" name="Optimization Level" superClass="nds.cpp.link.option.optimization.level" valueType="string"/>
								<option defaultValue="true" id="nds32le-elf-mculib-v3m.cpp.link.option.noshared.base.1863764420" name="No shared libraries (-static)" superClass="nds32le-elf-mculib-v3m.cpp.link.option.noshared.base" valueType="boolean"/>
							</tool>
							<tool command="$(ANDESIGHT_ROOT)/utils/nds_ldsag" commandLinePattern="${COMMAND} ${FLAGS} ${INPUTS} -o $(LDSAG_OUT)" errorParsers="" id="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug.723722602" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug"/>
						</toolChain>
					</folderInfo>
					<folderInfo id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********.134906153.PS5017_BISC5" name="/" resourcePath="PS5017_BISC5">
						<toolChain id="nds.nds32le-elf-mculib-v3m.exe.debug.1666894895" name="nds32le-elf-mculib-v3m" superClass="nds.nds32le-elf-mculib-v3m.exe.debug" unusedChildren="">
							<option id="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET.683971888.2125547364.913307565" name="RSE_TARGET" superClass="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET.683971888"/>
							<targetPlatform id="target.nds32le-elf-mculib-v3m.platform.exe.debug.488833985" name="Debug Platform" superClass="target.nds32le-elf-mculib-v3m.platform.exe.debug"/>
							<tool id="tool.nds32le-elf-mculib-v3m.archiver.base.456753424" name="Andes Archiver" superClass="tool.nds32le-elf-mculib-v3m.archiver.base.1266178970"/>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.1931958358" name="Andes C++ Compiler" superClass="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.2074098597"/>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug.1217762715" name="Andes C++ Linker" superClass="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug.1041927909"/>
							<tool id="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.1410119354" name="Andes C Compiler" superClass="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.1245150152">
								<option id="nds.c.compiler.option.preprocessor.def.symbols.1354555446" name="Defined symbols (-D)" superClass="nds.c.compiler.option.preprocessor.def.symbols" valueType="definedSymbols">
									<listOptionValue builtIn="false" value="CATEGORY_CUSTOMER=CUSTOMER_SANDISK"/>
									<listOptionValue builtIn="false" value="CATEGORY_FLASH=FLASH_SANDISK_BICS5_TLC"/>
									<listOptionValue builtIn="false" value="CATEGORY_CONTROLLER=CONTROLLER_PS5017"/>
								</option>
								<inputType id="tool.nds.c.compiler.input.486545523" superClass="tool.nds.c.compiler.input"/>
							</tool>
							<tool id="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug.372107534" name="Andes C Linker" superClass="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug.1707562758"/>
							<tool id="tool.nds32le-elf-mculib-v3m.assembler.exe.debug.1723566330" name="Andes Assembler" superClass="tool.nds32le-elf-mculib-v3m.assembler.exe.debug.967803047">
								<inputType id="tool.nds.assembler.input.1055984205" superClass="tool.nds.assembler.input"/>
							</tool>
							<tool id="tool.nds32le-elf-mculib-v3m.nm.exe.debug.987384568" name="NM (symbol listing)" superClass="tool.nds32le-elf-mculib-v3m.nm.exe.debug.1467064680"/>
							<tool id="tool.nds32le-elf-mculib-v3m.readelf.exe.debug.917645583" name="Readelf (ELF info listing)" superClass="tool.nds32le-elf-mculib-v3m.readelf.exe.debug.1134038235"/>
							<tool id="tool.nds32le-elf-mculib-v3m.objdump.exe.debug.548384151" name="Objdump (disassembly)" superClass="tool.nds32le-elf-mculib-v3m.objdump.exe.debug.1400357168"/>
							<tool id="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug.863084096" name="Objcopy (object content copy)" superClass="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug.1628402834"/>
							<tool id="tool.nds32le-elf-mculib-v3m.size.exe.debug.1236161387" name="Size (section size listing)" superClass="tool.nds32le-elf-mculib-v3m.size.exe.debug.965966949"/>
							<tool id="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug.2141785112" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug.723722602"/>
							<tool id="tool.nds32le-elf-mculib-v3m.ldsag.base.2022944163" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.base.1007425525"/>
						</toolChain>
					</folderInfo>
					<folderInfo id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********.134906153.opt_master_only" name="/" resourcePath="opt_master_only">
						<toolChain errorParsers="" id="nds.nds32le-elf-mculib-v3m.exe.debug.779992823" name="nds32le-elf-mculib-v3m" superClass="nds.nds32le-elf-mculib-v3m.exe.debug" unusedChildren="">
							<option id="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET.423936908.313979601.1887125436.1009016960.1709021572.1412875331.1151677017" name="RSE_TARGET" superClass="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET.423936908"/>
							<targetPlatform id="target.nds32le-elf-mculib-v3m.platform.exe.debug.1856707549" name="Debug Platform" superClass="target.nds32le-elf-mculib-v3m.platform.exe.debug"/>
							<tool id="tool.nds32le-elf-mculib-v3m.archiver.base.939380035" name="Andes Archiver" superClass="tool.nds32le-elf-mculib-v3m.archiver.base.1266178970"/>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.1818514318" name="Andes C++ Compiler" superClass="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.2074098597">
								<option id="nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.option.debugging.level.200212929" name="Debug Level" superClass="nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.option.debugging.level" value="nds.cpp.compiler.debugging.level.max" valueType="enumerated"/>
							</tool>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug.1772455601" name="Andes C++ Linker" superClass="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug.1041927909">
								<option defaultValue="-O0" id="nds.cpp.link.option.optimization.level.1709363365" name="Optimization Level" superClass="nds.cpp.link.option.optimization.level" valueType="string"/>
								<option defaultValue="true" id="nds32le-elf-mculib-v3m.cpp.link.option.noshared.base.232301648" name="No shared libraries (-static)" superClass="nds32le-elf-mculib-v3m.cpp.link.option.noshared.base" valueType="boolean"/>
							</tool>
							<tool errorParsers="org.eclipse.cdt.core.GCCErrorParser" id="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.1254129268" name="Andes C Compiler" superClass="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.1245150152">
								<inputType id="tool.nds.c.compiler.input.1210983209" superClass="tool.nds.c.compiler.input"/>
							</tool>
							<tool id="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug.1492475907" name="Andes C Linker" superClass="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug.1707562758"/>
							<tool errorParsers="org.eclipse.cdt.core.GASErrorParser" id="tool.nds32le-elf-mculib-v3m.assembler.exe.debug.1358323679" name="Andes Assembler" superClass="tool.nds32le-elf-mculib-v3m.assembler.exe.debug.967803047">
								<inputType id="tool.nds.assembler.input.1523149544" superClass="tool.nds.assembler.input"/>
							</tool>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.nm.exe.debug.2074184190" name="NM (symbol listing)" superClass="tool.nds32le-elf-mculib-v3m.nm.exe.debug.1467064680"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.readelf.exe.debug.1394655535" name="Readelf (ELF info listing)" superClass="tool.nds32le-elf-mculib-v3m.readelf.exe.debug.1134038235"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.objdump.exe.debug.362408789" name="Objdump (disassembly)" superClass="tool.nds32le-elf-mculib-v3m.objdump.exe.debug.1400357168"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug.2019115106" name="Objcopy (object content copy)" superClass="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug.1628402834"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.size.exe.debug.1408133687" name="Size (section size listing)" superClass="tool.nds32le-elf-mculib-v3m.size.exe.debug.965966949"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug.1710852036" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug.723722602"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.ldsag.base.13744592" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.base.1007425525"/>
						</toolChain>
					</folderInfo>
					<sourceEntries>
						<entry excluding="opt_master_only|Src_KIC|shr/inc/shr_types.h" flags="VALUE_WORKSPACE_PATH" kind="sourcePath" name=""/>
					</sourceEntries>
				</configuration>
			</storageModule>
			<storageModule moduleId="org.eclipse.cdt.core.externalSettings"/>
		</cconfiguration>
		<cconfiguration id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********">
			<storageModule buildSystemId="org.eclipse.cdt.managedbuilder.core.configurationDataProvider" id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********" moduleId="org.eclipse.cdt.core.settings" name="PS5017_YMTC_TAS">
				<externalSettings/>
				<extensions>
					<extension id="com.andestech.ide.cdt.managedbuilder.core.CROSS_GNU_ELF" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="org.eclipse.cdt.core.GmakeErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.CWDLocator" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GCCErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GASErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GLDErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
				</extensions>
			</storageModule>
			<storageModule moduleId="cdtBuildSystem" version="4.0.0">
				<configuration artifactExtension="adx" artifactName="${ProjName}" buildArtefactType="nds.buildArtefactType.exe" buildProperties="org.eclipse.cdt.build.core.buildType=org.eclipse.cdt.build.core.buildType.debug,org.eclipse.cdt.build.core.buildArtefactType=nds.buildArtefactType.exe" description="" errorParsers="org.eclipse.cdt.core.GmakeErrorParser;org.eclipse.cdt.core.CWDLocator;org.eclipse.cdt.core.GCCErrorParser;org.eclipse.cdt.core.GASErrorParser;org.eclipse.cdt.core.GLDErrorParser" id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********" name="PS5017_YMTC_TAS" parent="config.nds32le-elf-mculib-v3m.exe.debug" postannouncebuildStep="" postbuildStep="" preannouncebuildStep="" prebuildStep="">
					<folderInfo id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********." name="/" resourcePath="">
						<toolChain errorParsers="" id="nds.nds32le-elf-mculib-v3m.exe.debug.1343861581" name="nds32le-elf-mculib-v3m" superClass="nds.nds32le-elf-mculib-v3m.exe.debug">
							<option id="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET.1599934830" name="RSE_TARGET" superClass="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET" value="rse://LOCALHOST/~toolchains/nds32le-elf-mculib-v3m" valueType="string"/>
							<targetPlatform binaryParser="com.andestech.ide.cdt.managedbuilder.core.CROSS_GNU_ELF" id="target.nds32le-elf-mculib-v3m.platform.exe.debug.805427345" name="Debug Platform" superClass="target.nds32le-elf-mculib-v3m.platform.exe.debug"/>
							<builder buildPath="${workspace_loc:/ps5008_opt/Debug}" errorParsers="org.eclipse.cdt.core.GmakeErrorParser;org.eclipse.cdt.core.CWDLocator" id="target.nds32le-elf-mculib-v3m.builder.exe.debug.352813004" keepEnvironmentInBuildfile="false" managedBuildOn="true" name="Andes Make Builder" parallelBuildOn="false" parallelizationNumber="-1" superClass="target.nds32le-elf-mculib-v3m.builder.exe.debug"/>
							<tool id="tool.nds32le-elf-mculib-v3m.archiver.base.1048064578" name="Andes Archiver" superClass="tool.nds32le-elf-mculib-v3m.archiver.base"/>
							<tool command="$(CROSS_COMPILE)gcc" commandLinePattern="${COMMAND} ${FLAGS} ${OUTPUT_FLAG} ${OUTPUT_PREFIX}${OUTPUT} ${INPUTS}" errorParsers="org.eclipse.cdt.core.GCCErrorParser" id="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.1999229441" name="Andes C Compiler" superClass="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug">
								<option id="nds32le-elf-mculib-v3m.c.compiler.exe.debug.option.debugging.level.84493901" name="Debug Level" superClass="nds32le-elf-mculib-v3m.c.compiler.exe.debug.option.debugging.level" value="nds.c.debugging.level.max" valueType="enumerated"/>
								<option id="nds.c.compiler.option.include.paths.302162881" name="Include paths (-I)" superClass="nds.c.compiler.option.include.paths" valueType="includePath">
									<listOptionValue builtIn="false" value="&quot;..\opt\inc&quot;"/>
									<listOptionValue builtIn="false" value="&quot;..\opt&quot;"/>
									<listOptionValue builtIn="false" value="&quot;..\shr\inc&quot;"/>
									<listOptionValue builtIn="false" value="&quot;..\startup\inc&quot;"/>
								</option>
								<option id="nds32le-elf-mculib-v3m.c.compiler.exe.debug.option.optimization.level.1761583243" name="Optimization Level" superClass="nds32le-elf-mculib-v3m.c.compiler.exe.debug.option.optimization.level" value="nds.c.optimization.level.more" valueType="enumerated"/>
								<option id="nds.c.compiler.option.optimization.removesections.function.764676669" name="Remove unused function sections (-ffunction-sections)" superClass="nds.c.compiler.option.optimization.removesections.function" value="true" valueType="boolean"/>
								<option id="nds.c.compiler.option.optimization.removesections.data.909212166" name="Remove unused data sections (-fdata-sections)" superClass="nds.c.compiler.option.optimization.removesections.data" value="true" valueType="boolean"/>
								<option id="nds.c.compiler.option.cmodel.1973351308" name="Code Model" superClass="nds.c.compiler.option.cmodel" value="nds.c.compiler.option.cmodel.medium" valueType="enumerated"/>
								<option id="nds.c.compiler.option.preprocessor.def.symbols.1507472586" name="Defined symbols (-D)" superClass="nds.c.compiler.option.preprocessor.def.symbols" valueType="definedSymbols">
									<listOptionValue builtIn="false" value="CATEGORY_CUSTOMER=CUSTOMER_YMTC"/>
									<listOptionValue builtIn="false" value="CATEGORY_FLASH=FLASH_YMTC_TAS_TLC"/>
									<listOptionValue builtIn="false" value="CATEGORY_CONTROLLER=CONTROLLER_PS5017"/>
								</option>
								<inputType id="tool.nds.c.compiler.input.1162489236" superClass="tool.nds.c.compiler.input"/>
							</tool>
							<tool command="$(CROSS_COMPILE)gcc" commandLinePattern="${COMMAND} ${FLAGS} ${OUTPUT_FLAG} ${OUTPUT_PREFIX}${OUTPUT} ${INPUTS}" errorParsers="org.eclipse.cdt.core.GLDErrorParser" id="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug.1081793525" name="Andes C Linker" superClass="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug">
								<option defaultValue="-O0" id="nds.c.link.option.optimization.level.99951462" name="Optimization Level" superClass="nds.c.link.option.optimization.level" value="-O2" valueType="string"/>
								<option defaultValue="true" id="nds32le-elf-mculib-v3m.c.link.option.noshared.base.1868798985" name="No shared libraries (-static)" superClass="nds32le-elf-mculib-v3m.c.link.option.noshared.base" valueType="boolean"/>
								<option defaultValue="" id="nds32le-elf-mculib-v3m.c.link.exe.debug.option.ldscript.1889528968" name="Linker Script (-T)" superClass="nds32le-elf-mculib-v3m.c.link.exe.debug.option.ldscript" value="..\LinkerScript\ps5008_hw.ld" valueType="string"/>
								<option id="nds.c.linker.option.cmodel.1672541807" name="Code Model" superClass="nds.c.linker.option.cmodel" value="-mcmodel=medium" valueType="string"/>
								<option id="nds.c.link.option.map.955339720" name="Write a map file. (-Map)" superClass="nds.c.link.option.map" value="true" valueType="boolean"/>
								<option id="nds.c.link.option.virtualhosting.216356864" name="Virtual Hosting (-mvh)" superClass="nds.c.link.option.virtualhosting" value="false" valueType="boolean"/>
								<option id="nds.c.link.option.nostart.281606691" name="Do not use standard start files (-nostartfiles)" superClass="nds.c.link.option.nostart" value="true" valueType="boolean"/>
								<option id="nds.c.link.option.removesections.262566048" name="Remove unused sections (-ffunction-sections or -fdata-sections)" superClass="nds.c.link.option.removesections" value="true" valueType="boolean"/>
								<inputType id="tool.nds.c.linker.input.326232264" superClass="tool.nds.c.linker.input">
									<additionalInput kind="additionalinputdependency" paths="$(USER_OBJS)"/>
									<additionalInput kind="additionalinput" paths="$(LIBS)"/>
								</inputType>
							</tool>
							<tool command="$(CROSS_COMPILE)as" commandLinePattern="${COMMAND} ${FLAGS} ${OUTPUT_FLAG} ${OUTPUT_PREFIX}${OUTPUT} ${INPUTS}" errorParsers="org.eclipse.cdt.core.GASErrorParser" id="tool.nds32le-elf-mculib-v3m.assembler.exe.debug.1206300490" name="Andes Assembler" superClass="tool.nds32le-elf-mculib-v3m.assembler.exe.debug">
								<option defaultValue="-g" id="nds.both.asm.option.flags.1252580966" name="Assembler flags" superClass="nds.both.asm.option.flags" valueType="string"/>
								<inputType id="tool.nds.assembler.input.789286244" superClass="tool.nds.assembler.input"/>
							</tool>
							<tool command="$(CROSS_COMPILE)nm" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot; ${OUTPUT_FLAG} ${OUTPUT}" errorParsers="" id="tool.nds32le-elf-mculib-v3m.nm.exe.debug.1214156034" name="NM (symbol listing)" superClass="tool.nds32le-elf-mculib-v3m.nm.exe.debug">
								<option id="nds.nm.option.sortsymbol.1634706522" name="Sort symbols numerically by address. (-n)" superClass="nds.nm.option.sortsymbol" value="true" valueType="boolean"/>
								<option id="nds.nm.option.printfilename.1475809817" name="Print name of the input file before every symbol. (-A)" superClass="nds.nm.option.printfilename" value="false" valueType="boolean"/>
							</tool>
							<tool command="$(CROSS_COMPILE)readelf" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot; ${OUTPUT_FLAG} ${OUTPUT}" errorParsers="" id="tool.nds32le-elf-mculib-v3m.readelf.exe.debug.1182031749" name="Readelf (ELF info listing)" superClass="tool.nds32le-elf-mculib-v3m.readelf.exe.debug"/>
							<tool command="$(CROSS_COMPILE)objdump" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot; ${OUTPUT_FLAG} ${OUTPUT}" errorParsers="" id="tool.nds32le-elf-mculib-v3m.objdump.exe.debug.552620128" name="Objdump (disassembly)" superClass="tool.nds32le-elf-mculib-v3m.objdump.exe.debug">
								<option id="nds.objdump.option.disable.1907678650" name="Disable. (Do not auto-generate output file.)" superClass="nds.objdump.option.disable" value="false" valueType="boolean"/>
								<option id="nds.objdump.option.linenumber.701284041" name="Include line numbers and filenames in output. (-l)" superClass="nds.objdump.option.linenumber" value="true" valueType="boolean"/>
								<option id="nds.objdump.option.otherflags.1319551421" name="Other flags" superClass="nds.objdump.option.otherflags" value="-S" valueType="string"/>
							</tool>
							<tool command="$(CROSS_COMPILE)objcopy" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot; ${OUTPUT}" errorParsers="" id="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug.220646110" name="Objcopy (object content copy)" superClass="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug">
								<option id="nds.objcopy.option.disable.1494985305" name="Disable. (Do not auto-generate output file.)" superClass="nds.objcopy.option.disable" value="false" valueType="boolean"/>
								<option id="nds.objcopy.option.stripall.1416258887" name="Remove all symbol and relocation information. (-S)" superClass="nds.objcopy.option.stripall" value="false" valueType="boolean"/>
								<option id="nds.objcopy.option.otherflags.2063274053" name="Other flags" superClass="nds.objcopy.option.otherflags" value="-R .flh_iram_fpu.data" valueType="string"/>
							</tool>
							<tool command="$(CROSS_COMPILE)size" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot;" errorParsers="" id="tool.nds32le-elf-mculib-v3m.size.exe.debug.521170686" name="Size (section size listing)" superClass="tool.nds32le-elf-mculib-v3m.size.exe.debug"/>
							<tool command="$(ANDESIGHT_ROOT)/utils/nds_ldsag" commandLinePattern="${COMMAND} ${FLAGS} ${INPUTS} -o $(LDSAG_OUT)" errorParsers="" id="tool.nds32le-elf-mculib-v3m.ldsag.base.1171141033" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.base"/>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.1081271551" name="Andes C++ Compiler" superClass="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug">
								<option id="nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.option.debugging.level.1382331901" name="Debug Level" superClass="nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.option.debugging.level" value="nds.cpp.compiler.debugging.level.max" valueType="enumerated"/>
							</tool>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug.521696229" name="Andes C++ Linker" superClass="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug">
								<option defaultValue="-O0" id="nds.cpp.link.option.optimization.level.1656978546" name="Optimization Level" superClass="nds.cpp.link.option.optimization.level" valueType="string"/>
								<option defaultValue="true" id="nds32le-elf-mculib-v3m.cpp.link.option.noshared.base.2136850786" name="No shared libraries (-static)" superClass="nds32le-elf-mculib-v3m.cpp.link.option.noshared.base" valueType="boolean"/>
							</tool>
							<tool command="$(ANDESIGHT_ROOT)/utils/nds_ldsag" commandLinePattern="${COMMAND} ${FLAGS} ${INPUTS} -o $(LDSAG_OUT)" errorParsers="" id="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug.996960144" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug"/>
						</toolChain>
					</folderInfo>
					<folderInfo id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********.opt_master_only" name="/" resourcePath="opt_master_only">
						<toolChain errorParsers="" id="nds.nds32le-elf-mculib-v3m.exe.debug.581112525" name="nds32le-elf-mculib-v3m" superClass="nds.nds32le-elf-mculib-v3m.exe.debug" unusedChildren="">
							<option id="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET.423936908.313979601.1887125436.1009016960.1709021572.182459041" name="RSE_TARGET" superClass="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET.423936908"/>
							<targetPlatform id="target.nds32le-elf-mculib-v3m.platform.exe.debug.965234736" name="Debug Platform" superClass="target.nds32le-elf-mculib-v3m.platform.exe.debug"/>
							<tool id="tool.nds32le-elf-mculib-v3m.archiver.base.353637814" name="Andes Archiver" superClass="tool.nds32le-elf-mculib-v3m.archiver.base.1048064578"/>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.1161856460" name="Andes C++ Compiler" superClass="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.1081271551">
								<option id="nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.option.debugging.level.1556178963" name="Debug Level" superClass="nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.option.debugging.level" value="nds.cpp.compiler.debugging.level.max" valueType="enumerated"/>
							</tool>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug.1710151040" name="Andes C++ Linker" superClass="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug.521696229">
								<option defaultValue="-O0" id="nds.cpp.link.option.optimization.level.89197823" name="Optimization Level" superClass="nds.cpp.link.option.optimization.level" valueType="string"/>
								<option defaultValue="true" id="nds32le-elf-mculib-v3m.cpp.link.option.noshared.base.687961318" name="No shared libraries (-static)" superClass="nds32le-elf-mculib-v3m.cpp.link.option.noshared.base" valueType="boolean"/>
							</tool>
							<tool errorParsers="org.eclipse.cdt.core.GCCErrorParser" id="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.497063530" name="Andes C Compiler" superClass="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.1999229441">
								<inputType id="tool.nds.c.compiler.input.859981540" superClass="tool.nds.c.compiler.input"/>
							</tool>
							<tool id="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug.1011773652" name="Andes C Linker" superClass="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug.1081793525"/>
							<tool errorParsers="org.eclipse.cdt.core.GASErrorParser" id="tool.nds32le-elf-mculib-v3m.assembler.exe.debug.769822791" name="Andes Assembler" superClass="tool.nds32le-elf-mculib-v3m.assembler.exe.debug.1206300490">
								<inputType id="tool.nds.assembler.input.1424913425" superClass="tool.nds.assembler.input"/>
							</tool>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.nm.exe.debug.475216993" name="NM (symbol listing)" superClass="tool.nds32le-elf-mculib-v3m.nm.exe.debug.1214156034"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.readelf.exe.debug.981433383" name="Readelf (ELF info listing)" superClass="tool.nds32le-elf-mculib-v3m.readelf.exe.debug.1182031749"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.objdump.exe.debug.773133832" name="Objdump (disassembly)" superClass="tool.nds32le-elf-mculib-v3m.objdump.exe.debug.552620128"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug.1561114227" name="Objcopy (object content copy)" superClass="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug.220646110"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.size.exe.debug.2134546819" name="Size (section size listing)" superClass="tool.nds32le-elf-mculib-v3m.size.exe.debug.521170686"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug.1144672141" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug.996960144"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.ldsag.base.1013926824" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.base.1171141033"/>
						</toolChain>
					</folderInfo>
					<folderInfo id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********.64541172" name="/" resourcePath="opt">
						<toolChain id="nds.nds32le-elf-mculib-v3m.exe.debug.331548819" name="nds32le-elf-mculib-v3m" superClass="nds.nds32le-elf-mculib-v3m.exe.debug" unusedChildren="">
							<option id="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET.1599934830.46803478" name="RSE_TARGET" superClass="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET.1599934830"/>
							<targetPlatform id="target.nds32le-elf-mculib-v3m.platform.exe.debug" name="Debug Platform" superClass="target.nds32le-elf-mculib-v3m.platform.exe.debug"/>
							<tool id="tool.nds32le-elf-mculib-v3m.archiver.base.2047418704" name="Andes Archiver" superClass="tool.nds32le-elf-mculib-v3m.archiver.base.1048064578"/>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.363594505" name="Andes C++ Compiler" superClass="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.1081271551"/>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug.1012648070" name="Andes C++ Linker" superClass="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug.521696229"/>
							<tool id="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.**********" name="Andes C Compiler" superClass="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.1999229441">
								<inputType id="tool.nds.c.compiler.input.613629388" superClass="tool.nds.c.compiler.input"/>
							</tool>
							<tool id="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug.1058715701" name="Andes C Linker" superClass="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug.1081793525"/>
							<tool id="tool.nds32le-elf-mculib-v3m.assembler.exe.debug.855894938" name="Andes Assembler" superClass="tool.nds32le-elf-mculib-v3m.assembler.exe.debug.1206300490">
								<inputType id="tool.nds.assembler.input.932661475" superClass="tool.nds.assembler.input"/>
							</tool>
							<tool id="tool.nds32le-elf-mculib-v3m.nm.exe.debug.479339898" name="NM (symbol listing)" superClass="tool.nds32le-elf-mculib-v3m.nm.exe.debug.1214156034"/>
							<tool id="tool.nds32le-elf-mculib-v3m.readelf.exe.debug.1682794768" name="Readelf (ELF info listing)" superClass="tool.nds32le-elf-mculib-v3m.readelf.exe.debug.1182031749"/>
							<tool id="tool.nds32le-elf-mculib-v3m.objdump.exe.debug.500567824" name="Objdump (disassembly)" superClass="tool.nds32le-elf-mculib-v3m.objdump.exe.debug.552620128"/>
							<tool id="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug.285720473" name="Objcopy (object content copy)" superClass="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug.220646110"/>
							<tool id="tool.nds32le-elf-mculib-v3m.size.exe.debug.1105584770" name="Size (section size listing)" superClass="tool.nds32le-elf-mculib-v3m.size.exe.debug.521170686"/>
							<tool id="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug.1508170293" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug.996960144"/>
							<tool id="tool.nds32le-elf-mculib-v3m.ldsag.base.1523726996" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.base.1171141033"/>
						</toolChain>
					</folderInfo>
					<sourceEntries>
						<entry excluding="opt_master_only|Src_KIC|shr/inc/shr_types.h" flags="VALUE_WORKSPACE_PATH" kind="sourcePath" name=""/>
					</sourceEntries>
				</configuration>
			</storageModule>
			<storageModule moduleId="org.eclipse.cdt.core.externalSettings"/>
		</cconfiguration>
		<cconfiguration id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********">
			<storageModule buildSystemId="org.eclipse.cdt.managedbuilder.core.configurationDataProvider" id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********" moduleId="org.eclipse.cdt.core.settings" name="PS5017_Hynix_V7_QLC">
				<externalSettings/>
				<extensions>
					<extension id="com.andestech.ide.cdt.managedbuilder.core.CROSS_GNU_ELF" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="org.eclipse.cdt.core.GmakeErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.CWDLocator" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GCCErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GASErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GLDErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
				</extensions>
			</storageModule>
			<storageModule moduleId="cdtBuildSystem" version="4.0.0">
				<configuration artifactExtension="adx" artifactName="${ProjName}" buildArtefactType="nds.buildArtefactType.exe" buildProperties="org.eclipse.cdt.build.core.buildType=org.eclipse.cdt.build.core.buildType.debug,org.eclipse.cdt.build.core.buildArtefactType=nds.buildArtefactType.exe" description="" errorParsers="org.eclipse.cdt.core.GmakeErrorParser;org.eclipse.cdt.core.CWDLocator;org.eclipse.cdt.core.GCCErrorParser;org.eclipse.cdt.core.GASErrorParser;org.eclipse.cdt.core.GLDErrorParser" id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********" name="PS5017_Hynix_V7_QLC" parent="config.nds32le-elf-mculib-v3m.exe.debug" postannouncebuildStep="" postbuildStep="" preannouncebuildStep="" prebuildStep="">
					<folderInfo id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********." name="/" resourcePath="">
						<toolChain errorParsers="" id="nds.nds32le-elf-mculib-v3m.exe.debug.20260435" name="nds32le-elf-mculib-v3m" superClass="nds.nds32le-elf-mculib-v3m.exe.debug">
							<option id="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET.1849191359" name="RSE_TARGET" superClass="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET" value="rse://LOCALHOST/~toolchains/nds32le-elf-mculib-v3m" valueType="string"/>
							<targetPlatform binaryParser="com.andestech.ide.cdt.managedbuilder.core.CROSS_GNU_ELF" id="target.nds32le-elf-mculib-v3m.platform.exe.debug.1433870619" name="Debug Platform" superClass="target.nds32le-elf-mculib-v3m.platform.exe.debug"/>
							<builder buildPath="${workspace_loc:/ps5008_opt/Debug}" errorParsers="org.eclipse.cdt.core.GmakeErrorParser;org.eclipse.cdt.core.CWDLocator" id="target.nds32le-elf-mculib-v3m.builder.exe.debug.1070333634" keepEnvironmentInBuildfile="false" managedBuildOn="true" name="Andes Make Builder" parallelBuildOn="false" parallelizationNumber="-1" superClass="target.nds32le-elf-mculib-v3m.builder.exe.debug"/>
							<tool id="tool.nds32le-elf-mculib-v3m.archiver.base.307327357" name="Andes Archiver" superClass="tool.nds32le-elf-mculib-v3m.archiver.base"/>
							<tool command="$(CROSS_COMPILE)gcc" commandLinePattern="${COMMAND} ${FLAGS} ${OUTPUT_FLAG} ${OUTPUT_PREFIX}${OUTPUT} ${INPUTS}" errorParsers="org.eclipse.cdt.core.GCCErrorParser" id="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.533290899" name="Andes C Compiler" superClass="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug">
								<option id="nds32le-elf-mculib-v3m.c.compiler.exe.debug.option.debugging.level.912773324" name="Debug Level" superClass="nds32le-elf-mculib-v3m.c.compiler.exe.debug.option.debugging.level" value="nds.c.debugging.level.max" valueType="enumerated"/>
								<option id="nds.c.compiler.option.include.paths.815252915" name="Include paths (-I)" superClass="nds.c.compiler.option.include.paths" valueType="includePath">
									<listOptionValue builtIn="false" value="&quot;..\opt\inc&quot;"/>
									<listOptionValue builtIn="false" value="&quot;..\opt&quot;"/>
									<listOptionValue builtIn="false" value="&quot;..\shr\inc&quot;"/>
									<listOptionValue builtIn="false" value="&quot;..\startup\inc&quot;"/>
								</option>
								<option id="nds32le-elf-mculib-v3m.c.compiler.exe.debug.option.optimization.level.1350823062" name="Optimization Level" superClass="nds32le-elf-mculib-v3m.c.compiler.exe.debug.option.optimization.level" value="nds.c.optimization.level.more" valueType="enumerated"/>
								<option id="nds.c.compiler.option.optimization.removesections.function.1617265437" name="Remove unused function sections (-ffunction-sections)" superClass="nds.c.compiler.option.optimization.removesections.function" value="true" valueType="boolean"/>
								<option id="nds.c.compiler.option.optimization.removesections.data.308410988" name="Remove unused data sections (-fdata-sections)" superClass="nds.c.compiler.option.optimization.removesections.data" value="true" valueType="boolean"/>
								<option id="nds.c.compiler.option.cmodel.485423464" name="Code Model" superClass="nds.c.compiler.option.cmodel" value="nds.c.compiler.option.cmodel.medium" valueType="enumerated"/>
								<option id="nds.c.compiler.option.preprocessor.def.symbols.467230363" name="Defined symbols (-D)" superClass="nds.c.compiler.option.preprocessor.def.symbols" valueType="definedSymbols">
									<listOptionValue builtIn="false" value="CATEGORY_CUSTOMER=CUSTOMER_HYNIX"/>
									<listOptionValue builtIn="false" value="CATEGORY_FLASH=FLASH_HYNIX_V7_QLC"/>
									<listOptionValue builtIn="false" value="CATEGORY_CONTROLLER=CONTROLLER_PS5017"/>
								</option>
								<inputType id="tool.nds.c.compiler.input.612716712" superClass="tool.nds.c.compiler.input"/>
							</tool>
							<tool command="$(CROSS_COMPILE)gcc" commandLinePattern="${COMMAND} ${FLAGS} ${OUTPUT_FLAG} ${OUTPUT_PREFIX}${OUTPUT} ${INPUTS}" errorParsers="org.eclipse.cdt.core.GLDErrorParser" id="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug.1783078716" name="Andes C Linker" superClass="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug">
								<option defaultValue="-O0" id="nds.c.link.option.optimization.level.1690870554" name="Optimization Level" superClass="nds.c.link.option.optimization.level" value="-O2" valueType="string"/>
								<option defaultValue="true" id="nds32le-elf-mculib-v3m.c.link.option.noshared.base.1587711458" name="No shared libraries (-static)" superClass="nds32le-elf-mculib-v3m.c.link.option.noshared.base" valueType="boolean"/>
								<option defaultValue="" id="nds32le-elf-mculib-v3m.c.link.exe.debug.option.ldscript.1133872204" name="Linker Script (-T)" superClass="nds32le-elf-mculib-v3m.c.link.exe.debug.option.ldscript" value="..\LinkerScript\ps5008_hw.ld" valueType="string"/>
								<option id="nds.c.linker.option.cmodel.1893382112" name="Code Model" superClass="nds.c.linker.option.cmodel" value="-mcmodel=medium" valueType="string"/>
								<option id="nds.c.link.option.map.1944796635" name="Write a map file. (-Map)" superClass="nds.c.link.option.map" value="true" valueType="boolean"/>
								<option id="nds.c.link.option.virtualhosting.1202112526" name="Virtual Hosting (-mvh)" superClass="nds.c.link.option.virtualhosting" value="false" valueType="boolean"/>
								<option id="nds.c.link.option.nostart.1064635549" name="Do not use standard start files (-nostartfiles)" superClass="nds.c.link.option.nostart" value="true" valueType="boolean"/>
								<option id="nds.c.link.option.removesections.260841690" name="Remove unused sections (-ffunction-sections or -fdata-sections)" superClass="nds.c.link.option.removesections" value="true" valueType="boolean"/>
								<inputType id="tool.nds.c.linker.input.311580159" superClass="tool.nds.c.linker.input">
									<additionalInput kind="additionalinputdependency" paths="$(USER_OBJS)"/>
									<additionalInput kind="additionalinput" paths="$(LIBS)"/>
								</inputType>
							</tool>
							<tool command="$(CROSS_COMPILE)as" commandLinePattern="${COMMAND} ${FLAGS} ${OUTPUT_FLAG} ${OUTPUT_PREFIX}${OUTPUT} ${INPUTS}" errorParsers="org.eclipse.cdt.core.GASErrorParser" id="tool.nds32le-elf-mculib-v3m.assembler.exe.debug.878256899" name="Andes Assembler" superClass="tool.nds32le-elf-mculib-v3m.assembler.exe.debug">
								<option defaultValue="-g" id="nds.both.asm.option.flags.39815108" name="Assembler flags" superClass="nds.both.asm.option.flags" valueType="string"/>
								<inputType id="tool.nds.assembler.input.1079325248" superClass="tool.nds.assembler.input"/>
							</tool>
							<tool command="$(CROSS_COMPILE)nm" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot; ${OUTPUT_FLAG} ${OUTPUT}" errorParsers="" id="tool.nds32le-elf-mculib-v3m.nm.exe.debug.1340976769" name="NM (symbol listing)" superClass="tool.nds32le-elf-mculib-v3m.nm.exe.debug">
								<option id="nds.nm.option.sortsymbol.178625563" name="Sort symbols numerically by address. (-n)" superClass="nds.nm.option.sortsymbol" value="true" valueType="boolean"/>
								<option id="nds.nm.option.printfilename.461122597" name="Print name of the input file before every symbol. (-A)" superClass="nds.nm.option.printfilename" value="false" valueType="boolean"/>
							</tool>
							<tool command="$(CROSS_COMPILE)readelf" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot; ${OUTPUT_FLAG} ${OUTPUT}" errorParsers="" id="tool.nds32le-elf-mculib-v3m.readelf.exe.debug.636973391" name="Readelf (ELF info listing)" superClass="tool.nds32le-elf-mculib-v3m.readelf.exe.debug"/>
							<tool command="$(CROSS_COMPILE)objdump" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot; ${OUTPUT_FLAG} ${OUTPUT}" errorParsers="" id="tool.nds32le-elf-mculib-v3m.objdump.exe.debug.925271686" name="Objdump (disassembly)" superClass="tool.nds32le-elf-mculib-v3m.objdump.exe.debug">
								<option id="nds.objdump.option.disable.803623239" name="Disable. (Do not auto-generate output file.)" superClass="nds.objdump.option.disable" value="false" valueType="boolean"/>
								<option id="nds.objdump.option.linenumber.688627880" name="Include line numbers and filenames in output. (-l)" superClass="nds.objdump.option.linenumber" value="true" valueType="boolean"/>
								<option id="nds.objdump.option.otherflags.151576401" name="Other flags" superClass="nds.objdump.option.otherflags" value="-S" valueType="string"/>
							</tool>
							<tool command="$(CROSS_COMPILE)objcopy" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot; ${OUTPUT}" errorParsers="" id="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug.41469005" name="Objcopy (object content copy)" superClass="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug">
								<option id="nds.objcopy.option.disable.1604915152" name="Disable. (Do not auto-generate output file.)" superClass="nds.objcopy.option.disable" value="false" valueType="boolean"/>
								<option id="nds.objcopy.option.stripall.1706425223" name="Remove all symbol and relocation information. (-S)" superClass="nds.objcopy.option.stripall" value="false" valueType="boolean"/>
								<option id="nds.objcopy.option.otherflags.1340132369" name="Other flags" superClass="nds.objcopy.option.otherflags" value="-R .flh_iram_fpu.data" valueType="string"/>
							</tool>
							<tool command="$(CROSS_COMPILE)size" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot;" errorParsers="" id="tool.nds32le-elf-mculib-v3m.size.exe.debug.465623352" name="Size (section size listing)" superClass="tool.nds32le-elf-mculib-v3m.size.exe.debug"/>
							<tool command="$(ANDESIGHT_ROOT)/utils/nds_ldsag" commandLinePattern="${COMMAND} ${FLAGS} ${INPUTS} -o $(LDSAG_OUT)" errorParsers="" id="tool.nds32le-elf-mculib-v3m.ldsag.base.1212131896" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.base"/>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.1421433449" name="Andes C++ Compiler" superClass="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug">
								<option id="nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.option.debugging.level.1043051571" name="Debug Level" superClass="nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.option.debugging.level" value="nds.cpp.compiler.debugging.level.max" valueType="enumerated"/>
							</tool>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug.1606859881" name="Andes C++ Linker" superClass="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug">
								<option defaultValue="-O0" id="nds.cpp.link.option.optimization.level.439591336" name="Optimization Level" superClass="nds.cpp.link.option.optimization.level" valueType="string"/>
								<option defaultValue="true" id="nds32le-elf-mculib-v3m.cpp.link.option.noshared.base.354326608" name="No shared libraries (-static)" superClass="nds32le-elf-mculib-v3m.cpp.link.option.noshared.base" valueType="boolean"/>
							</tool>
							<tool command="$(ANDESIGHT_ROOT)/utils/nds_ldsag" commandLinePattern="${COMMAND} ${FLAGS} ${INPUTS} -o $(LDSAG_OUT)" errorParsers="" id="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug.87223053" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug"/>
						</toolChain>
					</folderInfo>
					<folderInfo id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********.opt_master_only" name="/" resourcePath="opt_master_only">
						<toolChain errorParsers="" id="nds.nds32le-elf-mculib-v3m.exe.debug.1496301260" name="nds32le-elf-mculib-v3m" superClass="nds.nds32le-elf-mculib-v3m.exe.debug" unusedChildren="">
							<option id="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET.423936908.313979601.1887125436.1009016960.1709021572.1454310588" name="RSE_TARGET" superClass="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET.423936908"/>
							<targetPlatform id="target.nds32le-elf-mculib-v3m.platform.exe.debug.400646415" name="Debug Platform" superClass="target.nds32le-elf-mculib-v3m.platform.exe.debug"/>
							<tool id="tool.nds32le-elf-mculib-v3m.archiver.base.1033600268" name="Andes Archiver" superClass="tool.nds32le-elf-mculib-v3m.archiver.base.307327357"/>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.147783778" name="Andes C++ Compiler" superClass="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.1421433449">
								<option id="nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.option.debugging.level.145209371" name="Debug Level" superClass="nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.option.debugging.level" value="nds.cpp.compiler.debugging.level.max" valueType="enumerated"/>
							</tool>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug.992965470" name="Andes C++ Linker" superClass="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug.1606859881">
								<option defaultValue="-O0" id="nds.cpp.link.option.optimization.level.1019003131" name="Optimization Level" superClass="nds.cpp.link.option.optimization.level" valueType="string"/>
								<option defaultValue="true" id="nds32le-elf-mculib-v3m.cpp.link.option.noshared.base.96674207" name="No shared libraries (-static)" superClass="nds32le-elf-mculib-v3m.cpp.link.option.noshared.base" valueType="boolean"/>
							</tool>
							<tool errorParsers="org.eclipse.cdt.core.GCCErrorParser" id="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.1305901272" name="Andes C Compiler" superClass="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.533290899">
								<inputType id="tool.nds.c.compiler.input.1575387215" superClass="tool.nds.c.compiler.input"/>
							</tool>
							<tool id="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug.1928870277" name="Andes C Linker" superClass="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug.1783078716"/>
							<tool errorParsers="org.eclipse.cdt.core.GASErrorParser" id="tool.nds32le-elf-mculib-v3m.assembler.exe.debug.1978868229" name="Andes Assembler" superClass="tool.nds32le-elf-mculib-v3m.assembler.exe.debug.878256899">
								<inputType id="tool.nds.assembler.input.631208486" superClass="tool.nds.assembler.input"/>
							</tool>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.nm.exe.debug.944202330" name="NM (symbol listing)" superClass="tool.nds32le-elf-mculib-v3m.nm.exe.debug.1340976769"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.readelf.exe.debug.1794083576" name="Readelf (ELF info listing)" superClass="tool.nds32le-elf-mculib-v3m.readelf.exe.debug.636973391"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.objdump.exe.debug.1824167727" name="Objdump (disassembly)" superClass="tool.nds32le-elf-mculib-v3m.objdump.exe.debug.925271686"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug.37021483" name="Objcopy (object content copy)" superClass="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug.41469005"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.size.exe.debug.1676275454" name="Size (section size listing)" superClass="tool.nds32le-elf-mculib-v3m.size.exe.debug.465623352"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug.469600587" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug.87223053"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.ldsag.base.1288545110" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.base.1212131896"/>
						</toolChain>
					</folderInfo>
					<sourceEntries>
						<entry excluding="opt_master_only|Src_KIC|shr/inc/shr_types.h" flags="VALUE_WORKSPACE_PATH" kind="sourcePath" name=""/>
					</sourceEntries>
				</configuration>
			</storageModule>
			<storageModule moduleId="org.eclipse.cdt.core.externalSettings"/>
		</cconfiguration>
		<cconfiguration id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********">
			<storageModule buildSystemId="org.eclipse.cdt.managedbuilder.core.configurationDataProvider" id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********" moduleId="org.eclipse.cdt.core.settings" name="PS5017_YMTC_EMS">
				<externalSettings/>
				<extensions>
					<extension id="com.andestech.ide.cdt.managedbuilder.core.CROSS_GNU_ELF" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="org.eclipse.cdt.core.GmakeErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.CWDLocator" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GCCErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GASErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GLDErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
				</extensions>
			</storageModule>
			<storageModule moduleId="cdtBuildSystem" version="4.0.0">
				<configuration artifactExtension="adx" artifactName="${ProjName}" buildArtefactType="nds.buildArtefactType.exe" buildProperties="org.eclipse.cdt.build.core.buildType=org.eclipse.cdt.build.core.buildType.debug,org.eclipse.cdt.build.core.buildArtefactType=nds.buildArtefactType.exe" description="" errorParsers="org.eclipse.cdt.core.GmakeErrorParser;org.eclipse.cdt.core.CWDLocator;org.eclipse.cdt.core.GCCErrorParser;org.eclipse.cdt.core.GASErrorParser;org.eclipse.cdt.core.GLDErrorParser" id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********" name="PS5017_YMTC_EMS" parent="config.nds32le-elf-mculib-v3m.exe.debug" postannouncebuildStep="" postbuildStep="" preannouncebuildStep="" prebuildStep="">
					<folderInfo id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********." name="/" resourcePath="">
						<toolChain errorParsers="" id="nds.nds32le-elf-mculib-v3m.exe.debug.1225690387" name="nds32le-elf-mculib-v3m" superClass="nds.nds32le-elf-mculib-v3m.exe.debug">
							<option id="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET.464865305" name="RSE_TARGET" superClass="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET" value="rse://LOCALHOST/~toolchains/nds32le-elf-mculib-v3m" valueType="string"/>
							<targetPlatform binaryParser="com.andestech.ide.cdt.managedbuilder.core.CROSS_GNU_ELF" id="target.nds32le-elf-mculib-v3m.platform.exe.debug.749583703" name="Debug Platform" superClass="target.nds32le-elf-mculib-v3m.platform.exe.debug"/>
							<builder buildPath="${workspace_loc:/ps5008_opt/Debug}" errorParsers="org.eclipse.cdt.core.GmakeErrorParser;org.eclipse.cdt.core.CWDLocator" id="target.nds32le-elf-mculib-v3m.builder.exe.debug.1415529139" keepEnvironmentInBuildfile="false" managedBuildOn="true" name="Andes Make Builder" parallelBuildOn="false" parallelizationNumber="-1" superClass="target.nds32le-elf-mculib-v3m.builder.exe.debug"/>
							<tool id="tool.nds32le-elf-mculib-v3m.archiver.base.135260269" name="Andes Archiver" superClass="tool.nds32le-elf-mculib-v3m.archiver.base"/>
							<tool command="$(CROSS_COMPILE)gcc" commandLinePattern="${COMMAND} ${FLAGS} ${OUTPUT_FLAG} ${OUTPUT_PREFIX}${OUTPUT} ${INPUTS}" errorParsers="org.eclipse.cdt.core.GCCErrorParser" id="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.1245613064" name="Andes C Compiler" superClass="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug">
								<option id="nds32le-elf-mculib-v3m.c.compiler.exe.debug.option.debugging.level.1045960636" name="Debug Level" superClass="nds32le-elf-mculib-v3m.c.compiler.exe.debug.option.debugging.level" value="nds.c.debugging.level.max" valueType="enumerated"/>
								<option id="nds.c.compiler.option.include.paths.424127870" name="Include paths (-I)" superClass="nds.c.compiler.option.include.paths" valueType="includePath">
									<listOptionValue builtIn="false" value="&quot;..\opt\inc&quot;"/>
									<listOptionValue builtIn="false" value="&quot;..\opt&quot;"/>
									<listOptionValue builtIn="false" value="&quot;..\shr\inc&quot;"/>
									<listOptionValue builtIn="false" value="&quot;..\startup\inc&quot;"/>
								</option>
								<option id="nds32le-elf-mculib-v3m.c.compiler.exe.debug.option.optimization.level.1804802277" name="Optimization Level" superClass="nds32le-elf-mculib-v3m.c.compiler.exe.debug.option.optimization.level" value="nds.c.optimization.level.more" valueType="enumerated"/>
								<option id="nds.c.compiler.option.optimization.removesections.function.123977373" name="Remove unused function sections (-ffunction-sections)" superClass="nds.c.compiler.option.optimization.removesections.function" value="true" valueType="boolean"/>
								<option id="nds.c.compiler.option.optimization.removesections.data.539652083" name="Remove unused data sections (-fdata-sections)" superClass="nds.c.compiler.option.optimization.removesections.data" value="true" valueType="boolean"/>
								<option id="nds.c.compiler.option.cmodel.753813273" name="Code Model" superClass="nds.c.compiler.option.cmodel" value="nds.c.compiler.option.cmodel.medium" valueType="enumerated"/>
								<option id="nds.c.compiler.option.preprocessor.def.symbols.877929544" name="Defined symbols (-D)" superClass="nds.c.compiler.option.preprocessor.def.symbols" valueType="definedSymbols">
									<listOptionValue builtIn="false" value="CATEGORY_CUSTOMER=CUSTOMER_HYNIX"/>
									<listOptionValue builtIn="false" value="CATEGORY_FLASH=FLASH_YMTC_EMS_QLC"/>
									<listOptionValue builtIn="false" value="CATEGORY_CONTROLLER=CONTROLLER_PS5017"/>
								</option>
								<inputType id="tool.nds.c.compiler.input.738708871" superClass="tool.nds.c.compiler.input"/>
							</tool>
							<tool command="$(CROSS_COMPILE)gcc" commandLinePattern="${COMMAND} ${FLAGS} ${OUTPUT_FLAG} ${OUTPUT_PREFIX}${OUTPUT} ${INPUTS}" errorParsers="org.eclipse.cdt.core.GLDErrorParser" id="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug.1901902386" name="Andes C Linker" superClass="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug">
								<option defaultValue="-O0" id="nds.c.link.option.optimization.level.322532731" name="Optimization Level" superClass="nds.c.link.option.optimization.level" value="-O2" valueType="string"/>
								<option defaultValue="true" id="nds32le-elf-mculib-v3m.c.link.option.noshared.base.2004700298" name="No shared libraries (-static)" superClass="nds32le-elf-mculib-v3m.c.link.option.noshared.base" valueType="boolean"/>
								<option defaultValue="" id="nds32le-elf-mculib-v3m.c.link.exe.debug.option.ldscript.856099052" name="Linker Script (-T)" superClass="nds32le-elf-mculib-v3m.c.link.exe.debug.option.ldscript" value="..\LinkerScript\ps5008_hw.ld" valueType="string"/>
								<option id="nds.c.linker.option.cmodel.906998270" name="Code Model" superClass="nds.c.linker.option.cmodel" value="-mcmodel=medium" valueType="string"/>
								<option id="nds.c.link.option.map.782783982" name="Write a map file. (-Map)" superClass="nds.c.link.option.map" value="true" valueType="boolean"/>
								<option id="nds.c.link.option.virtualhosting.1501581698" name="Virtual Hosting (-mvh)" superClass="nds.c.link.option.virtualhosting" value="false" valueType="boolean"/>
								<option id="nds.c.link.option.nostart.788236018" name="Do not use standard start files (-nostartfiles)" superClass="nds.c.link.option.nostart" value="true" valueType="boolean"/>
								<option id="nds.c.link.option.removesections.28406278" name="Remove unused sections (-ffunction-sections or -fdata-sections)" superClass="nds.c.link.option.removesections" value="true" valueType="boolean"/>
								<inputType id="tool.nds.c.linker.input.492767384" superClass="tool.nds.c.linker.input">
									<additionalInput kind="additionalinputdependency" paths="$(USER_OBJS)"/>
									<additionalInput kind="additionalinput" paths="$(LIBS)"/>
								</inputType>
							</tool>
							<tool command="$(CROSS_COMPILE)as" commandLinePattern="${COMMAND} ${FLAGS} ${OUTPUT_FLAG} ${OUTPUT_PREFIX}${OUTPUT} ${INPUTS}" errorParsers="org.eclipse.cdt.core.GASErrorParser" id="tool.nds32le-elf-mculib-v3m.assembler.exe.debug.1889835845" name="Andes Assembler" superClass="tool.nds32le-elf-mculib-v3m.assembler.exe.debug">
								<option defaultValue="-g" id="nds.both.asm.option.flags.1075619092" name="Assembler flags" superClass="nds.both.asm.option.flags" valueType="string"/>
								<inputType id="tool.nds.assembler.input.1332730225" superClass="tool.nds.assembler.input"/>
							</tool>
							<tool command="$(CROSS_COMPILE)nm" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot; ${OUTPUT_FLAG} ${OUTPUT}" errorParsers="" id="tool.nds32le-elf-mculib-v3m.nm.exe.debug.1552418575" name="NM (symbol listing)" superClass="tool.nds32le-elf-mculib-v3m.nm.exe.debug">
								<option id="nds.nm.option.sortsymbol.393263307" name="Sort symbols numerically by address. (-n)" superClass="nds.nm.option.sortsymbol" value="true" valueType="boolean"/>
								<option id="nds.nm.option.printfilename.2029723057" name="Print name of the input file before every symbol. (-A)" superClass="nds.nm.option.printfilename" value="false" valueType="boolean"/>
							</tool>
							<tool command="$(CROSS_COMPILE)readelf" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot; ${OUTPUT_FLAG} ${OUTPUT}" errorParsers="" id="tool.nds32le-elf-mculib-v3m.readelf.exe.debug.1587875284" name="Readelf (ELF info listing)" superClass="tool.nds32le-elf-mculib-v3m.readelf.exe.debug"/>
							<tool command="$(CROSS_COMPILE)objdump" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot; ${OUTPUT_FLAG} ${OUTPUT}" errorParsers="" id="tool.nds32le-elf-mculib-v3m.objdump.exe.debug.1025756138" name="Objdump (disassembly)" superClass="tool.nds32le-elf-mculib-v3m.objdump.exe.debug">
								<option id="nds.objdump.option.disable.1688456585" name="Disable. (Do not auto-generate output file.)" superClass="nds.objdump.option.disable" value="false" valueType="boolean"/>
								<option id="nds.objdump.option.linenumber.366042661" name="Include line numbers and filenames in output. (-l)" superClass="nds.objdump.option.linenumber" value="true" valueType="boolean"/>
								<option id="nds.objdump.option.otherflags.679270571" name="Other flags" superClass="nds.objdump.option.otherflags" value="-S" valueType="string"/>
							</tool>
							<tool command="$(CROSS_COMPILE)objcopy" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot; ${OUTPUT}" errorParsers="" id="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug.263881946" name="Objcopy (object content copy)" superClass="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug">
								<option id="nds.objcopy.option.disable.837670727" name="Disable. (Do not auto-generate output file.)" superClass="nds.objcopy.option.disable" value="false" valueType="boolean"/>
								<option id="nds.objcopy.option.stripall.1032054743" name="Remove all symbol and relocation information. (-S)" superClass="nds.objcopy.option.stripall" value="false" valueType="boolean"/>
								<option id="nds.objcopy.option.otherflags.1367405617" name="Other flags" superClass="nds.objcopy.option.otherflags" value="-R .flh_iram_fpu.data" valueType="string"/>
							</tool>
							<tool command="$(CROSS_COMPILE)size" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot;" errorParsers="" id="tool.nds32le-elf-mculib-v3m.size.exe.debug.1428327725" name="Size (section size listing)" superClass="tool.nds32le-elf-mculib-v3m.size.exe.debug"/>
							<tool command="$(ANDESIGHT_ROOT)/utils/nds_ldsag" commandLinePattern="${COMMAND} ${FLAGS} ${INPUTS} -o $(LDSAG_OUT)" errorParsers="" id="tool.nds32le-elf-mculib-v3m.ldsag.base.634805832" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.base"/>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.1234475963" name="Andes C++ Compiler" superClass="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug">
								<option id="nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.option.debugging.level.995993436" name="Debug Level" superClass="nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.option.debugging.level" value="nds.cpp.compiler.debugging.level.max" valueType="enumerated"/>
							</tool>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug.15660341" name="Andes C++ Linker" superClass="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug">
								<option defaultValue="-O0" id="nds.cpp.link.option.optimization.level.1376607247" name="Optimization Level" superClass="nds.cpp.link.option.optimization.level" valueType="string"/>
								<option defaultValue="true" id="nds32le-elf-mculib-v3m.cpp.link.option.noshared.base.480307326" name="No shared libraries (-static)" superClass="nds32le-elf-mculib-v3m.cpp.link.option.noshared.base" valueType="boolean"/>
							</tool>
							<tool command="$(ANDESIGHT_ROOT)/utils/nds_ldsag" commandLinePattern="${COMMAND} ${FLAGS} ${INPUTS} -o $(LDSAG_OUT)" errorParsers="" id="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug.68400824" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug"/>
						</toolChain>
					</folderInfo>
					<folderInfo id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********.opt_master_only" name="/" resourcePath="opt_master_only">
						<toolChain errorParsers="" id="nds.nds32le-elf-mculib-v3m.exe.debug.2096214345" name="nds32le-elf-mculib-v3m" superClass="nds.nds32le-elf-mculib-v3m.exe.debug" unusedChildren="">
							<option id="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET.423936908.313979601.1887125436.1009016960.1709021572.1684034818" name="RSE_TARGET" superClass="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET.423936908"/>
							<targetPlatform id="target.nds32le-elf-mculib-v3m.platform.exe.debug.1520844898" name="Debug Platform" superClass="target.nds32le-elf-mculib-v3m.platform.exe.debug"/>
							<tool id="tool.nds32le-elf-mculib-v3m.archiver.base.846570668" name="Andes Archiver" superClass="tool.nds32le-elf-mculib-v3m.archiver.base.135260269"/>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.1316682194" name="Andes C++ Compiler" superClass="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.1234475963">
								<option id="nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.option.debugging.level.684037061" name="Debug Level" superClass="nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.option.debugging.level" value="nds.cpp.compiler.debugging.level.max" valueType="enumerated"/>
							</tool>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug.2074410772" name="Andes C++ Linker" superClass="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug.15660341">
								<option defaultValue="-O0" id="nds.cpp.link.option.optimization.level.611538233" name="Optimization Level" superClass="nds.cpp.link.option.optimization.level" valueType="string"/>
								<option defaultValue="true" id="nds32le-elf-mculib-v3m.cpp.link.option.noshared.base.282488275" name="No shared libraries (-static)" superClass="nds32le-elf-mculib-v3m.cpp.link.option.noshared.base" valueType="boolean"/>
							</tool>
							<tool errorParsers="org.eclipse.cdt.core.GCCErrorParser" id="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.1793820117" name="Andes C Compiler" superClass="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.1245613064">
								<inputType id="tool.nds.c.compiler.input.182660323" superClass="tool.nds.c.compiler.input"/>
							</tool>
							<tool id="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug.1789492899" name="Andes C Linker" superClass="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug.1901902386"/>
							<tool errorParsers="org.eclipse.cdt.core.GASErrorParser" id="tool.nds32le-elf-mculib-v3m.assembler.exe.debug.462647401" name="Andes Assembler" superClass="tool.nds32le-elf-mculib-v3m.assembler.exe.debug.1889835845">
								<inputType id="tool.nds.assembler.input.1516760562" superClass="tool.nds.assembler.input"/>
							</tool>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.nm.exe.debug.568752400" name="NM (symbol listing)" superClass="tool.nds32le-elf-mculib-v3m.nm.exe.debug.1552418575"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.readelf.exe.debug.1263002969" name="Readelf (ELF info listing)" superClass="tool.nds32le-elf-mculib-v3m.readelf.exe.debug.1587875284"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.objdump.exe.debug.669666676" name="Objdump (disassembly)" superClass="tool.nds32le-elf-mculib-v3m.objdump.exe.debug.1025756138"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug.1847893848" name="Objcopy (object content copy)" superClass="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug.263881946"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.size.exe.debug.786059296" name="Size (section size listing)" superClass="tool.nds32le-elf-mculib-v3m.size.exe.debug.1428327725"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug.577478094" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug.68400824"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.ldsag.base.124305928" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.base.634805832"/>
						</toolChain>
					</folderInfo>
					<sourceEntries>
						<entry excluding="opt_master_only|Src_KIC|shr/inc/shr_types.h" flags="VALUE_WORKSPACE_PATH" kind="sourcePath" name=""/>
					</sourceEntries>
				</configuration>
			</storageModule>
			<storageModule moduleId="org.eclipse.cdt.core.externalSettings"/>
		</cconfiguration>
		<cconfiguration id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.826799903">
			<storageModule buildSystemId="org.eclipse.cdt.managedbuilder.core.configurationDataProvider" id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.826799903" moduleId="org.eclipse.cdt.core.settings" name="PS5017_Hynix_V8">
				<externalSettings/>
				<extensions>
					<extension id="com.andestech.ide.cdt.managedbuilder.core.CROSS_GNU_ELF" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="org.eclipse.cdt.core.GmakeErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.CWDLocator" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GCCErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GASErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GLDErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
				</extensions>
			</storageModule>
			<storageModule moduleId="cdtBuildSystem" version="4.0.0">
				<configuration artifactExtension="adx" artifactName="${ProjName}" buildArtefactType="nds.buildArtefactType.exe" buildProperties="org.eclipse.cdt.build.core.buildType=org.eclipse.cdt.build.core.buildType.debug,org.eclipse.cdt.build.core.buildArtefactType=nds.buildArtefactType.exe" description="" errorParsers="org.eclipse.cdt.core.GmakeErrorParser;org.eclipse.cdt.core.CWDLocator;org.eclipse.cdt.core.GCCErrorParser;org.eclipse.cdt.core.GASErrorParser;org.eclipse.cdt.core.GLDErrorParser" id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.826799903" name="PS5017_Hynix_V8" parent="config.nds32le-elf-mculib-v3m.exe.debug" postannouncebuildStep="" postbuildStep="" preannouncebuildStep="" prebuildStep="">
					<folderInfo id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.826799903." name="/" resourcePath="">
						<toolChain errorParsers="" id="nds.nds32le-elf-mculib-v3m.exe.debug.1409493732" name="nds32le-elf-mculib-v3m" superClass="nds.nds32le-elf-mculib-v3m.exe.debug">
							<option id="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET.388771549" name="RSE_TARGET" superClass="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET" value="rse://LOCALHOST/~toolchains/nds32le-elf-mculib-v3m" valueType="string"/>
							<targetPlatform binaryParser="com.andestech.ide.cdt.managedbuilder.core.CROSS_GNU_ELF" id="target.nds32le-elf-mculib-v3m.platform.exe.debug.1686413359" name="Debug Platform" superClass="target.nds32le-elf-mculib-v3m.platform.exe.debug"/>
							<builder buildPath="${workspace_loc:/ps5008_opt/Debug}" errorParsers="org.eclipse.cdt.core.GmakeErrorParser;org.eclipse.cdt.core.CWDLocator" id="target.nds32le-elf-mculib-v3m.builder.exe.debug.1642896025" keepEnvironmentInBuildfile="false" managedBuildOn="true" name="Andes Make Builder" parallelBuildOn="false" parallelizationNumber="-1" superClass="target.nds32le-elf-mculib-v3m.builder.exe.debug"/>
							<tool id="tool.nds32le-elf-mculib-v3m.archiver.base.885314444" name="Andes Archiver" superClass="tool.nds32le-elf-mculib-v3m.archiver.base"/>
							<tool command="$(CROSS_COMPILE)gcc" commandLinePattern="${COMMAND} ${FLAGS} ${OUTPUT_FLAG} ${OUTPUT_PREFIX}${OUTPUT} ${INPUTS}" errorParsers="org.eclipse.cdt.core.GCCErrorParser" id="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.601670068" name="Andes C Compiler" superClass="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug">
								<option id="nds32le-elf-mculib-v3m.c.compiler.exe.debug.option.debugging.level.200853586" name="Debug Level" superClass="nds32le-elf-mculib-v3m.c.compiler.exe.debug.option.debugging.level" value="nds.c.debugging.level.max" valueType="enumerated"/>
								<option id="nds.c.compiler.option.include.paths.1432824184" name="Include paths (-I)" superClass="nds.c.compiler.option.include.paths" valueType="includePath">
									<listOptionValue builtIn="false" value="&quot;..\opt\inc&quot;"/>
									<listOptionValue builtIn="false" value="&quot;..\opt&quot;"/>
									<listOptionValue builtIn="false" value="&quot;..\shr\inc&quot;"/>
									<listOptionValue builtIn="false" value="&quot;..\startup\inc&quot;"/>
								</option>
								<option id="nds32le-elf-mculib-v3m.c.compiler.exe.debug.option.optimization.level.253323223" name="Optimization Level" superClass="nds32le-elf-mculib-v3m.c.compiler.exe.debug.option.optimization.level" value="nds.c.optimization.level.more" valueType="enumerated"/>
								<option id="nds.c.compiler.option.optimization.removesections.function.315456852" name="Remove unused function sections (-ffunction-sections)" superClass="nds.c.compiler.option.optimization.removesections.function" value="true" valueType="boolean"/>
								<option id="nds.c.compiler.option.optimization.removesections.data.857582787" name="Remove unused data sections (-fdata-sections)" superClass="nds.c.compiler.option.optimization.removesections.data" value="true" valueType="boolean"/>
								<option id="nds.c.compiler.option.cmodel.790738657" name="Code Model" superClass="nds.c.compiler.option.cmodel" value="nds.c.compiler.option.cmodel.medium" valueType="enumerated"/>
								<option id="nds.c.compiler.option.preprocessor.def.symbols.245483066" name="Defined symbols (-D)" superClass="nds.c.compiler.option.preprocessor.def.symbols" valueType="definedSymbols">
									<listOptionValue builtIn="false" value="CATEGORY_CUSTOMER=CUSTOMER_HYNIX"/>
									<listOptionValue builtIn="false" value="CATEGORY_FLASH=FLASH_HYNIX_V8_TLC"/>
									<listOptionValue builtIn="false" value="CATEGORY_CONTROLLER=CONTROLLER_PS5017"/>
								</option>
								<inputType id="tool.nds.c.compiler.input.185037919" superClass="tool.nds.c.compiler.input"/>
							</tool>
							<tool command="$(CROSS_COMPILE)gcc" commandLinePattern="${COMMAND} ${FLAGS} ${OUTPUT_FLAG} ${OUTPUT_PREFIX}${OUTPUT} ${INPUTS}" errorParsers="org.eclipse.cdt.core.GLDErrorParser" id="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug.46651639" name="Andes C Linker" superClass="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug">
								<option defaultValue="-O0" id="nds.c.link.option.optimization.level.1379077813" name="Optimization Level" superClass="nds.c.link.option.optimization.level" value="-O2" valueType="string"/>
								<option defaultValue="true" id="nds32le-elf-mculib-v3m.c.link.option.noshared.base.1961724551" name="No shared libraries (-static)" superClass="nds32le-elf-mculib-v3m.c.link.option.noshared.base" valueType="boolean"/>
								<option defaultValue="" id="nds32le-elf-mculib-v3m.c.link.exe.debug.option.ldscript.1284224874" name="Linker Script (-T)" superClass="nds32le-elf-mculib-v3m.c.link.exe.debug.option.ldscript" value="..\LinkerScript\ps5008_hw.ld" valueType="string"/>
								<option id="nds.c.linker.option.cmodel.1849570598" name="Code Model" superClass="nds.c.linker.option.cmodel" value="-mcmodel=medium" valueType="string"/>
								<option id="nds.c.link.option.map.661663066" name="Write a map file. (-Map)" superClass="nds.c.link.option.map" value="true" valueType="boolean"/>
								<option id="nds.c.link.option.virtualhosting.608745605" name="Virtual Hosting (-mvh)" superClass="nds.c.link.option.virtualhosting" value="false" valueType="boolean"/>
								<option id="nds.c.link.option.nostart.317102121" name="Do not use standard start files (-nostartfiles)" superClass="nds.c.link.option.nostart" value="true" valueType="boolean"/>
								<option id="nds.c.link.option.removesections.1032058567" name="Remove unused sections (-ffunction-sections or -fdata-sections)" superClass="nds.c.link.option.removesections" value="true" valueType="boolean"/>
								<inputType id="tool.nds.c.linker.input.503935479" superClass="tool.nds.c.linker.input">
									<additionalInput kind="additionalinputdependency" paths="$(USER_OBJS)"/>
									<additionalInput kind="additionalinput" paths="$(LIBS)"/>
								</inputType>
							</tool>
							<tool command="$(CROSS_COMPILE)as" commandLinePattern="${COMMAND} ${FLAGS} ${OUTPUT_FLAG} ${OUTPUT_PREFIX}${OUTPUT} ${INPUTS}" errorParsers="org.eclipse.cdt.core.GASErrorParser" id="tool.nds32le-elf-mculib-v3m.assembler.exe.debug.564973600" name="Andes Assembler" superClass="tool.nds32le-elf-mculib-v3m.assembler.exe.debug">
								<option defaultValue="-g" id="nds.both.asm.option.flags.190009079" name="Assembler flags" superClass="nds.both.asm.option.flags" valueType="string"/>
								<inputType id="tool.nds.assembler.input.1454862057" superClass="tool.nds.assembler.input"/>
							</tool>
							<tool command="$(CROSS_COMPILE)nm" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot; ${OUTPUT_FLAG} ${OUTPUT}" errorParsers="" id="tool.nds32le-elf-mculib-v3m.nm.exe.debug.538969130" name="NM (symbol listing)" superClass="tool.nds32le-elf-mculib-v3m.nm.exe.debug">
								<option id="nds.nm.option.sortsymbol.2140779875" name="Sort symbols numerically by address. (-n)" superClass="nds.nm.option.sortsymbol" value="true" valueType="boolean"/>
								<option id="nds.nm.option.printfilename.1087682940" name="Print name of the input file before every symbol. (-A)" superClass="nds.nm.option.printfilename" value="false" valueType="boolean"/>
							</tool>
							<tool command="$(CROSS_COMPILE)readelf" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot; ${OUTPUT_FLAG} ${OUTPUT}" errorParsers="" id="tool.nds32le-elf-mculib-v3m.readelf.exe.debug.1331302660" name="Readelf (ELF info listing)" superClass="tool.nds32le-elf-mculib-v3m.readelf.exe.debug"/>
							<tool command="$(CROSS_COMPILE)objdump" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot; ${OUTPUT_FLAG} ${OUTPUT}" errorParsers="" id="tool.nds32le-elf-mculib-v3m.objdump.exe.debug.85695901" name="Objdump (disassembly)" superClass="tool.nds32le-elf-mculib-v3m.objdump.exe.debug">
								<option id="nds.objdump.option.disable.1858094798" name="Disable. (Do not auto-generate output file.)" superClass="nds.objdump.option.disable" value="false" valueType="boolean"/>
								<option id="nds.objdump.option.linenumber.403769796" name="Include line numbers and filenames in output. (-l)" superClass="nds.objdump.option.linenumber" value="true" valueType="boolean"/>
								<option id="nds.objdump.option.otherflags.752695017" name="Other flags" superClass="nds.objdump.option.otherflags" value="-S" valueType="string"/>
							</tool>
							<tool command="$(CROSS_COMPILE)objcopy" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot; ${OUTPUT}" errorParsers="" id="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug.542113896" name="Objcopy (object content copy)" superClass="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug">
								<option id="nds.objcopy.option.disable.697018973" name="Disable. (Do not auto-generate output file.)" superClass="nds.objcopy.option.disable" value="false" valueType="boolean"/>
								<option id="nds.objcopy.option.stripall.192575353" name="Remove all symbol and relocation information. (-S)" superClass="nds.objcopy.option.stripall" value="false" valueType="boolean"/>
								<option id="nds.objcopy.option.otherflags.2108404090" name="Other flags" superClass="nds.objcopy.option.otherflags" value="-R .flh_iram_fpu.data" valueType="string"/>
							</tool>
							<tool command="$(CROSS_COMPILE)size" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot;" errorParsers="" id="tool.nds32le-elf-mculib-v3m.size.exe.debug.234781842" name="Size (section size listing)" superClass="tool.nds32le-elf-mculib-v3m.size.exe.debug"/>
							<tool command="$(ANDESIGHT_ROOT)/utils/nds_ldsag" commandLinePattern="${COMMAND} ${FLAGS} ${INPUTS} -o $(LDSAG_OUT)" errorParsers="" id="tool.nds32le-elf-mculib-v3m.ldsag.base.1502564886" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.base"/>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.1986969961" name="Andes C++ Compiler" superClass="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug">
								<option id="nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.option.debugging.level.449290023" name="Debug Level" superClass="nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.option.debugging.level" value="nds.cpp.compiler.debugging.level.max" valueType="enumerated"/>
							</tool>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug.1634065956" name="Andes C++ Linker" superClass="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug">
								<option defaultValue="-O0" id="nds.cpp.link.option.optimization.level.552686411" name="Optimization Level" superClass="nds.cpp.link.option.optimization.level" valueType="string"/>
								<option defaultValue="true" id="nds32le-elf-mculib-v3m.cpp.link.option.noshared.base.352634946" name="No shared libraries (-static)" superClass="nds32le-elf-mculib-v3m.cpp.link.option.noshared.base" valueType="boolean"/>
							</tool>
							<tool command="$(ANDESIGHT_ROOT)/utils/nds_ldsag" commandLinePattern="${COMMAND} ${FLAGS} ${INPUTS} -o $(LDSAG_OUT)" errorParsers="" id="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug.1126708557" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug"/>
						</toolChain>
					</folderInfo>
					<folderInfo id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.826799903.opt_master_only" name="/" resourcePath="opt_master_only">
						<toolChain errorParsers="" id="nds.nds32le-elf-mculib-v3m.exe.debug.1117625052" name="nds32le-elf-mculib-v3m" superClass="nds.nds32le-elf-mculib-v3m.exe.debug" unusedChildren="">
							<option id="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET.423936908.313979601.1887125436.1009016960.1709021572.1742216434" name="RSE_TARGET" superClass="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET.423936908"/>
							<targetPlatform id="target.nds32le-elf-mculib-v3m.platform.exe.debug.2117994473" name="Debug Platform" superClass="target.nds32le-elf-mculib-v3m.platform.exe.debug"/>
							<tool id="tool.nds32le-elf-mculib-v3m.archiver.base.1543650750" name="Andes Archiver" superClass="tool.nds32le-elf-mculib-v3m.archiver.base.885314444"/>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.575274127" name="Andes C++ Compiler" superClass="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.1986969961">
								<option id="nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.option.debugging.level.319442449" name="Debug Level" superClass="nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.option.debugging.level" value="nds.cpp.compiler.debugging.level.max" valueType="enumerated"/>
							</tool>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug.733212111" name="Andes C++ Linker" superClass="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug.1634065956">
								<option defaultValue="-O0" id="nds.cpp.link.option.optimization.level.1836757566" name="Optimization Level" superClass="nds.cpp.link.option.optimization.level" valueType="string"/>
								<option defaultValue="true" id="nds32le-elf-mculib-v3m.cpp.link.option.noshared.base.391511176" name="No shared libraries (-static)" superClass="nds32le-elf-mculib-v3m.cpp.link.option.noshared.base" valueType="boolean"/>
							</tool>
							<tool errorParsers="org.eclipse.cdt.core.GCCErrorParser" id="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.1166732605" name="Andes C Compiler" superClass="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.601670068">
								<inputType id="tool.nds.c.compiler.input.187274958" superClass="tool.nds.c.compiler.input"/>
							</tool>
							<tool id="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug.1921912355" name="Andes C Linker" superClass="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug.46651639"/>
							<tool errorParsers="org.eclipse.cdt.core.GASErrorParser" id="tool.nds32le-elf-mculib-v3m.assembler.exe.debug.794213589" name="Andes Assembler" superClass="tool.nds32le-elf-mculib-v3m.assembler.exe.debug.564973600">
								<inputType id="tool.nds.assembler.input.2078784241" superClass="tool.nds.assembler.input"/>
							</tool>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.nm.exe.debug.131323551" name="NM (symbol listing)" superClass="tool.nds32le-elf-mculib-v3m.nm.exe.debug.538969130"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.readelf.exe.debug.340296621" name="Readelf (ELF info listing)" superClass="tool.nds32le-elf-mculib-v3m.readelf.exe.debug.1331302660"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.objdump.exe.debug.499052780" name="Objdump (disassembly)" superClass="tool.nds32le-elf-mculib-v3m.objdump.exe.debug.85695901"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug.682957879" name="Objcopy (object content copy)" superClass="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug.542113896"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.size.exe.debug.1914077058" name="Size (section size listing)" superClass="tool.nds32le-elf-mculib-v3m.size.exe.debug.234781842"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug.1750986388" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug.1126708557"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.ldsag.base.161928580" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.base.1502564886"/>
						</toolChain>
					</folderInfo>
					<sourceEntries>
						<entry excluding="opt_master_only|Src_KIC|shr/inc/shr_types.h" flags="VALUE_WORKSPACE_PATH" kind="sourcePath" name=""/>
					</sourceEntries>
				</configuration>
			</storageModule>
			<storageModule moduleId="org.eclipse.cdt.core.externalSettings"/>
		</cconfiguration>
		<cconfiguration id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********">
			<storageModule buildSystemId="org.eclipse.cdt.managedbuilder.core.configurationDataProvider" id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********" moduleId="org.eclipse.cdt.core.settings" name="PS5017_Micron_B47R">
				<externalSettings/>
				<extensions>
					<extension id="com.andestech.ide.cdt.managedbuilder.core.CROSS_GNU_ELF" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="org.eclipse.cdt.core.GmakeErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.CWDLocator" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GCCErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GASErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GLDErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
				</extensions>
			</storageModule>
			<storageModule moduleId="cdtBuildSystem" version="4.0.0">
				<configuration artifactExtension="adx" artifactName="${ProjName}" buildArtefactType="nds.buildArtefactType.exe" buildProperties="org.eclipse.cdt.build.core.buildType=org.eclipse.cdt.build.core.buildType.debug,org.eclipse.cdt.build.core.buildArtefactType=nds.buildArtefactType.exe" description="" errorParsers="org.eclipse.cdt.core.GmakeErrorParser;org.eclipse.cdt.core.CWDLocator;org.eclipse.cdt.core.GCCErrorParser;org.eclipse.cdt.core.GASErrorParser;org.eclipse.cdt.core.GLDErrorParser" id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********" name="PS5017_Micron_B47R" parent="config.nds32le-elf-mculib-v3m.exe.debug" postannouncebuildStep="" postbuildStep="" preannouncebuildStep="" prebuildStep="">
					<folderInfo id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********." name="/" resourcePath="">
						<toolChain errorParsers="" id="nds.nds32le-elf-mculib-v3m.exe.debug.782354336" name="nds32le-elf-mculib-v3m" superClass="nds.nds32le-elf-mculib-v3m.exe.debug">
							<option id="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET.1208031625" name="RSE_TARGET" superClass="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET" value="rse://LOCALHOST/~toolchains/nds32le-elf-mculib-v3m" valueType="string"/>
							<targetPlatform binaryParser="com.andestech.ide.cdt.managedbuilder.core.CROSS_GNU_ELF" id="target.nds32le-elf-mculib-v3m.platform.exe.debug.1702239380" name="Debug Platform" superClass="target.nds32le-elf-mculib-v3m.platform.exe.debug"/>
							<builder buildPath="${workspace_loc:/ps5008_opt/Debug}" errorParsers="org.eclipse.cdt.core.GmakeErrorParser;org.eclipse.cdt.core.CWDLocator" id="target.nds32le-elf-mculib-v3m.builder.exe.debug.766807084" keepEnvironmentInBuildfile="false" managedBuildOn="true" name="Andes Make Builder" parallelBuildOn="false" parallelizationNumber="-1" superClass="target.nds32le-elf-mculib-v3m.builder.exe.debug"/>
							<tool id="tool.nds32le-elf-mculib-v3m.archiver.base.337106741" name="Andes Archiver" superClass="tool.nds32le-elf-mculib-v3m.archiver.base"/>
							<tool command="$(CROSS_COMPILE)gcc" commandLinePattern="${COMMAND} ${FLAGS} ${OUTPUT_FLAG} ${OUTPUT_PREFIX}${OUTPUT} ${INPUTS}" errorParsers="org.eclipse.cdt.core.GCCErrorParser" id="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.1083288925" name="Andes C Compiler" superClass="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug">
								<option id="nds32le-elf-mculib-v3m.c.compiler.exe.debug.option.debugging.level.1763028538" name="Debug Level" superClass="nds32le-elf-mculib-v3m.c.compiler.exe.debug.option.debugging.level" value="nds.c.debugging.level.max" valueType="enumerated"/>
								<option id="nds.c.compiler.option.include.paths.1883526181" name="Include paths (-I)" superClass="nds.c.compiler.option.include.paths" valueType="includePath">
									<listOptionValue builtIn="false" value="&quot;..\opt\inc&quot;"/>
									<listOptionValue builtIn="false" value="&quot;..\opt&quot;"/>
									<listOptionValue builtIn="false" value="&quot;..\shr\inc&quot;"/>
									<listOptionValue builtIn="false" value="&quot;..\startup\inc&quot;"/>
								</option>
								<option id="nds32le-elf-mculib-v3m.c.compiler.exe.debug.option.optimization.level.788283125" name="Optimization Level" superClass="nds32le-elf-mculib-v3m.c.compiler.exe.debug.option.optimization.level" value="nds.c.optimization.level.more" valueType="enumerated"/>
								<option id="nds.c.compiler.option.optimization.removesections.function.507825369" name="Remove unused function sections (-ffunction-sections)" superClass="nds.c.compiler.option.optimization.removesections.function" value="true" valueType="boolean"/>
								<option id="nds.c.compiler.option.optimization.removesections.data.101006272" name="Remove unused data sections (-fdata-sections)" superClass="nds.c.compiler.option.optimization.removesections.data" value="true" valueType="boolean"/>
								<option id="nds.c.compiler.option.cmodel.1548388468" name="Code Model" superClass="nds.c.compiler.option.cmodel" value="nds.c.compiler.option.cmodel.medium" valueType="enumerated"/>
								<option id="nds.c.compiler.option.preprocessor.def.symbols.1766226148" name="Defined symbols (-D)" superClass="nds.c.compiler.option.preprocessor.def.symbols" valueType="definedSymbols">
									<listOptionValue builtIn="false" value="CATEGORY_CUSTOMER=CUSTOMER_MICRON"/>
									<listOptionValue builtIn="false" value="CATEGORY_FLASH=FLASH_B47R_TLC"/>
									<listOptionValue builtIn="false" value="CATEGORY_CONTROLLER=CONTROLLER_PS5017"/>
								</option>
								<inputType id="tool.nds.c.compiler.input.1911842201" superClass="tool.nds.c.compiler.input"/>
							</tool>
							<tool command="$(CROSS_COMPILE)gcc" commandLinePattern="${COMMAND} ${FLAGS} ${OUTPUT_FLAG} ${OUTPUT_PREFIX}${OUTPUT} ${INPUTS}" errorParsers="org.eclipse.cdt.core.GLDErrorParser" id="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug.221692075" name="Andes C Linker" superClass="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug">
								<option defaultValue="-O0" id="nds.c.link.option.optimization.level.1616527125" name="Optimization Level" superClass="nds.c.link.option.optimization.level" value="-O2" valueType="string"/>
								<option defaultValue="true" id="nds32le-elf-mculib-v3m.c.link.option.noshared.base.186052431" name="No shared libraries (-static)" superClass="nds32le-elf-mculib-v3m.c.link.option.noshared.base" valueType="boolean"/>
								<option defaultValue="" id="nds32le-elf-mculib-v3m.c.link.exe.debug.option.ldscript.1480559552" name="Linker Script (-T)" superClass="nds32le-elf-mculib-v3m.c.link.exe.debug.option.ldscript" value="..\LinkerScript\ps5008_hw.ld" valueType="string"/>
								<option id="nds.c.linker.option.cmodel.1244163786" name="Code Model" superClass="nds.c.linker.option.cmodel" value="-mcmodel=medium" valueType="string"/>
								<option id="nds.c.link.option.map.1659187992" name="Write a map file. (-Map)" superClass="nds.c.link.option.map" value="true" valueType="boolean"/>
								<option id="nds.c.link.option.virtualhosting.578278370" name="Virtual Hosting (-mvh)" superClass="nds.c.link.option.virtualhosting" value="false" valueType="boolean"/>
								<option id="nds.c.link.option.nostart.563154959" name="Do not use standard start files (-nostartfiles)" superClass="nds.c.link.option.nostart" value="true" valueType="boolean"/>
								<option id="nds.c.link.option.removesections.778681964" name="Remove unused sections (-ffunction-sections or -fdata-sections)" superClass="nds.c.link.option.removesections" value="true" valueType="boolean"/>
								<inputType id="tool.nds.c.linker.input.394619774" superClass="tool.nds.c.linker.input">
									<additionalInput kind="additionalinputdependency" paths="$(USER_OBJS)"/>
									<additionalInput kind="additionalinput" paths="$(LIBS)"/>
								</inputType>
							</tool>
							<tool command="$(CROSS_COMPILE)as" commandLinePattern="${COMMAND} ${FLAGS} ${OUTPUT_FLAG} ${OUTPUT_PREFIX}${OUTPUT} ${INPUTS}" errorParsers="org.eclipse.cdt.core.GASErrorParser" id="tool.nds32le-elf-mculib-v3m.assembler.exe.debug.1625560200" name="Andes Assembler" superClass="tool.nds32le-elf-mculib-v3m.assembler.exe.debug">
								<option defaultValue="-g" id="nds.both.asm.option.flags.563896665" name="Assembler flags" superClass="nds.both.asm.option.flags" valueType="string"/>
								<inputType id="tool.nds.assembler.input.396584070" superClass="tool.nds.assembler.input"/>
							</tool>
							<tool command="$(CROSS_COMPILE)nm" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot; ${OUTPUT_FLAG} ${OUTPUT}" errorParsers="" id="tool.nds32le-elf-mculib-v3m.nm.exe.debug.489052158" name="NM (symbol listing)" superClass="tool.nds32le-elf-mculib-v3m.nm.exe.debug">
								<option id="nds.nm.option.sortsymbol.412020712" name="Sort symbols numerically by address. (-n)" superClass="nds.nm.option.sortsymbol" value="true" valueType="boolean"/>
								<option id="nds.nm.option.printfilename.1923702388" name="Print name of the input file before every symbol. (-A)" superClass="nds.nm.option.printfilename" value="false" valueType="boolean"/>
							</tool>
							<tool command="$(CROSS_COMPILE)readelf" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot; ${OUTPUT_FLAG} ${OUTPUT}" errorParsers="" id="tool.nds32le-elf-mculib-v3m.readelf.exe.debug.1341657385" name="Readelf (ELF info listing)" superClass="tool.nds32le-elf-mculib-v3m.readelf.exe.debug"/>
							<tool command="$(CROSS_COMPILE)objdump" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot; ${OUTPUT_FLAG} ${OUTPUT}" errorParsers="" id="tool.nds32le-elf-mculib-v3m.objdump.exe.debug.1735932337" name="Objdump (disassembly)" superClass="tool.nds32le-elf-mculib-v3m.objdump.exe.debug">
								<option id="nds.objdump.option.disable.1561696245" name="Disable. (Do not auto-generate output file.)" superClass="nds.objdump.option.disable" value="false" valueType="boolean"/>
								<option id="nds.objdump.option.linenumber.2047413622" name="Include line numbers and filenames in output. (-l)" superClass="nds.objdump.option.linenumber" value="true" valueType="boolean"/>
								<option id="nds.objdump.option.otherflags.1660330450" name="Other flags" superClass="nds.objdump.option.otherflags" value="-S" valueType="string"/>
							</tool>
							<tool command="$(CROSS_COMPILE)objcopy" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot; ${OUTPUT}" errorParsers="" id="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug.351292297" name="Objcopy (object content copy)" superClass="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug">
								<option id="nds.objcopy.option.disable.1809057844" name="Disable. (Do not auto-generate output file.)" superClass="nds.objcopy.option.disable" value="false" valueType="boolean"/>
								<option id="nds.objcopy.option.stripall.675371230" name="Remove all symbol and relocation information. (-S)" superClass="nds.objcopy.option.stripall" value="false" valueType="boolean"/>
								<option id="nds.objcopy.option.otherflags.349354875" name="Other flags" superClass="nds.objcopy.option.otherflags" value="-R .flh_iram_fpu.data" valueType="string"/>
							</tool>
							<tool command="$(CROSS_COMPILE)size" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot;" errorParsers="" id="tool.nds32le-elf-mculib-v3m.size.exe.debug.2049649984" name="Size (section size listing)" superClass="tool.nds32le-elf-mculib-v3m.size.exe.debug"/>
							<tool command="$(ANDESIGHT_ROOT)/utils/nds_ldsag" commandLinePattern="${COMMAND} ${FLAGS} ${INPUTS} -o $(LDSAG_OUT)" errorParsers="" id="tool.nds32le-elf-mculib-v3m.ldsag.base.1041983772" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.base"/>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.1857549187" name="Andes C++ Compiler" superClass="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug">
								<option id="nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.option.debugging.level.1248830586" name="Debug Level" superClass="nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.option.debugging.level" value="nds.cpp.compiler.debugging.level.max" valueType="enumerated"/>
							</tool>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug.319971465" name="Andes C++ Linker" superClass="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug">
								<option defaultValue="-O0" id="nds.cpp.link.option.optimization.level.141473089" name="Optimization Level" superClass="nds.cpp.link.option.optimization.level" valueType="string"/>
								<option defaultValue="true" id="nds32le-elf-mculib-v3m.cpp.link.option.noshared.base.1529876288" name="No shared libraries (-static)" superClass="nds32le-elf-mculib-v3m.cpp.link.option.noshared.base" valueType="boolean"/>
							</tool>
							<tool command="$(ANDESIGHT_ROOT)/utils/nds_ldsag" commandLinePattern="${COMMAND} ${FLAGS} ${INPUTS} -o $(LDSAG_OUT)" errorParsers="" id="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug.814343191" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug"/>
						</toolChain>
					</folderInfo>
					<folderInfo id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********.opt_master_only" name="/" resourcePath="opt_master_only">
						<toolChain errorParsers="" id="nds.nds32le-elf-mculib-v3m.exe.debug.375050449" name="nds32le-elf-mculib-v3m" superClass="nds.nds32le-elf-mculib-v3m.exe.debug" unusedChildren="">
							<option id="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET.423936908.313979601.1887125436.1009016960.1709021572.8634972" name="RSE_TARGET" superClass="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET.423936908"/>
							<targetPlatform id="target.nds32le-elf-mculib-v3m.platform.exe.debug.344108452" name="Debug Platform" superClass="target.nds32le-elf-mculib-v3m.platform.exe.debug"/>
							<tool id="tool.nds32le-elf-mculib-v3m.archiver.base.382789457" name="Andes Archiver" superClass="tool.nds32le-elf-mculib-v3m.archiver.base.337106741"/>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.1223382139" name="Andes C++ Compiler" superClass="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.1857549187">
								<option id="nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.option.debugging.level.1967143328" name="Debug Level" superClass="nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.option.debugging.level" value="nds.cpp.compiler.debugging.level.max" valueType="enumerated"/>
							</tool>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug.402049092" name="Andes C++ Linker" superClass="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug.319971465">
								<option defaultValue="-O0" id="nds.cpp.link.option.optimization.level.696215794" name="Optimization Level" superClass="nds.cpp.link.option.optimization.level" valueType="string"/>
								<option defaultValue="true" id="nds32le-elf-mculib-v3m.cpp.link.option.noshared.base.1754220663" name="No shared libraries (-static)" superClass="nds32le-elf-mculib-v3m.cpp.link.option.noshared.base" valueType="boolean"/>
							</tool>
							<tool errorParsers="org.eclipse.cdt.core.GCCErrorParser" id="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.1858877655" name="Andes C Compiler" superClass="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.1083288925">
								<inputType id="tool.nds.c.compiler.input.521222713" superClass="tool.nds.c.compiler.input"/>
							</tool>
							<tool id="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug.1694692313" name="Andes C Linker" superClass="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug.221692075"/>
							<tool errorParsers="org.eclipse.cdt.core.GASErrorParser" id="tool.nds32le-elf-mculib-v3m.assembler.exe.debug.1114454408" name="Andes Assembler" superClass="tool.nds32le-elf-mculib-v3m.assembler.exe.debug.1625560200">
								<inputType id="tool.nds.assembler.input.344693143" superClass="tool.nds.assembler.input"/>
							</tool>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.nm.exe.debug.1782794269" name="NM (symbol listing)" superClass="tool.nds32le-elf-mculib-v3m.nm.exe.debug.489052158"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.readelf.exe.debug.1396374584" name="Readelf (ELF info listing)" superClass="tool.nds32le-elf-mculib-v3m.readelf.exe.debug.1341657385"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.objdump.exe.debug.1298846588" name="Objdump (disassembly)" superClass="tool.nds32le-elf-mculib-v3m.objdump.exe.debug.1735932337"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug.646675797" name="Objcopy (object content copy)" superClass="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug.351292297"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.size.exe.debug.144607241" name="Size (section size listing)" superClass="tool.nds32le-elf-mculib-v3m.size.exe.debug.2049649984"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug.95828117" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug.814343191"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.ldsag.base.1377063164" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.base.1041983772"/>
						</toolChain>
					</folderInfo>
					<sourceEntries>
						<entry excluding="opt_master_only|Src_KIC|shr/inc/shr_types.h" flags="VALUE_WORKSPACE_PATH" kind="sourcePath" name=""/>
					</sourceEntries>
				</configuration>
			</storageModule>
			<storageModule moduleId="org.eclipse.cdt.core.externalSettings"/>
		</cconfiguration>
		<cconfiguration id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********.134906153.**********">
			<storageModule buildSystemId="org.eclipse.cdt.managedbuilder.core.configurationDataProvider" id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********.134906153.**********" moduleId="org.eclipse.cdt.core.settings" name="PS5017_BICS8">
				<externalSettings/>
				<extensions>
					<extension id="com.andestech.ide.cdt.managedbuilder.core.CROSS_GNU_ELF" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="org.eclipse.cdt.core.GmakeErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.CWDLocator" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GCCErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GASErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GLDErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
				</extensions>
			</storageModule>
			<storageModule moduleId="cdtBuildSystem" version="4.0.0">
				<configuration artifactExtension="adx" artifactName="${ProjName}" buildArtefactType="nds.buildArtefactType.exe" buildProperties="org.eclipse.cdt.build.core.buildType=org.eclipse.cdt.build.core.buildType.debug,org.eclipse.cdt.build.core.buildArtefactType=nds.buildArtefactType.exe" description="" errorParsers="org.eclipse.cdt.core.GmakeErrorParser;org.eclipse.cdt.core.CWDLocator;org.eclipse.cdt.core.GCCErrorParser;org.eclipse.cdt.core.GASErrorParser;org.eclipse.cdt.core.GLDErrorParser" id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********.134906153.**********" name="PS5017_BICS8" parent="config.nds32le-elf-mculib-v3m.exe.debug" postannouncebuildStep="" postbuildStep="" preannouncebuildStep="" prebuildStep="">
					<folderInfo id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********.134906153.**********." name="/" resourcePath="">
						<toolChain errorParsers="" id="nds.nds32le-elf-mculib-v3m.exe.debug.1041442227" name="nds32le-elf-mculib-v3m" superClass="nds.nds32le-elf-mculib-v3m.exe.debug">
							<option id="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET.1609956608" name="RSE_TARGET" superClass="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET" value="rse://LOCALHOST/~toolchains/nds32le-elf-mculib-v3m" valueType="string"/>
							<targetPlatform binaryParser="com.andestech.ide.cdt.managedbuilder.core.CROSS_GNU_ELF" id="target.nds32le-elf-mculib-v3m.platform.exe.debug.2111646050" name="Debug Platform" superClass="target.nds32le-elf-mculib-v3m.platform.exe.debug"/>
							<builder buildPath="${workspace_loc:/ps5008_opt/Debug}" errorParsers="org.eclipse.cdt.core.GmakeErrorParser;org.eclipse.cdt.core.CWDLocator" id="target.nds32le-elf-mculib-v3m.builder.exe.debug.584430933" keepEnvironmentInBuildfile="false" managedBuildOn="true" name="Andes Make Builder" parallelBuildOn="false" parallelizationNumber="-1" superClass="target.nds32le-elf-mculib-v3m.builder.exe.debug"/>
							<tool id="tool.nds32le-elf-mculib-v3m.archiver.base.1407988558" name="Andes Archiver" superClass="tool.nds32le-elf-mculib-v3m.archiver.base"/>
							<tool command="$(CROSS_COMPILE)gcc" commandLinePattern="${COMMAND} ${FLAGS} ${OUTPUT_FLAG} ${OUTPUT_PREFIX}${OUTPUT} ${INPUTS}" errorParsers="org.eclipse.cdt.core.GCCErrorParser" id="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.1037714393" name="Andes C Compiler" superClass="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug">
								<option id="nds32le-elf-mculib-v3m.c.compiler.exe.debug.option.debugging.level.1507553422" name="Debug Level" superClass="nds32le-elf-mculib-v3m.c.compiler.exe.debug.option.debugging.level" value="nds.c.debugging.level.max" valueType="enumerated"/>
								<option id="nds.c.compiler.option.include.paths.441181837" name="Include paths (-I)" superClass="nds.c.compiler.option.include.paths" valueType="includePath">
									<listOptionValue builtIn="false" value="&quot;..\opt\inc&quot;"/>
									<listOptionValue builtIn="false" value="&quot;..\opt&quot;"/>
									<listOptionValue builtIn="false" value="&quot;..\shr\inc&quot;"/>
									<listOptionValue builtIn="false" value="&quot;..\startup\inc&quot;"/>
								</option>
								<option id="nds32le-elf-mculib-v3m.c.compiler.exe.debug.option.optimization.level.596413451" name="Optimization Level" superClass="nds32le-elf-mculib-v3m.c.compiler.exe.debug.option.optimization.level" value="nds.c.optimization.level.more" valueType="enumerated"/>
								<option id="nds.c.compiler.option.optimization.removesections.function.776241618" name="Remove unused function sections (-ffunction-sections)" superClass="nds.c.compiler.option.optimization.removesections.function" value="true" valueType="boolean"/>
								<option id="nds.c.compiler.option.optimization.removesections.data.1634176111" name="Remove unused data sections (-fdata-sections)" superClass="nds.c.compiler.option.optimization.removesections.data" value="true" valueType="boolean"/>
								<option id="nds.c.compiler.option.cmodel.675711056" name="Code Model" superClass="nds.c.compiler.option.cmodel" value="nds.c.compiler.option.cmodel.medium" valueType="enumerated"/>
								<option id="nds.c.compiler.option.preprocessor.def.symbols.1018016847" name="Defined symbols (-D)" superClass="nds.c.compiler.option.preprocessor.def.symbols" valueType="definedSymbols">
									<listOptionValue builtIn="false" value="CATEGORY_CUSTOMER=CUSTOMER_SANDISK"/>
									<listOptionValue builtIn="false" value="CATEGORY_FLASH=FLASH_SANDISK_BICS8_TLC"/>
									<listOptionValue builtIn="false" value="CATEGORY_CONTROLLER=CONTROLLER_PS5017"/>
								</option>
								<inputType id="tool.nds.c.compiler.input.881008052" superClass="tool.nds.c.compiler.input"/>
							</tool>
							<tool command="$(CROSS_COMPILE)gcc" commandLinePattern="${COMMAND} ${FLAGS} ${OUTPUT_FLAG} ${OUTPUT_PREFIX}${OUTPUT} ${INPUTS}" errorParsers="org.eclipse.cdt.core.GLDErrorParser" id="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug.2076352237" name="Andes C Linker" superClass="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug">
								<option defaultValue="-O0" id="nds.c.link.option.optimization.level.1720177201" name="Optimization Level" superClass="nds.c.link.option.optimization.level" value="-O2" valueType="string"/>
								<option defaultValue="true" id="nds32le-elf-mculib-v3m.c.link.option.noshared.base.1051840438" name="No shared libraries (-static)" superClass="nds32le-elf-mculib-v3m.c.link.option.noshared.base" valueType="boolean"/>
								<option defaultValue="" id="nds32le-elf-mculib-v3m.c.link.exe.debug.option.ldscript.1342851374" name="Linker Script (-T)" superClass="nds32le-elf-mculib-v3m.c.link.exe.debug.option.ldscript" value="..\LinkerScript\ps5008_hw.ld" valueType="string"/>
								<option id="nds.c.linker.option.cmodel.1313738685" name="Code Model" superClass="nds.c.linker.option.cmodel" value="-mcmodel=medium" valueType="string"/>
								<option id="nds.c.link.option.map.383257208" name="Write a map file. (-Map)" superClass="nds.c.link.option.map" value="true" valueType="boolean"/>
								<option id="nds.c.link.option.virtualhosting.970158670" name="Virtual Hosting (-mvh)" superClass="nds.c.link.option.virtualhosting" value="false" valueType="boolean"/>
								<option id="nds.c.link.option.nostart.2019414936" name="Do not use standard start files (-nostartfiles)" superClass="nds.c.link.option.nostart" value="true" valueType="boolean"/>
								<option id="nds.c.link.option.removesections.325350126" name="Remove unused sections (-ffunction-sections or -fdata-sections)" superClass="nds.c.link.option.removesections" value="true" valueType="boolean"/>
								<inputType id="tool.nds.c.linker.input.363803989" superClass="tool.nds.c.linker.input">
									<additionalInput kind="additionalinputdependency" paths="$(USER_OBJS)"/>
									<additionalInput kind="additionalinput" paths="$(LIBS)"/>
								</inputType>
							</tool>
							<tool command="$(CROSS_COMPILE)as" commandLinePattern="${COMMAND} ${FLAGS} ${OUTPUT_FLAG} ${OUTPUT_PREFIX}${OUTPUT} ${INPUTS}" errorParsers="org.eclipse.cdt.core.GASErrorParser" id="tool.nds32le-elf-mculib-v3m.assembler.exe.debug.1386064292" name="Andes Assembler" superClass="tool.nds32le-elf-mculib-v3m.assembler.exe.debug">
								<option defaultValue="-g" id="nds.both.asm.option.flags.1039484147" name="Assembler flags" superClass="nds.both.asm.option.flags" valueType="string"/>
								<inputType id="tool.nds.assembler.input.710805297" superClass="tool.nds.assembler.input"/>
							</tool>
							<tool command="$(CROSS_COMPILE)nm" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot; ${OUTPUT_FLAG} ${OUTPUT}" errorParsers="" id="tool.nds32le-elf-mculib-v3m.nm.exe.debug.1435229134" name="NM (symbol listing)" superClass="tool.nds32le-elf-mculib-v3m.nm.exe.debug">
								<option id="nds.nm.option.sortsymbol.225048572" name="Sort symbols numerically by address. (-n)" superClass="nds.nm.option.sortsymbol" value="true" valueType="boolean"/>
								<option id="nds.nm.option.printfilename.1658540053" name="Print name of the input file before every symbol. (-A)" superClass="nds.nm.option.printfilename" value="false" valueType="boolean"/>
							</tool>
							<tool command="$(CROSS_COMPILE)readelf" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot; ${OUTPUT_FLAG} ${OUTPUT}" errorParsers="" id="tool.nds32le-elf-mculib-v3m.readelf.exe.debug.359613485" name="Readelf (ELF info listing)" superClass="tool.nds32le-elf-mculib-v3m.readelf.exe.debug"/>
							<tool command="$(CROSS_COMPILE)objdump" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot; ${OUTPUT_FLAG} ${OUTPUT}" errorParsers="" id="tool.nds32le-elf-mculib-v3m.objdump.exe.debug.1237981787" name="Objdump (disassembly)" superClass="tool.nds32le-elf-mculib-v3m.objdump.exe.debug">
								<option id="nds.objdump.option.disable.1193270953" name="Disable. (Do not auto-generate output file.)" superClass="nds.objdump.option.disable" value="false" valueType="boolean"/>
								<option id="nds.objdump.option.linenumber.1183723318" name="Include line numbers and filenames in output. (-l)" superClass="nds.objdump.option.linenumber" value="true" valueType="boolean"/>
								<option id="nds.objdump.option.otherflags.259566833" name="Other flags" superClass="nds.objdump.option.otherflags" value="-S" valueType="string"/>
							</tool>
							<tool command="$(CROSS_COMPILE)objcopy" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot; ${OUTPUT}" errorParsers="" id="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug.1114840579" name="Objcopy (object content copy)" superClass="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug">
								<option id="nds.objcopy.option.disable.1430566075" name="Disable. (Do not auto-generate output file.)" superClass="nds.objcopy.option.disable" value="false" valueType="boolean"/>
								<option id="nds.objcopy.option.stripall.2046636435" name="Remove all symbol and relocation information. (-S)" superClass="nds.objcopy.option.stripall" value="false" valueType="boolean"/>
								<option id="nds.objcopy.option.otherflags.2065071119" name="Other flags" superClass="nds.objcopy.option.otherflags" value="-R .flh_iram_fpu.data" valueType="string"/>
							</tool>
							<tool command="$(CROSS_COMPILE)size" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot;" errorParsers="" id="tool.nds32le-elf-mculib-v3m.size.exe.debug.1038212830" name="Size (section size listing)" superClass="tool.nds32le-elf-mculib-v3m.size.exe.debug"/>
							<tool command="$(ANDESIGHT_ROOT)/utils/nds_ldsag" commandLinePattern="${COMMAND} ${FLAGS} ${INPUTS} -o $(LDSAG_OUT)" errorParsers="" id="tool.nds32le-elf-mculib-v3m.ldsag.base.2045853749" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.base"/>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.699659230" name="Andes C++ Compiler" superClass="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug">
								<option id="nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.option.debugging.level.1720759663" name="Debug Level" superClass="nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.option.debugging.level" value="nds.cpp.compiler.debugging.level.max" valueType="enumerated"/>
							</tool>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug.1533043716" name="Andes C++ Linker" superClass="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug">
								<option defaultValue="-O0" id="nds.cpp.link.option.optimization.level.1660640246" name="Optimization Level" superClass="nds.cpp.link.option.optimization.level" valueType="string"/>
								<option defaultValue="true" id="nds32le-elf-mculib-v3m.cpp.link.option.noshared.base.1974018954" name="No shared libraries (-static)" superClass="nds32le-elf-mculib-v3m.cpp.link.option.noshared.base" valueType="boolean"/>
							</tool>
							<tool command="$(ANDESIGHT_ROOT)/utils/nds_ldsag" commandLinePattern="${COMMAND} ${FLAGS} ${INPUTS} -o $(LDSAG_OUT)" errorParsers="" id="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug.134891866" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug"/>
						</toolChain>
					</folderInfo>
					<folderInfo id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********.134906153.**********.PS5017_BISC5" name="/" resourcePath="PS5017_BISC5">
						<toolChain id="nds.nds32le-elf-mculib-v3m.exe.debug.811268133" name="nds32le-elf-mculib-v3m" superClass="nds.nds32le-elf-mculib-v3m.exe.debug" unusedChildren="">
							<option id="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET.683971888.2125547364.913307565.322092074" name="RSE_TARGET" superClass="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET.683971888"/>
							<targetPlatform id="target.nds32le-elf-mculib-v3m.platform.exe.debug.940637364" name="Debug Platform" superClass="target.nds32le-elf-mculib-v3m.platform.exe.debug"/>
							<tool id="tool.nds32le-elf-mculib-v3m.archiver.base.1776489877" name="Andes Archiver" superClass="tool.nds32le-elf-mculib-v3m.archiver.base.1407988558"/>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.48882309" name="Andes C++ Compiler" superClass="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.699659230"/>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug.709653998" name="Andes C++ Linker" superClass="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug.1533043716"/>
							<tool id="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.253891993" name="Andes C Compiler" superClass="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.1037714393">
								<option id="nds.c.compiler.option.preprocessor.def.symbols.465605198" name="Defined symbols (-D)" superClass="nds.c.compiler.option.preprocessor.def.symbols" valueType="definedSymbols">
									<listOptionValue builtIn="false" value="CATEGORY_CUSTOMER=CUSTOMER_SANDISK"/>
									<listOptionValue builtIn="false" value="CATEGORY_FLASH=FLASH_SANDISK_BICS5_TLC"/>
									<listOptionValue builtIn="false" value="CATEGORY_CONTROLLER=CONTROLLER_PS5017"/>
								</option>
								<inputType id="tool.nds.c.compiler.input.333341659" superClass="tool.nds.c.compiler.input"/>
							</tool>
							<tool id="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug.639711665" name="Andes C Linker" superClass="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug.2076352237"/>
							<tool id="tool.nds32le-elf-mculib-v3m.assembler.exe.debug.868651182" name="Andes Assembler" superClass="tool.nds32le-elf-mculib-v3m.assembler.exe.debug.1386064292">
								<inputType id="tool.nds.assembler.input.942837255" superClass="tool.nds.assembler.input"/>
							</tool>
							<tool id="tool.nds32le-elf-mculib-v3m.nm.exe.debug.427363764" name="NM (symbol listing)" superClass="tool.nds32le-elf-mculib-v3m.nm.exe.debug.1435229134"/>
							<tool id="tool.nds32le-elf-mculib-v3m.readelf.exe.debug.256310138" name="Readelf (ELF info listing)" superClass="tool.nds32le-elf-mculib-v3m.readelf.exe.debug.359613485"/>
							<tool id="tool.nds32le-elf-mculib-v3m.objdump.exe.debug.1211585892" name="Objdump (disassembly)" superClass="tool.nds32le-elf-mculib-v3m.objdump.exe.debug.1237981787"/>
							<tool id="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug.309923616" name="Objcopy (object content copy)" superClass="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug.1114840579"/>
							<tool id="tool.nds32le-elf-mculib-v3m.size.exe.debug.682347126" name="Size (section size listing)" superClass="tool.nds32le-elf-mculib-v3m.size.exe.debug.1038212830"/>
							<tool id="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug.692731414" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug.134891866"/>
							<tool id="tool.nds32le-elf-mculib-v3m.ldsag.base.1321479050" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.base.2045853749"/>
						</toolChain>
					</folderInfo>
					<folderInfo id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********.134906153.**********.opt_master_only" name="/" resourcePath="opt_master_only">
						<toolChain errorParsers="" id="nds.nds32le-elf-mculib-v3m.exe.debug.1949533439" name="nds32le-elf-mculib-v3m" superClass="nds.nds32le-elf-mculib-v3m.exe.debug" unusedChildren="">
							<option id="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET.423936908.313979601.1887125436.1009016960.1709021572.1412875331.1151677017.696906217" name="RSE_TARGET" superClass="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET.423936908"/>
							<targetPlatform id="target.nds32le-elf-mculib-v3m.platform.exe.debug.1695567836" name="Debug Platform" superClass="target.nds32le-elf-mculib-v3m.platform.exe.debug"/>
							<tool id="tool.nds32le-elf-mculib-v3m.archiver.base.606090950" name="Andes Archiver" superClass="tool.nds32le-elf-mculib-v3m.archiver.base.1407988558"/>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.1287551987" name="Andes C++ Compiler" superClass="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.699659230">
								<option id="nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.option.debugging.level.234509435" name="Debug Level" superClass="nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.option.debugging.level" value="nds.cpp.compiler.debugging.level.max" valueType="enumerated"/>
							</tool>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug.970702293" name="Andes C++ Linker" superClass="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug.1533043716">
								<option defaultValue="-O0" id="nds.cpp.link.option.optimization.level.2024468765" name="Optimization Level" superClass="nds.cpp.link.option.optimization.level" valueType="string"/>
								<option defaultValue="true" id="nds32le-elf-mculib-v3m.cpp.link.option.noshared.base.1849259450" name="No shared libraries (-static)" superClass="nds32le-elf-mculib-v3m.cpp.link.option.noshared.base" valueType="boolean"/>
							</tool>
							<tool errorParsers="org.eclipse.cdt.core.GCCErrorParser" id="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.712680518" name="Andes C Compiler" superClass="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.1037714393">
								<inputType id="tool.nds.c.compiler.input.1747549753" superClass="tool.nds.c.compiler.input"/>
							</tool>
							<tool id="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug.1185847604" name="Andes C Linker" superClass="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug.2076352237"/>
							<tool errorParsers="org.eclipse.cdt.core.GASErrorParser" id="tool.nds32le-elf-mculib-v3m.assembler.exe.debug.1315363520" name="Andes Assembler" superClass="tool.nds32le-elf-mculib-v3m.assembler.exe.debug.1386064292">
								<inputType id="tool.nds.assembler.input.382276021" superClass="tool.nds.assembler.input"/>
							</tool>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.nm.exe.debug.1350383892" name="NM (symbol listing)" superClass="tool.nds32le-elf-mculib-v3m.nm.exe.debug.1435229134"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.readelf.exe.debug.1883808173" name="Readelf (ELF info listing)" superClass="tool.nds32le-elf-mculib-v3m.readelf.exe.debug.359613485"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.objdump.exe.debug.1512375865" name="Objdump (disassembly)" superClass="tool.nds32le-elf-mculib-v3m.objdump.exe.debug.1237981787"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug.1300254514" name="Objcopy (object content copy)" superClass="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug.1114840579"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.size.exe.debug.2077833755" name="Size (section size listing)" superClass="tool.nds32le-elf-mculib-v3m.size.exe.debug.1038212830"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug.1535436859" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug.134891866"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.ldsag.base.1707875377" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.base.2045853749"/>
						</toolChain>
					</folderInfo>
					<sourceEntries>
						<entry excluding="opt_master_only|Src_KIC|shr/inc/shr_types.h" flags="VALUE_WORKSPACE_PATH" kind="sourcePath" name=""/>
					</sourceEntries>
				</configuration>
			</storageModule>
			<storageModule moduleId="org.eclipse.cdt.core.externalSettings"/>
		</cconfiguration>
		<cconfiguration id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********.112201386">
			<storageModule buildSystemId="org.eclipse.cdt.managedbuilder.core.configurationDataProvider" id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********.112201386" moduleId="org.eclipse.cdt.core.settings" name="PS5017_YMTC_WTS">
				<externalSettings/>
				<extensions>
					<extension id="com.andestech.ide.cdt.managedbuilder.core.CROSS_GNU_ELF" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="org.eclipse.cdt.core.GmakeErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.CWDLocator" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GCCErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GASErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GLDErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
				</extensions>
			</storageModule>
			<storageModule moduleId="cdtBuildSystem" version="4.0.0">
				<configuration artifactExtension="adx" artifactName="${ProjName}" buildArtefactType="nds.buildArtefactType.exe" buildProperties="org.eclipse.cdt.build.core.buildType=org.eclipse.cdt.build.core.buildType.debug,org.eclipse.cdt.build.core.buildArtefactType=nds.buildArtefactType.exe" description="" errorParsers="org.eclipse.cdt.core.GmakeErrorParser;org.eclipse.cdt.core.CWDLocator;org.eclipse.cdt.core.GCCErrorParser;org.eclipse.cdt.core.GASErrorParser;org.eclipse.cdt.core.GLDErrorParser" id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********.112201386" name="PS5017_YMTC_WTS" parent="config.nds32le-elf-mculib-v3m.exe.debug" postannouncebuildStep="" postbuildStep="" preannouncebuildStep="" prebuildStep="">
					<folderInfo id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********.112201386." name="/" resourcePath="">
						<toolChain errorParsers="" id="nds.nds32le-elf-mculib-v3m.exe.debug.1274952992" name="nds32le-elf-mculib-v3m" superClass="nds.nds32le-elf-mculib-v3m.exe.debug">
							<option id="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET.867972011" name="RSE_TARGET" superClass="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET" value="rse://LOCALHOST/~toolchains/nds32le-elf-mculib-v3m" valueType="string"/>
							<targetPlatform binaryParser="com.andestech.ide.cdt.managedbuilder.core.CROSS_GNU_ELF" id="target.nds32le-elf-mculib-v3m.platform.exe.debug.1671840353" name="Debug Platform" superClass="target.nds32le-elf-mculib-v3m.platform.exe.debug"/>
							<builder buildPath="${workspace_loc:/ps5008_opt/Debug}" errorParsers="org.eclipse.cdt.core.GmakeErrorParser;org.eclipse.cdt.core.CWDLocator" id="target.nds32le-elf-mculib-v3m.builder.exe.debug.1822903610" keepEnvironmentInBuildfile="false" managedBuildOn="true" name="Andes Make Builder" parallelBuildOn="false" parallelizationNumber="-1" superClass="target.nds32le-elf-mculib-v3m.builder.exe.debug"/>
							<tool id="tool.nds32le-elf-mculib-v3m.archiver.base.1450153053" name="Andes Archiver" superClass="tool.nds32le-elf-mculib-v3m.archiver.base"/>
							<tool command="$(CROSS_COMPILE)gcc" commandLinePattern="${COMMAND} ${FLAGS} ${OUTPUT_FLAG} ${OUTPUT_PREFIX}${OUTPUT} ${INPUTS}" errorParsers="org.eclipse.cdt.core.GCCErrorParser" id="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.2036747734" name="Andes C Compiler" superClass="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug">
								<option id="nds32le-elf-mculib-v3m.c.compiler.exe.debug.option.debugging.level.201319821" name="Debug Level" superClass="nds32le-elf-mculib-v3m.c.compiler.exe.debug.option.debugging.level" value="nds.c.debugging.level.max" valueType="enumerated"/>
								<option id="nds.c.compiler.option.include.paths.621353327" name="Include paths (-I)" superClass="nds.c.compiler.option.include.paths" valueType="includePath">
									<listOptionValue builtIn="false" value="&quot;..\opt\inc&quot;"/>
									<listOptionValue builtIn="false" value="&quot;..\opt&quot;"/>
									<listOptionValue builtIn="false" value="&quot;..\shr\inc&quot;"/>
									<listOptionValue builtIn="false" value="&quot;..\startup\inc&quot;"/>
								</option>
								<option id="nds32le-elf-mculib-v3m.c.compiler.exe.debug.option.optimization.level.1188873347" name="Optimization Level" superClass="nds32le-elf-mculib-v3m.c.compiler.exe.debug.option.optimization.level" value="nds.c.optimization.level.more" valueType="enumerated"/>
								<option id="nds.c.compiler.option.optimization.removesections.function.1407496446" name="Remove unused function sections (-ffunction-sections)" superClass="nds.c.compiler.option.optimization.removesections.function" value="true" valueType="boolean"/>
								<option id="nds.c.compiler.option.optimization.removesections.data.483635039" name="Remove unused data sections (-fdata-sections)" superClass="nds.c.compiler.option.optimization.removesections.data" value="true" valueType="boolean"/>
								<option id="nds.c.compiler.option.cmodel.910013093" name="Code Model" superClass="nds.c.compiler.option.cmodel" value="nds.c.compiler.option.cmodel.medium" valueType="enumerated"/>
								<option id="nds.c.compiler.option.preprocessor.def.symbols.207532049" name="Defined symbols (-D)" superClass="nds.c.compiler.option.preprocessor.def.symbols" valueType="definedSymbols">
									<listOptionValue builtIn="false" value="CATEGORY_CUSTOMER=CUSTOMER_YMTC"/>
									<listOptionValue builtIn="false" value="CATEGORY_FLASH=FLASH_YMTC_WTS_TLC"/>
									<listOptionValue builtIn="false" value="CATEGORY_CONTROLLER=CONTROLLER_PS5017"/>
								</option>
								<inputType id="tool.nds.c.compiler.input.986503196" superClass="tool.nds.c.compiler.input"/>
							</tool>
							<tool command="$(CROSS_COMPILE)gcc" commandLinePattern="${COMMAND} ${FLAGS} ${OUTPUT_FLAG} ${OUTPUT_PREFIX}${OUTPUT} ${INPUTS}" errorParsers="org.eclipse.cdt.core.GLDErrorParser" id="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug.1408036823" name="Andes C Linker" superClass="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug">
								<option defaultValue="-O0" id="nds.c.link.option.optimization.level.1379153296" name="Optimization Level" superClass="nds.c.link.option.optimization.level" value="-O2" valueType="string"/>
								<option defaultValue="true" id="nds32le-elf-mculib-v3m.c.link.option.noshared.base.1017372386" name="No shared libraries (-static)" superClass="nds32le-elf-mculib-v3m.c.link.option.noshared.base" valueType="boolean"/>
								<option defaultValue="" id="nds32le-elf-mculib-v3m.c.link.exe.debug.option.ldscript.1414278202" name="Linker Script (-T)" superClass="nds32le-elf-mculib-v3m.c.link.exe.debug.option.ldscript" value="..\LinkerScript\ps5008_hw.ld" valueType="string"/>
								<option id="nds.c.linker.option.cmodel.1992613764" name="Code Model" superClass="nds.c.linker.option.cmodel" value="-mcmodel=medium" valueType="string"/>
								<option id="nds.c.link.option.map.9350614" name="Write a map file. (-Map)" superClass="nds.c.link.option.map" value="true" valueType="boolean"/>
								<option id="nds.c.link.option.virtualhosting.714841166" name="Virtual Hosting (-mvh)" superClass="nds.c.link.option.virtualhosting" value="false" valueType="boolean"/>
								<option id="nds.c.link.option.nostart.822498410" name="Do not use standard start files (-nostartfiles)" superClass="nds.c.link.option.nostart" value="true" valueType="boolean"/>
								<option id="nds.c.link.option.removesections.1927428674" name="Remove unused sections (-ffunction-sections or -fdata-sections)" superClass="nds.c.link.option.removesections" value="true" valueType="boolean"/>
								<inputType id="tool.nds.c.linker.input.1005515490" superClass="tool.nds.c.linker.input">
									<additionalInput kind="additionalinputdependency" paths="$(USER_OBJS)"/>
									<additionalInput kind="additionalinput" paths="$(LIBS)"/>
								</inputType>
							</tool>
							<tool command="$(CROSS_COMPILE)as" commandLinePattern="${COMMAND} ${FLAGS} ${OUTPUT_FLAG} ${OUTPUT_PREFIX}${OUTPUT} ${INPUTS}" errorParsers="org.eclipse.cdt.core.GASErrorParser" id="tool.nds32le-elf-mculib-v3m.assembler.exe.debug.1659373445" name="Andes Assembler" superClass="tool.nds32le-elf-mculib-v3m.assembler.exe.debug">
								<option defaultValue="-g" id="nds.both.asm.option.flags.2112143249" name="Assembler flags" superClass="nds.both.asm.option.flags" valueType="string"/>
								<inputType id="tool.nds.assembler.input.1995720422" superClass="tool.nds.assembler.input"/>
							</tool>
							<tool command="$(CROSS_COMPILE)nm" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot; ${OUTPUT_FLAG} ${OUTPUT}" errorParsers="" id="tool.nds32le-elf-mculib-v3m.nm.exe.debug.784694501" name="NM (symbol listing)" superClass="tool.nds32le-elf-mculib-v3m.nm.exe.debug">
								<option id="nds.nm.option.sortsymbol.701875186" name="Sort symbols numerically by address. (-n)" superClass="nds.nm.option.sortsymbol" value="true" valueType="boolean"/>
								<option id="nds.nm.option.printfilename.1805507189" name="Print name of the input file before every symbol. (-A)" superClass="nds.nm.option.printfilename" value="false" valueType="boolean"/>
							</tool>
							<tool command="$(CROSS_COMPILE)readelf" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot; ${OUTPUT_FLAG} ${OUTPUT}" errorParsers="" id="tool.nds32le-elf-mculib-v3m.readelf.exe.debug.1914907117" name="Readelf (ELF info listing)" superClass="tool.nds32le-elf-mculib-v3m.readelf.exe.debug"/>
							<tool command="$(CROSS_COMPILE)objdump" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot; ${OUTPUT_FLAG} ${OUTPUT}" errorParsers="" id="tool.nds32le-elf-mculib-v3m.objdump.exe.debug.4394937" name="Objdump (disassembly)" superClass="tool.nds32le-elf-mculib-v3m.objdump.exe.debug">
								<option id="nds.objdump.option.disable.962613333" name="Disable. (Do not auto-generate output file.)" superClass="nds.objdump.option.disable" value="false" valueType="boolean"/>
								<option id="nds.objdump.option.linenumber.693013460" name="Include line numbers and filenames in output. (-l)" superClass="nds.objdump.option.linenumber" value="true" valueType="boolean"/>
								<option id="nds.objdump.option.otherflags.1026554609" name="Other flags" superClass="nds.objdump.option.otherflags" value="-S" valueType="string"/>
							</tool>
							<tool command="$(CROSS_COMPILE)objcopy" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot; ${OUTPUT}" errorParsers="" id="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug.1465050514" name="Objcopy (object content copy)" superClass="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug">
								<option id="nds.objcopy.option.disable.1029977197" name="Disable. (Do not auto-generate output file.)" superClass="nds.objcopy.option.disable" value="false" valueType="boolean"/>
								<option id="nds.objcopy.option.stripall.1965332730" name="Remove all symbol and relocation information. (-S)" superClass="nds.objcopy.option.stripall" value="false" valueType="boolean"/>
								<option id="nds.objcopy.option.otherflags.1741084988" name="Other flags" superClass="nds.objcopy.option.otherflags" value="-R .flh_iram_fpu.data" valueType="string"/>
							</tool>
							<tool command="$(CROSS_COMPILE)size" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot;" errorParsers="" id="tool.nds32le-elf-mculib-v3m.size.exe.debug.1155482448" name="Size (section size listing)" superClass="tool.nds32le-elf-mculib-v3m.size.exe.debug"/>
							<tool command="$(ANDESIGHT_ROOT)/utils/nds_ldsag" commandLinePattern="${COMMAND} ${FLAGS} ${INPUTS} -o $(LDSAG_OUT)" errorParsers="" id="tool.nds32le-elf-mculib-v3m.ldsag.base.344124832" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.base"/>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.361714770" name="Andes C++ Compiler" superClass="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug">
								<option id="nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.option.debugging.level.491573751" name="Debug Level" superClass="nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.option.debugging.level" value="nds.cpp.compiler.debugging.level.max" valueType="enumerated"/>
							</tool>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug.486666073" name="Andes C++ Linker" superClass="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug">
								<option defaultValue="-O0" id="nds.cpp.link.option.optimization.level.1837677071" name="Optimization Level" superClass="nds.cpp.link.option.optimization.level" valueType="string"/>
								<option defaultValue="true" id="nds32le-elf-mculib-v3m.cpp.link.option.noshared.base.1316230532" name="No shared libraries (-static)" superClass="nds32le-elf-mculib-v3m.cpp.link.option.noshared.base" valueType="boolean"/>
							</tool>
							<tool command="$(ANDESIGHT_ROOT)/utils/nds_ldsag" commandLinePattern="${COMMAND} ${FLAGS} ${INPUTS} -o $(LDSAG_OUT)" errorParsers="" id="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug.1117116829" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug"/>
						</toolChain>
					</folderInfo>
					<folderInfo id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********.112201386.opt_master_only" name="/" resourcePath="opt_master_only">
						<toolChain errorParsers="" id="nds.nds32le-elf-mculib-v3m.exe.debug.1389848890" name="nds32le-elf-mculib-v3m" superClass="nds.nds32le-elf-mculib-v3m.exe.debug" unusedChildren="">
							<option id="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET.423936908.313979601.1887125436.1009016960.1709021572.182459041.2129931864" name="RSE_TARGET" superClass="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET.423936908"/>
							<targetPlatform id="target.nds32le-elf-mculib-v3m.platform.exe.debug.1424838050" name="Debug Platform" superClass="target.nds32le-elf-mculib-v3m.platform.exe.debug"/>
							<tool id="tool.nds32le-elf-mculib-v3m.archiver.base.901128156" name="Andes Archiver" superClass="tool.nds32le-elf-mculib-v3m.archiver.base.1450153053"/>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.302520490" name="Andes C++ Compiler" superClass="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.361714770">
								<option id="nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.option.debugging.level.2115728072" name="Debug Level" superClass="nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.option.debugging.level" value="nds.cpp.compiler.debugging.level.max" valueType="enumerated"/>
							</tool>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug.1991465145" name="Andes C++ Linker" superClass="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug.486666073">
								<option defaultValue="-O0" id="nds.cpp.link.option.optimization.level.1289554156" name="Optimization Level" superClass="nds.cpp.link.option.optimization.level" valueType="string"/>
								<option defaultValue="true" id="nds32le-elf-mculib-v3m.cpp.link.option.noshared.base.1527328610" name="No shared libraries (-static)" superClass="nds32le-elf-mculib-v3m.cpp.link.option.noshared.base" valueType="boolean"/>
							</tool>
							<tool errorParsers="org.eclipse.cdt.core.GCCErrorParser" id="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.1033577195" name="Andes C Compiler" superClass="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.2036747734">
								<inputType id="tool.nds.c.compiler.input.1553364048" superClass="tool.nds.c.compiler.input"/>
							</tool>
							<tool id="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug.1340524658" name="Andes C Linker" superClass="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug.1408036823"/>
							<tool errorParsers="org.eclipse.cdt.core.GASErrorParser" id="tool.nds32le-elf-mculib-v3m.assembler.exe.debug.2105949846" name="Andes Assembler" superClass="tool.nds32le-elf-mculib-v3m.assembler.exe.debug.1659373445">
								<inputType id="tool.nds.assembler.input.2080833828" superClass="tool.nds.assembler.input"/>
							</tool>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.nm.exe.debug.931930002" name="NM (symbol listing)" superClass="tool.nds32le-elf-mculib-v3m.nm.exe.debug.784694501"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.readelf.exe.debug.863167876" name="Readelf (ELF info listing)" superClass="tool.nds32le-elf-mculib-v3m.readelf.exe.debug.1914907117"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.objdump.exe.debug.56507411" name="Objdump (disassembly)" superClass="tool.nds32le-elf-mculib-v3m.objdump.exe.debug.4394937"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug.1903497230" name="Objcopy (object content copy)" superClass="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug.1465050514"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.size.exe.debug.569710652" name="Size (section size listing)" superClass="tool.nds32le-elf-mculib-v3m.size.exe.debug.1155482448"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug.1381283659" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug.1117116829"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.ldsag.base.1074698894" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.base.344124832"/>
						</toolChain>
					</folderInfo>
					<folderInfo id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********.112201386.opt" name="/" resourcePath="opt">
						<toolChain id="nds.nds32le-elf-mculib-v3m.exe.debug.208597201" name="nds32le-elf-mculib-v3m" superClass="nds.nds32le-elf-mculib-v3m.exe.debug" unusedChildren="">
							<option id="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET.1599934830.46803478.1309616118" name="RSE_TARGET" superClass="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET.1599934830"/>
							<targetPlatform id="target.nds32le-elf-mculib-v3m.platform.exe.debug.785213140" name="Debug Platform" superClass="target.nds32le-elf-mculib-v3m.platform.exe.debug"/>
							<tool id="tool.nds32le-elf-mculib-v3m.archiver.base.1090181531" name="Andes Archiver" superClass="tool.nds32le-elf-mculib-v3m.archiver.base.1450153053"/>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.1911922204" name="Andes C++ Compiler" superClass="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.361714770"/>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug.834820441" name="Andes C++ Linker" superClass="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug.486666073"/>
							<tool id="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.1164557109" name="Andes C Compiler" superClass="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.2036747734">
								<inputType id="tool.nds.c.compiler.input.1275877617" superClass="tool.nds.c.compiler.input"/>
							</tool>
							<tool id="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug.70660450" name="Andes C Linker" superClass="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug.1408036823"/>
							<tool id="tool.nds32le-elf-mculib-v3m.assembler.exe.debug.121823799" name="Andes Assembler" superClass="tool.nds32le-elf-mculib-v3m.assembler.exe.debug.1659373445">
								<inputType id="tool.nds.assembler.input.820758648" superClass="tool.nds.assembler.input"/>
							</tool>
							<tool id="tool.nds32le-elf-mculib-v3m.nm.exe.debug.829668978" name="NM (symbol listing)" superClass="tool.nds32le-elf-mculib-v3m.nm.exe.debug.784694501"/>
							<tool id="tool.nds32le-elf-mculib-v3m.readelf.exe.debug.2139056424" name="Readelf (ELF info listing)" superClass="tool.nds32le-elf-mculib-v3m.readelf.exe.debug.1914907117"/>
							<tool id="tool.nds32le-elf-mculib-v3m.objdump.exe.debug.1729312785" name="Objdump (disassembly)" superClass="tool.nds32le-elf-mculib-v3m.objdump.exe.debug.4394937"/>
							<tool id="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug.54887759" name="Objcopy (object content copy)" superClass="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug.1465050514"/>
							<tool id="tool.nds32le-elf-mculib-v3m.size.exe.debug.438687359" name="Size (section size listing)" superClass="tool.nds32le-elf-mculib-v3m.size.exe.debug.1155482448"/>
							<tool id="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug.507620560" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug.1117116829"/>
							<tool id="tool.nds32le-elf-mculib-v3m.ldsag.base.1236872556" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.base.344124832"/>
						</toolChain>
					</folderInfo>
					<sourceEntries>
						<entry excluding="opt_master_only|Src_KIC|shr/inc/shr_types.h" flags="VALUE_WORKSPACE_PATH" kind="sourcePath" name=""/>
					</sourceEntries>
				</configuration>
			</storageModule>
			<storageModule moduleId="org.eclipse.cdt.core.externalSettings"/>
		</cconfiguration>
		<cconfiguration id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.576423965">
			<storageModule buildSystemId="org.eclipse.cdt.managedbuilder.core.configurationDataProvider" id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.576423965" moduleId="org.eclipse.cdt.core.settings" name="PS5017_Samsung_V6">
				<externalSettings/>
				<extensions>
					<extension id="com.andestech.ide.cdt.managedbuilder.core.CROSS_GNU_ELF" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="org.eclipse.cdt.core.GmakeErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.CWDLocator" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GCCErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GASErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GLDErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
				</extensions>
			</storageModule>
			<storageModule moduleId="cdtBuildSystem" version="4.0.0">
				<configuration artifactExtension="adx" artifactName="${ProjName}" buildArtefactType="nds.buildArtefactType.exe" buildProperties="org.eclipse.cdt.build.core.buildType=org.eclipse.cdt.build.core.buildType.debug,org.eclipse.cdt.build.core.buildArtefactType=nds.buildArtefactType.exe" description="" errorParsers="org.eclipse.cdt.core.GmakeErrorParser;org.eclipse.cdt.core.CWDLocator;org.eclipse.cdt.core.GCCErrorParser;org.eclipse.cdt.core.GASErrorParser;org.eclipse.cdt.core.GLDErrorParser" id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.576423965" name="PS5017_Samsung_V6" parent="config.nds32le-elf-mculib-v3m.exe.debug" postannouncebuildStep="" postbuildStep="" preannouncebuildStep="" prebuildStep="">
					<folderInfo id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.576423965." name="/" resourcePath="">
						<toolChain errorParsers="" id="nds.nds32le-elf-mculib-v3m.exe.debug.974627332" name="nds32le-elf-mculib-v3m" superClass="nds.nds32le-elf-mculib-v3m.exe.debug">
							<option id="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET.159895187" name="RSE_TARGET" superClass="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET" value="rse://LOCALHOST/~toolchains/nds32le-elf-mculib-v3m" valueType="string"/>
							<targetPlatform binaryParser="com.andestech.ide.cdt.managedbuilder.core.CROSS_GNU_ELF" id="target.nds32le-elf-mculib-v3m.platform.exe.debug.928385317" name="Debug Platform" superClass="target.nds32le-elf-mculib-v3m.platform.exe.debug"/>
							<builder buildPath="${workspace_loc:/ps5008_opt/Debug}" errorParsers="org.eclipse.cdt.core.GmakeErrorParser;org.eclipse.cdt.core.CWDLocator" id="target.nds32le-elf-mculib-v3m.builder.exe.debug.884108860" keepEnvironmentInBuildfile="false" managedBuildOn="true" name="Andes Make Builder" parallelBuildOn="false" parallelizationNumber="-1" superClass="target.nds32le-elf-mculib-v3m.builder.exe.debug"/>
							<tool id="tool.nds32le-elf-mculib-v3m.archiver.base.993618449" name="Andes Archiver" superClass="tool.nds32le-elf-mculib-v3m.archiver.base"/>
							<tool command="$(CROSS_COMPILE)gcc" commandLinePattern="${COMMAND} ${FLAGS} ${OUTPUT_FLAG} ${OUTPUT_PREFIX}${OUTPUT} ${INPUTS}" errorParsers="org.eclipse.cdt.core.GCCErrorParser" id="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.323350479" name="Andes C Compiler" superClass="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug">
								<option id="nds32le-elf-mculib-v3m.c.compiler.exe.debug.option.debugging.level.235475048" name="Debug Level" superClass="nds32le-elf-mculib-v3m.c.compiler.exe.debug.option.debugging.level" value="nds.c.debugging.level.max" valueType="enumerated"/>
								<option id="nds.c.compiler.option.include.paths.1277143457" name="Include paths (-I)" superClass="nds.c.compiler.option.include.paths" valueType="includePath">
									<listOptionValue builtIn="false" value="&quot;..\opt\inc&quot;"/>
									<listOptionValue builtIn="false" value="&quot;..\opt&quot;"/>
									<listOptionValue builtIn="false" value="&quot;..\shr\inc&quot;"/>
									<listOptionValue builtIn="false" value="&quot;..\startup\inc&quot;"/>
								</option>
								<option id="nds32le-elf-mculib-v3m.c.compiler.exe.debug.option.optimization.level.74429644" name="Optimization Level" superClass="nds32le-elf-mculib-v3m.c.compiler.exe.debug.option.optimization.level" value="nds.c.optimization.level.more" valueType="enumerated"/>
								<option id="nds.c.compiler.option.optimization.removesections.function.401335945" name="Remove unused function sections (-ffunction-sections)" superClass="nds.c.compiler.option.optimization.removesections.function" value="true" valueType="boolean"/>
								<option id="nds.c.compiler.option.optimization.removesections.data.767561215" name="Remove unused data sections (-fdata-sections)" superClass="nds.c.compiler.option.optimization.removesections.data" value="true" valueType="boolean"/>
								<option id="nds.c.compiler.option.cmodel.456830248" name="Code Model" superClass="nds.c.compiler.option.cmodel" value="nds.c.compiler.option.cmodel.medium" valueType="enumerated"/>
								<option id="nds.c.compiler.option.preprocessor.def.symbols.2102768559" name="Defined symbols (-D)" superClass="nds.c.compiler.option.preprocessor.def.symbols" valueType="definedSymbols">
									<listOptionValue builtIn="false" value="CATEGORY_CUSTOMER=CUSTOMER_SAMSUNG"/>
									<listOptionValue builtIn="false" value="CATEGORY_FLASH=FLASH_SAMSUNG_V6_TLC"/>
									<listOptionValue builtIn="false" value="CATEGORY_CONTROLLER=CONTROLLER_PS5017"/>
								</option>
								<inputType id="tool.nds.c.compiler.input.1940122962" superClass="tool.nds.c.compiler.input"/>
							</tool>
							<tool command="$(CROSS_COMPILE)gcc" commandLinePattern="${COMMAND} ${FLAGS} ${OUTPUT_FLAG} ${OUTPUT_PREFIX}${OUTPUT} ${INPUTS}" errorParsers="org.eclipse.cdt.core.GLDErrorParser" id="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug.23386589" name="Andes C Linker" superClass="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug">
								<option defaultValue="-O0" id="nds.c.link.option.optimization.level.244725140" name="Optimization Level" superClass="nds.c.link.option.optimization.level" value="-O2" valueType="string"/>
								<option defaultValue="true" id="nds32le-elf-mculib-v3m.c.link.option.noshared.base.403409796" name="No shared libraries (-static)" superClass="nds32le-elf-mculib-v3m.c.link.option.noshared.base" valueType="boolean"/>
								<option defaultValue="" id="nds32le-elf-mculib-v3m.c.link.exe.debug.option.ldscript.931636275" name="Linker Script (-T)" superClass="nds32le-elf-mculib-v3m.c.link.exe.debug.option.ldscript" value="..\LinkerScript\ps5008_hw.ld" valueType="string"/>
								<option id="nds.c.linker.option.cmodel.1976130937" name="Code Model" superClass="nds.c.linker.option.cmodel" value="-mcmodel=medium" valueType="string"/>
								<option id="nds.c.link.option.map.719043899" name="Write a map file. (-Map)" superClass="nds.c.link.option.map" value="true" valueType="boolean"/>
								<option id="nds.c.link.option.virtualhosting.56142753" name="Virtual Hosting (-mvh)" superClass="nds.c.link.option.virtualhosting" value="false" valueType="boolean"/>
								<option id="nds.c.link.option.nostart.601623731" name="Do not use standard start files (-nostartfiles)" superClass="nds.c.link.option.nostart" value="true" valueType="boolean"/>
								<option id="nds.c.link.option.removesections.1886820791" name="Remove unused sections (-ffunction-sections or -fdata-sections)" superClass="nds.c.link.option.removesections" value="true" valueType="boolean"/>
								<inputType id="tool.nds.c.linker.input.660032195" superClass="tool.nds.c.linker.input">
									<additionalInput kind="additionalinputdependency" paths="$(USER_OBJS)"/>
									<additionalInput kind="additionalinput" paths="$(LIBS)"/>
								</inputType>
							</tool>
							<tool command="$(CROSS_COMPILE)as" commandLinePattern="${COMMAND} ${FLAGS} ${OUTPUT_FLAG} ${OUTPUT_PREFIX}${OUTPUT} ${INPUTS}" errorParsers="org.eclipse.cdt.core.GASErrorParser" id="tool.nds32le-elf-mculib-v3m.assembler.exe.debug.1654882248" name="Andes Assembler" superClass="tool.nds32le-elf-mculib-v3m.assembler.exe.debug">
								<option defaultValue="-g" id="nds.both.asm.option.flags.1580892559" name="Assembler flags" superClass="nds.both.asm.option.flags" valueType="string"/>
								<inputType id="tool.nds.assembler.input.1019221518" superClass="tool.nds.assembler.input"/>
							</tool>
							<tool command="$(CROSS_COMPILE)nm" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot; ${OUTPUT_FLAG} ${OUTPUT}" errorParsers="" id="tool.nds32le-elf-mculib-v3m.nm.exe.debug.650092933" name="NM (symbol listing)" superClass="tool.nds32le-elf-mculib-v3m.nm.exe.debug">
								<option id="nds.nm.option.sortsymbol.165248754" name="Sort symbols numerically by address. (-n)" superClass="nds.nm.option.sortsymbol" value="true" valueType="boolean"/>
								<option id="nds.nm.option.printfilename.1580178418" name="Print name of the input file before every symbol. (-A)" superClass="nds.nm.option.printfilename" value="false" valueType="boolean"/>
							</tool>
							<tool command="$(CROSS_COMPILE)readelf" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot; ${OUTPUT_FLAG} ${OUTPUT}" errorParsers="" id="tool.nds32le-elf-mculib-v3m.readelf.exe.debug.684754852" name="Readelf (ELF info listing)" superClass="tool.nds32le-elf-mculib-v3m.readelf.exe.debug"/>
							<tool command="$(CROSS_COMPILE)objdump" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot; ${OUTPUT_FLAG} ${OUTPUT}" errorParsers="" id="tool.nds32le-elf-mculib-v3m.objdump.exe.debug.197444841" name="Objdump (disassembly)" superClass="tool.nds32le-elf-mculib-v3m.objdump.exe.debug">
								<option id="nds.objdump.option.disable.913556250" name="Disable. (Do not auto-generate output file.)" superClass="nds.objdump.option.disable" value="false" valueType="boolean"/>
								<option id="nds.objdump.option.linenumber.485770746" name="Include line numbers and filenames in output. (-l)" superClass="nds.objdump.option.linenumber" value="true" valueType="boolean"/>
								<option id="nds.objdump.option.otherflags.532831360" name="Other flags" superClass="nds.objdump.option.otherflags" value="-S" valueType="string"/>
							</tool>
							<tool command="$(CROSS_COMPILE)objcopy" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot; ${OUTPUT}" errorParsers="" id="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug.351974050" name="Objcopy (object content copy)" superClass="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug">
								<option id="nds.objcopy.option.disable.1047722894" name="Disable. (Do not auto-generate output file.)" superClass="nds.objcopy.option.disable" value="false" valueType="boolean"/>
								<option id="nds.objcopy.option.stripall.2034692468" name="Remove all symbol and relocation information. (-S)" superClass="nds.objcopy.option.stripall" value="false" valueType="boolean"/>
								<option id="nds.objcopy.option.otherflags.885155594" name="Other flags" superClass="nds.objcopy.option.otherflags" value="-R .flh_iram_fpu.data" valueType="string"/>
							</tool>
							<tool command="$(CROSS_COMPILE)size" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot;" errorParsers="" id="tool.nds32le-elf-mculib-v3m.size.exe.debug.724990267" name="Size (section size listing)" superClass="tool.nds32le-elf-mculib-v3m.size.exe.debug"/>
							<tool command="$(ANDESIGHT_ROOT)/utils/nds_ldsag" commandLinePattern="${COMMAND} ${FLAGS} ${INPUTS} -o $(LDSAG_OUT)" errorParsers="" id="tool.nds32le-elf-mculib-v3m.ldsag.base.420312841" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.base"/>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.103090454" name="Andes C++ Compiler" superClass="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug">
								<option id="nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.option.debugging.level.176138117" name="Debug Level" superClass="nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.option.debugging.level" value="nds.cpp.compiler.debugging.level.max" valueType="enumerated"/>
							</tool>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug.1609011471" name="Andes C++ Linker" superClass="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug">
								<option defaultValue="-O0" id="nds.cpp.link.option.optimization.level.164560411" name="Optimization Level" superClass="nds.cpp.link.option.optimization.level" valueType="string"/>
								<option defaultValue="true" id="nds32le-elf-mculib-v3m.cpp.link.option.noshared.base.1001414880" name="No shared libraries (-static)" superClass="nds32le-elf-mculib-v3m.cpp.link.option.noshared.base" valueType="boolean"/>
							</tool>
							<tool command="$(ANDESIGHT_ROOT)/utils/nds_ldsag" commandLinePattern="${COMMAND} ${FLAGS} ${INPUTS} -o $(LDSAG_OUT)" errorParsers="" id="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug.1110083780" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug"/>
						</toolChain>
					</folderInfo>
					<folderInfo id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.576423965.opt_master_only" name="/" resourcePath="opt_master_only">
						<toolChain errorParsers="" id="nds.nds32le-elf-mculib-v3m.exe.debug.1664509732" name="nds32le-elf-mculib-v3m" superClass="nds.nds32le-elf-mculib-v3m.exe.debug" unusedChildren="">
							<option id="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET.423936908.313979601.1887125436.1009016960.1709021572.205845821" name="RSE_TARGET" superClass="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET.423936908"/>
							<targetPlatform id="target.nds32le-elf-mculib-v3m.platform.exe.debug.1711712089" name="Debug Platform" superClass="target.nds32le-elf-mculib-v3m.platform.exe.debug"/>
							<tool id="tool.nds32le-elf-mculib-v3m.archiver.base.1815976914" name="Andes Archiver" superClass="tool.nds32le-elf-mculib-v3m.archiver.base.993618449"/>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.552595462" name="Andes C++ Compiler" superClass="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.103090454">
								<option id="nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.option.debugging.level.573320017" name="Debug Level" superClass="nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.option.debugging.level" value="nds.cpp.compiler.debugging.level.max" valueType="enumerated"/>
							</tool>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug.2017386592" name="Andes C++ Linker" superClass="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug.1609011471">
								<option defaultValue="-O0" id="nds.cpp.link.option.optimization.level.590358096" name="Optimization Level" superClass="nds.cpp.link.option.optimization.level" valueType="string"/>
								<option defaultValue="true" id="nds32le-elf-mculib-v3m.cpp.link.option.noshared.base.985693750" name="No shared libraries (-static)" superClass="nds32le-elf-mculib-v3m.cpp.link.option.noshared.base" valueType="boolean"/>
							</tool>
							<tool errorParsers="org.eclipse.cdt.core.GCCErrorParser" id="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.2146547980" name="Andes C Compiler" superClass="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.323350479">
								<inputType id="tool.nds.c.compiler.input.1648108612" superClass="tool.nds.c.compiler.input"/>
							</tool>
							<tool id="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug.2088394740" name="Andes C Linker" superClass="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug.23386589"/>
							<tool errorParsers="org.eclipse.cdt.core.GASErrorParser" id="tool.nds32le-elf-mculib-v3m.assembler.exe.debug.138699094" name="Andes Assembler" superClass="tool.nds32le-elf-mculib-v3m.assembler.exe.debug.1654882248">
								<inputType id="tool.nds.assembler.input.1479902116" superClass="tool.nds.assembler.input"/>
							</tool>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.nm.exe.debug.1208075933" name="NM (symbol listing)" superClass="tool.nds32le-elf-mculib-v3m.nm.exe.debug.650092933"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.readelf.exe.debug.1154923371" name="Readelf (ELF info listing)" superClass="tool.nds32le-elf-mculib-v3m.readelf.exe.debug.684754852"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.objdump.exe.debug.848981519" name="Objdump (disassembly)" superClass="tool.nds32le-elf-mculib-v3m.objdump.exe.debug.197444841"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug.283378560" name="Objcopy (object content copy)" superClass="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug.351974050"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.size.exe.debug.446738731" name="Size (section size listing)" superClass="tool.nds32le-elf-mculib-v3m.size.exe.debug.724990267"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug.1453696484" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug.1110083780"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.ldsag.base.1061380044" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.base.420312841"/>
						</toolChain>
					</folderInfo>
					<sourceEntries>
						<entry excluding="opt_master_only|Src_KIC|shr/inc/shr_types.h" flags="VALUE_WORKSPACE_PATH" kind="sourcePath" name=""/>
					</sourceEntries>
				</configuration>
			</storageModule>
			<storageModule moduleId="org.eclipse.cdt.core.externalSettings"/>
		</cconfiguration>
		<cconfiguration id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.576423965.515984178">
			<storageModule buildSystemId="org.eclipse.cdt.managedbuilder.core.configurationDataProvider" id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.576423965.515984178" moduleId="org.eclipse.cdt.core.settings" name="PS5017_Samsung_V6P">
				<externalSettings/>
				<extensions>
					<extension id="com.andestech.ide.cdt.managedbuilder.core.CROSS_GNU_ELF" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="org.eclipse.cdt.core.GmakeErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.CWDLocator" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GCCErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GASErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GLDErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
				</extensions>
			</storageModule>
			<storageModule moduleId="cdtBuildSystem" version="4.0.0">
				<configuration artifactExtension="adx" artifactName="${ProjName}" buildArtefactType="nds.buildArtefactType.exe" buildProperties="org.eclipse.cdt.build.core.buildType=org.eclipse.cdt.build.core.buildType.debug,org.eclipse.cdt.build.core.buildArtefactType=nds.buildArtefactType.exe" description="" errorParsers="org.eclipse.cdt.core.GmakeErrorParser;org.eclipse.cdt.core.CWDLocator;org.eclipse.cdt.core.GCCErrorParser;org.eclipse.cdt.core.GASErrorParser;org.eclipse.cdt.core.GLDErrorParser" id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.576423965.515984178" name="PS5017_Samsung_V6P" parent="config.nds32le-elf-mculib-v3m.exe.debug" postannouncebuildStep="" postbuildStep="" preannouncebuildStep="" prebuildStep="">
					<folderInfo id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.576423965.515984178." name="/" resourcePath="">
						<toolChain errorParsers="" id="nds.nds32le-elf-mculib-v3m.exe.debug.2016772054" name="nds32le-elf-mculib-v3m" superClass="nds.nds32le-elf-mculib-v3m.exe.debug">
							<option id="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET.589779346" name="RSE_TARGET" superClass="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET" value="rse://LOCALHOST/~toolchains/nds32le-elf-mculib-v3m" valueType="string"/>
							<targetPlatform binaryParser="com.andestech.ide.cdt.managedbuilder.core.CROSS_GNU_ELF" id="target.nds32le-elf-mculib-v3m.platform.exe.debug.791822349" name="Debug Platform" superClass="target.nds32le-elf-mculib-v3m.platform.exe.debug"/>
							<builder buildPath="${workspace_loc:/ps5008_opt/Debug}" errorParsers="org.eclipse.cdt.core.GmakeErrorParser;org.eclipse.cdt.core.CWDLocator" id="target.nds32le-elf-mculib-v3m.builder.exe.debug.2000327979" keepEnvironmentInBuildfile="false" managedBuildOn="true" name="Andes Make Builder" parallelBuildOn="false" parallelizationNumber="-1" superClass="target.nds32le-elf-mculib-v3m.builder.exe.debug"/>
							<tool id="tool.nds32le-elf-mculib-v3m.archiver.base.1711288442" name="Andes Archiver" superClass="tool.nds32le-elf-mculib-v3m.archiver.base"/>
							<tool command="$(CROSS_COMPILE)gcc" commandLinePattern="${COMMAND} ${FLAGS} ${OUTPUT_FLAG} ${OUTPUT_PREFIX}${OUTPUT} ${INPUTS}" errorParsers="org.eclipse.cdt.core.GCCErrorParser" id="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.1914355024" name="Andes C Compiler" superClass="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug">
								<option id="nds32le-elf-mculib-v3m.c.compiler.exe.debug.option.debugging.level.1778565848" name="Debug Level" superClass="nds32le-elf-mculib-v3m.c.compiler.exe.debug.option.debugging.level" value="nds.c.debugging.level.max" valueType="enumerated"/>
								<option id="nds.c.compiler.option.include.paths.276365792" name="Include paths (-I)" superClass="nds.c.compiler.option.include.paths" valueType="includePath">
									<listOptionValue builtIn="false" value="&quot;..\opt\inc&quot;"/>
									<listOptionValue builtIn="false" value="&quot;..\opt&quot;"/>
									<listOptionValue builtIn="false" value="&quot;..\shr\inc&quot;"/>
									<listOptionValue builtIn="false" value="&quot;..\startup\inc&quot;"/>
								</option>
								<option id="nds32le-elf-mculib-v3m.c.compiler.exe.debug.option.optimization.level.95639445" name="Optimization Level" superClass="nds32le-elf-mculib-v3m.c.compiler.exe.debug.option.optimization.level" value="nds.c.optimization.level.more" valueType="enumerated"/>
								<option id="nds.c.compiler.option.optimization.removesections.function.547892649" name="Remove unused function sections (-ffunction-sections)" superClass="nds.c.compiler.option.optimization.removesections.function" value="true" valueType="boolean"/>
								<option id="nds.c.compiler.option.optimization.removesections.data.751296628" name="Remove unused data sections (-fdata-sections)" superClass="nds.c.compiler.option.optimization.removesections.data" value="true" valueType="boolean"/>
								<option id="nds.c.compiler.option.cmodel.2031871659" name="Code Model" superClass="nds.c.compiler.option.cmodel" value="nds.c.compiler.option.cmodel.medium" valueType="enumerated"/>
								<option id="nds.c.compiler.option.preprocessor.def.symbols.1558261896" name="Defined symbols (-D)" superClass="nds.c.compiler.option.preprocessor.def.symbols" valueType="definedSymbols">
									<listOptionValue builtIn="false" value="CATEGORY_CUSTOMER=CUSTOMER_SAMSUNG"/>
									<listOptionValue builtIn="false" value="CATEGORY_FLASH=FLASH_SAMSUNG_V6P_TLC"/>
									<listOptionValue builtIn="false" value="CATEGORY_CONTROLLER=CONTROLLER_PS5017"/>
								</option>
								<inputType id="tool.nds.c.compiler.input.1566337483" superClass="tool.nds.c.compiler.input"/>
							</tool>
							<tool command="$(CROSS_COMPILE)gcc" commandLinePattern="${COMMAND} ${FLAGS} ${OUTPUT_FLAG} ${OUTPUT_PREFIX}${OUTPUT} ${INPUTS}" errorParsers="org.eclipse.cdt.core.GLDErrorParser" id="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug.1368235164" name="Andes C Linker" superClass="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug">
								<option defaultValue="-O0" id="nds.c.link.option.optimization.level.1460847574" name="Optimization Level" superClass="nds.c.link.option.optimization.level" value="-O2" valueType="string"/>
								<option defaultValue="true" id="nds32le-elf-mculib-v3m.c.link.option.noshared.base.464325006" name="No shared libraries (-static)" superClass="nds32le-elf-mculib-v3m.c.link.option.noshared.base" valueType="boolean"/>
								<option defaultValue="" id="nds32le-elf-mculib-v3m.c.link.exe.debug.option.ldscript.1883262888" name="Linker Script (-T)" superClass="nds32le-elf-mculib-v3m.c.link.exe.debug.option.ldscript" value="..\LinkerScript\ps5008_hw.ld" valueType="string"/>
								<option id="nds.c.linker.option.cmodel.2121062597" name="Code Model" superClass="nds.c.linker.option.cmodel" value="-mcmodel=medium" valueType="string"/>
								<option id="nds.c.link.option.map.20939417" name="Write a map file. (-Map)" superClass="nds.c.link.option.map" value="true" valueType="boolean"/>
								<option id="nds.c.link.option.virtualhosting.1399561254" name="Virtual Hosting (-mvh)" superClass="nds.c.link.option.virtualhosting" value="false" valueType="boolean"/>
								<option id="nds.c.link.option.nostart.1424653812" name="Do not use standard start files (-nostartfiles)" superClass="nds.c.link.option.nostart" value="true" valueType="boolean"/>
								<option id="nds.c.link.option.removesections.707459404" name="Remove unused sections (-ffunction-sections or -fdata-sections)" superClass="nds.c.link.option.removesections" value="true" valueType="boolean"/>
								<inputType id="tool.nds.c.linker.input.99841531" superClass="tool.nds.c.linker.input">
									<additionalInput kind="additionalinputdependency" paths="$(USER_OBJS)"/>
									<additionalInput kind="additionalinput" paths="$(LIBS)"/>
								</inputType>
							</tool>
							<tool command="$(CROSS_COMPILE)as" commandLinePattern="${COMMAND} ${FLAGS} ${OUTPUT_FLAG} ${OUTPUT_PREFIX}${OUTPUT} ${INPUTS}" errorParsers="org.eclipse.cdt.core.GASErrorParser" id="tool.nds32le-elf-mculib-v3m.assembler.exe.debug.230552186" name="Andes Assembler" superClass="tool.nds32le-elf-mculib-v3m.assembler.exe.debug">
								<option defaultValue="-g" id="nds.both.asm.option.flags.1879965654" name="Assembler flags" superClass="nds.both.asm.option.flags" valueType="string"/>
								<inputType id="tool.nds.assembler.input.1483580106" superClass="tool.nds.assembler.input"/>
							</tool>
							<tool command="$(CROSS_COMPILE)nm" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot; ${OUTPUT_FLAG} ${OUTPUT}" errorParsers="" id="tool.nds32le-elf-mculib-v3m.nm.exe.debug.1426936597" name="NM (symbol listing)" superClass="tool.nds32le-elf-mculib-v3m.nm.exe.debug">
								<option id="nds.nm.option.sortsymbol.1965315369" name="Sort symbols numerically by address. (-n)" superClass="nds.nm.option.sortsymbol" value="true" valueType="boolean"/>
								<option id="nds.nm.option.printfilename.452655002" name="Print name of the input file before every symbol. (-A)" superClass="nds.nm.option.printfilename" value="false" valueType="boolean"/>
							</tool>
							<tool command="$(CROSS_COMPILE)readelf" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot; ${OUTPUT_FLAG} ${OUTPUT}" errorParsers="" id="tool.nds32le-elf-mculib-v3m.readelf.exe.debug.488867667" name="Readelf (ELF info listing)" superClass="tool.nds32le-elf-mculib-v3m.readelf.exe.debug"/>
							<tool command="$(CROSS_COMPILE)objdump" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot; ${OUTPUT_FLAG} ${OUTPUT}" errorParsers="" id="tool.nds32le-elf-mculib-v3m.objdump.exe.debug.180732025" name="Objdump (disassembly)" superClass="tool.nds32le-elf-mculib-v3m.objdump.exe.debug">
								<option id="nds.objdump.option.disable.446574069" name="Disable. (Do not auto-generate output file.)" superClass="nds.objdump.option.disable" value="false" valueType="boolean"/>
								<option id="nds.objdump.option.linenumber.1061177096" name="Include line numbers and filenames in output. (-l)" superClass="nds.objdump.option.linenumber" value="true" valueType="boolean"/>
								<option id="nds.objdump.option.otherflags.1736945923" name="Other flags" superClass="nds.objdump.option.otherflags" value="-S" valueType="string"/>
							</tool>
							<tool command="$(CROSS_COMPILE)objcopy" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot; ${OUTPUT}" errorParsers="" id="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug.1745727028" name="Objcopy (object content copy)" superClass="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug">
								<option id="nds.objcopy.option.disable.187568871" name="Disable. (Do not auto-generate output file.)" superClass="nds.objcopy.option.disable" value="false" valueType="boolean"/>
								<option id="nds.objcopy.option.stripall.850075718" name="Remove all symbol and relocation information. (-S)" superClass="nds.objcopy.option.stripall" value="false" valueType="boolean"/>
								<option id="nds.objcopy.option.otherflags.587436441" name="Other flags" superClass="nds.objcopy.option.otherflags" value="-R .flh_iram_fpu.data" valueType="string"/>
							</tool>
							<tool command="$(CROSS_COMPILE)size" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot;" errorParsers="" id="tool.nds32le-elf-mculib-v3m.size.exe.debug.167068658" name="Size (section size listing)" superClass="tool.nds32le-elf-mculib-v3m.size.exe.debug"/>
							<tool command="$(ANDESIGHT_ROOT)/utils/nds_ldsag" commandLinePattern="${COMMAND} ${FLAGS} ${INPUTS} -o $(LDSAG_OUT)" errorParsers="" id="tool.nds32le-elf-mculib-v3m.ldsag.base.1763809089" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.base"/>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.466457776" name="Andes C++ Compiler" superClass="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug">
								<option id="nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.option.debugging.level.1796614724" name="Debug Level" superClass="nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.option.debugging.level" value="nds.cpp.compiler.debugging.level.max" valueType="enumerated"/>
							</tool>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug.1778867354" name="Andes C++ Linker" superClass="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug">
								<option defaultValue="-O0" id="nds.cpp.link.option.optimization.level.976721157" name="Optimization Level" superClass="nds.cpp.link.option.optimization.level" valueType="string"/>
								<option defaultValue="true" id="nds32le-elf-mculib-v3m.cpp.link.option.noshared.base.342720436" name="No shared libraries (-static)" superClass="nds32le-elf-mculib-v3m.cpp.link.option.noshared.base" valueType="boolean"/>
							</tool>
							<tool command="$(ANDESIGHT_ROOT)/utils/nds_ldsag" commandLinePattern="${COMMAND} ${FLAGS} ${INPUTS} -o $(LDSAG_OUT)" errorParsers="" id="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug.1591074942" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug"/>
						</toolChain>
					</folderInfo>
					<folderInfo id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.576423965.515984178.opt_master_only" name="/" resourcePath="opt_master_only">
						<toolChain errorParsers="" id="nds.nds32le-elf-mculib-v3m.exe.debug.756023003" name="nds32le-elf-mculib-v3m" superClass="nds.nds32le-elf-mculib-v3m.exe.debug" unusedChildren="">
							<option id="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET.423936908.313979601.1887125436.1009016960.1709021572.205845821.1568783009" name="RSE_TARGET" superClass="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET.423936908"/>
							<targetPlatform id="target.nds32le-elf-mculib-v3m.platform.exe.debug.1076087549" name="Debug Platform" superClass="target.nds32le-elf-mculib-v3m.platform.exe.debug"/>
							<tool id="tool.nds32le-elf-mculib-v3m.archiver.base.1209229260" name="Andes Archiver" superClass="tool.nds32le-elf-mculib-v3m.archiver.base.1711288442"/>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.1163486724" name="Andes C++ Compiler" superClass="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.466457776">
								<option id="nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.option.debugging.level.819423509" name="Debug Level" superClass="nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.option.debugging.level" value="nds.cpp.compiler.debugging.level.max" valueType="enumerated"/>
							</tool>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug.250431307" name="Andes C++ Linker" superClass="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug.1778867354">
								<option defaultValue="-O0" id="nds.cpp.link.option.optimization.level.1885551139" name="Optimization Level" superClass="nds.cpp.link.option.optimization.level" valueType="string"/>
								<option defaultValue="true" id="nds32le-elf-mculib-v3m.cpp.link.option.noshared.base.849845424" name="No shared libraries (-static)" superClass="nds32le-elf-mculib-v3m.cpp.link.option.noshared.base" valueType="boolean"/>
							</tool>
							<tool errorParsers="org.eclipse.cdt.core.GCCErrorParser" id="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.1773682049" name="Andes C Compiler" superClass="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.1914355024">
								<inputType id="tool.nds.c.compiler.input.1187881399" superClass="tool.nds.c.compiler.input"/>
							</tool>
							<tool id="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug.832432690" name="Andes C Linker" superClass="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug.1368235164"/>
							<tool errorParsers="org.eclipse.cdt.core.GASErrorParser" id="tool.nds32le-elf-mculib-v3m.assembler.exe.debug.1937346424" name="Andes Assembler" superClass="tool.nds32le-elf-mculib-v3m.assembler.exe.debug.230552186">
								<inputType id="tool.nds.assembler.input.934346687" superClass="tool.nds.assembler.input"/>
							</tool>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.nm.exe.debug.123522480" name="NM (symbol listing)" superClass="tool.nds32le-elf-mculib-v3m.nm.exe.debug.1426936597"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.readelf.exe.debug.539339572" name="Readelf (ELF info listing)" superClass="tool.nds32le-elf-mculib-v3m.readelf.exe.debug.488867667"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.objdump.exe.debug.2073518939" name="Objdump (disassembly)" superClass="tool.nds32le-elf-mculib-v3m.objdump.exe.debug.180732025"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug.1853475837" name="Objcopy (object content copy)" superClass="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug.1745727028"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.size.exe.debug.717304811" name="Size (section size listing)" superClass="tool.nds32le-elf-mculib-v3m.size.exe.debug.167068658"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug.79276574" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug.1591074942"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.ldsag.base.195614950" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.base.1763809089"/>
						</toolChain>
					</folderInfo>
					<sourceEntries>
						<entry excluding="opt_master_only|Src_KIC|shr/inc/shr_types.h" flags="VALUE_WORKSPACE_PATH" kind="sourcePath" name=""/>
					</sourceEntries>
				</configuration>
			</storageModule>
			<storageModule moduleId="org.eclipse.cdt.core.externalSettings"/>
		</cconfiguration>
		<cconfiguration id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.576423965.515984178.748773599">
			<storageModule buildSystemId="org.eclipse.cdt.managedbuilder.core.configurationDataProvider" id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.576423965.515984178.748773599" moduleId="org.eclipse.cdt.core.settings" name="PS5017_Samsung_V7">
				<externalSettings/>
				<extensions>
					<extension id="com.andestech.ide.cdt.managedbuilder.core.CROSS_GNU_ELF" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="org.eclipse.cdt.core.GmakeErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.CWDLocator" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GCCErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GASErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GLDErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
				</extensions>
			</storageModule>
			<storageModule moduleId="cdtBuildSystem" version="4.0.0">
				<configuration artifactExtension="adx" artifactName="${ProjName}" buildArtefactType="nds.buildArtefactType.exe" buildProperties="org.eclipse.cdt.build.core.buildType=org.eclipse.cdt.build.core.buildType.debug,org.eclipse.cdt.build.core.buildArtefactType=nds.buildArtefactType.exe" description="" errorParsers="org.eclipse.cdt.core.GmakeErrorParser;org.eclipse.cdt.core.CWDLocator;org.eclipse.cdt.core.GCCErrorParser;org.eclipse.cdt.core.GASErrorParser;org.eclipse.cdt.core.GLDErrorParser" id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.576423965.515984178.748773599" name="PS5017_Samsung_V7" parent="config.nds32le-elf-mculib-v3m.exe.debug" postannouncebuildStep="" postbuildStep="" preannouncebuildStep="" prebuildStep="">
					<folderInfo id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.576423965.515984178.748773599." name="/" resourcePath="">
						<toolChain errorParsers="" id="nds.nds32le-elf-mculib-v3m.exe.debug.1453567817" name="nds32le-elf-mculib-v3m" superClass="nds.nds32le-elf-mculib-v3m.exe.debug">
							<option id="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET.1276526119" name="RSE_TARGET" superClass="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET" value="rse://LOCALHOST/~toolchains/nds32le-elf-mculib-v3m" valueType="string"/>
							<targetPlatform binaryParser="com.andestech.ide.cdt.managedbuilder.core.CROSS_GNU_ELF" id="target.nds32le-elf-mculib-v3m.platform.exe.debug.557077189" name="Debug Platform" superClass="target.nds32le-elf-mculib-v3m.platform.exe.debug"/>
							<builder buildPath="${workspace_loc:/ps5008_opt/Debug}" errorParsers="org.eclipse.cdt.core.GmakeErrorParser;org.eclipse.cdt.core.CWDLocator" id="target.nds32le-elf-mculib-v3m.builder.exe.debug.1768864639" keepEnvironmentInBuildfile="false" managedBuildOn="true" name="Andes Make Builder" parallelBuildOn="false" parallelizationNumber="-1" superClass="target.nds32le-elf-mculib-v3m.builder.exe.debug"/>
							<tool id="tool.nds32le-elf-mculib-v3m.archiver.base.150156458" name="Andes Archiver" superClass="tool.nds32le-elf-mculib-v3m.archiver.base"/>
							<tool command="$(CROSS_COMPILE)gcc" commandLinePattern="${COMMAND} ${FLAGS} ${OUTPUT_FLAG} ${OUTPUT_PREFIX}${OUTPUT} ${INPUTS}" errorParsers="org.eclipse.cdt.core.GCCErrorParser" id="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.869096700" name="Andes C Compiler" superClass="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug">
								<option id="nds32le-elf-mculib-v3m.c.compiler.exe.debug.option.debugging.level.1908317530" name="Debug Level" superClass="nds32le-elf-mculib-v3m.c.compiler.exe.debug.option.debugging.level" value="nds.c.debugging.level.max" valueType="enumerated"/>
								<option id="nds.c.compiler.option.include.paths.1593704655" name="Include paths (-I)" superClass="nds.c.compiler.option.include.paths" valueType="includePath">
									<listOptionValue builtIn="false" value="&quot;..\opt\inc&quot;"/>
									<listOptionValue builtIn="false" value="&quot;..\opt&quot;"/>
									<listOptionValue builtIn="false" value="&quot;..\shr\inc&quot;"/>
									<listOptionValue builtIn="false" value="&quot;..\startup\inc&quot;"/>
								</option>
								<option id="nds32le-elf-mculib-v3m.c.compiler.exe.debug.option.optimization.level.96054066" name="Optimization Level" superClass="nds32le-elf-mculib-v3m.c.compiler.exe.debug.option.optimization.level" value="nds.c.optimization.level.more" valueType="enumerated"/>
								<option id="nds.c.compiler.option.optimization.removesections.function.73566717" name="Remove unused function sections (-ffunction-sections)" superClass="nds.c.compiler.option.optimization.removesections.function" value="true" valueType="boolean"/>
								<option id="nds.c.compiler.option.optimization.removesections.data.1020604162" name="Remove unused data sections (-fdata-sections)" superClass="nds.c.compiler.option.optimization.removesections.data" value="true" valueType="boolean"/>
								<option id="nds.c.compiler.option.cmodel.1532727602" name="Code Model" superClass="nds.c.compiler.option.cmodel" value="nds.c.compiler.option.cmodel.medium" valueType="enumerated"/>
								<option id="nds.c.compiler.option.preprocessor.def.symbols.1140927151" name="Defined symbols (-D)" superClass="nds.c.compiler.option.preprocessor.def.symbols" valueType="definedSymbols">
									<listOptionValue builtIn="false" value="CATEGORY_CUSTOMER=CUSTOMER_SAMSUNG"/>
									<listOptionValue builtIn="false" value="CATEGORY_FLASH=FLASH_SAMSUNG_V7_TLC"/>
									<listOptionValue builtIn="false" value="CATEGORY_CONTROLLER=CONTROLLER_PS5017"/>
								</option>
								<inputType id="tool.nds.c.compiler.input.226401552" superClass="tool.nds.c.compiler.input"/>
							</tool>
							<tool command="$(CROSS_COMPILE)gcc" commandLinePattern="${COMMAND} ${FLAGS} ${OUTPUT_FLAG} ${OUTPUT_PREFIX}${OUTPUT} ${INPUTS}" errorParsers="org.eclipse.cdt.core.GLDErrorParser" id="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug.122367498" name="Andes C Linker" superClass="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug">
								<option defaultValue="-O0" id="nds.c.link.option.optimization.level.2086522735" name="Optimization Level" superClass="nds.c.link.option.optimization.level" value="-O2" valueType="string"/>
								<option defaultValue="true" id="nds32le-elf-mculib-v3m.c.link.option.noshared.base.1443898089" name="No shared libraries (-static)" superClass="nds32le-elf-mculib-v3m.c.link.option.noshared.base" valueType="boolean"/>
								<option defaultValue="" id="nds32le-elf-mculib-v3m.c.link.exe.debug.option.ldscript.183488032" name="Linker Script (-T)" superClass="nds32le-elf-mculib-v3m.c.link.exe.debug.option.ldscript" value="..\LinkerScript\ps5008_hw.ld" valueType="string"/>
								<option id="nds.c.linker.option.cmodel.1902180663" name="Code Model" superClass="nds.c.linker.option.cmodel" value="-mcmodel=medium" valueType="string"/>
								<option id="nds.c.link.option.map.1518658175" name="Write a map file. (-Map)" superClass="nds.c.link.option.map" value="true" valueType="boolean"/>
								<option id="nds.c.link.option.virtualhosting.924101996" name="Virtual Hosting (-mvh)" superClass="nds.c.link.option.virtualhosting" value="false" valueType="boolean"/>
								<option id="nds.c.link.option.nostart.838093602" name="Do not use standard start files (-nostartfiles)" superClass="nds.c.link.option.nostart" value="true" valueType="boolean"/>
								<option id="nds.c.link.option.removesections.823608371" name="Remove unused sections (-ffunction-sections or -fdata-sections)" superClass="nds.c.link.option.removesections" value="true" valueType="boolean"/>
								<inputType id="tool.nds.c.linker.input.275246209" superClass="tool.nds.c.linker.input">
									<additionalInput kind="additionalinputdependency" paths="$(USER_OBJS)"/>
									<additionalInput kind="additionalinput" paths="$(LIBS)"/>
								</inputType>
							</tool>
							<tool command="$(CROSS_COMPILE)as" commandLinePattern="${COMMAND} ${FLAGS} ${OUTPUT_FLAG} ${OUTPUT_PREFIX}${OUTPUT} ${INPUTS}" errorParsers="org.eclipse.cdt.core.GASErrorParser" id="tool.nds32le-elf-mculib-v3m.assembler.exe.debug.999108865" name="Andes Assembler" superClass="tool.nds32le-elf-mculib-v3m.assembler.exe.debug">
								<option defaultValue="-g" id="nds.both.asm.option.flags.1075075128" name="Assembler flags" superClass="nds.both.asm.option.flags" valueType="string"/>
								<inputType id="tool.nds.assembler.input.850128705" superClass="tool.nds.assembler.input"/>
							</tool>
							<tool command="$(CROSS_COMPILE)nm" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot; ${OUTPUT_FLAG} ${OUTPUT}" errorParsers="" id="tool.nds32le-elf-mculib-v3m.nm.exe.debug.1177644005" name="NM (symbol listing)" superClass="tool.nds32le-elf-mculib-v3m.nm.exe.debug">
								<option id="nds.nm.option.sortsymbol.224880748" name="Sort symbols numerically by address. (-n)" superClass="nds.nm.option.sortsymbol" value="true" valueType="boolean"/>
								<option id="nds.nm.option.printfilename.471426290" name="Print name of the input file before every symbol. (-A)" superClass="nds.nm.option.printfilename" value="false" valueType="boolean"/>
							</tool>
							<tool command="$(CROSS_COMPILE)readelf" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot; ${OUTPUT_FLAG} ${OUTPUT}" errorParsers="" id="tool.nds32le-elf-mculib-v3m.readelf.exe.debug.468604787" name="Readelf (ELF info listing)" superClass="tool.nds32le-elf-mculib-v3m.readelf.exe.debug"/>
							<tool command="$(CROSS_COMPILE)objdump" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot; ${OUTPUT_FLAG} ${OUTPUT}" errorParsers="" id="tool.nds32le-elf-mculib-v3m.objdump.exe.debug.218477749" name="Objdump (disassembly)" superClass="tool.nds32le-elf-mculib-v3m.objdump.exe.debug">
								<option id="nds.objdump.option.disable.1441983414" name="Disable. (Do not auto-generate output file.)" superClass="nds.objdump.option.disable" value="false" valueType="boolean"/>
								<option id="nds.objdump.option.linenumber.2016002005" name="Include line numbers and filenames in output. (-l)" superClass="nds.objdump.option.linenumber" value="true" valueType="boolean"/>
								<option id="nds.objdump.option.otherflags.1849359024" name="Other flags" superClass="nds.objdump.option.otherflags" value="-S" valueType="string"/>
							</tool>
							<tool command="$(CROSS_COMPILE)objcopy" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot; ${OUTPUT}" errorParsers="" id="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug.1294667865" name="Objcopy (object content copy)" superClass="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug">
								<option id="nds.objcopy.option.disable.925243829" name="Disable. (Do not auto-generate output file.)" superClass="nds.objcopy.option.disable" value="false" valueType="boolean"/>
								<option id="nds.objcopy.option.stripall.726550158" name="Remove all symbol and relocation information. (-S)" superClass="nds.objcopy.option.stripall" value="false" valueType="boolean"/>
								<option id="nds.objcopy.option.otherflags.99315544" name="Other flags" superClass="nds.objcopy.option.otherflags" value="-R .flh_iram_fpu.data" valueType="string"/>
							</tool>
							<tool command="$(CROSS_COMPILE)size" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot;" errorParsers="" id="tool.nds32le-elf-mculib-v3m.size.exe.debug.2097445274" name="Size (section size listing)" superClass="tool.nds32le-elf-mculib-v3m.size.exe.debug"/>
							<tool command="$(ANDESIGHT_ROOT)/utils/nds_ldsag" commandLinePattern="${COMMAND} ${FLAGS} ${INPUTS} -o $(LDSAG_OUT)" errorParsers="" id="tool.nds32le-elf-mculib-v3m.ldsag.base.2117808774" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.base"/>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.2673065" name="Andes C++ Compiler" superClass="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug">
								<option id="nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.option.debugging.level.1539478476" name="Debug Level" superClass="nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.option.debugging.level" value="nds.cpp.compiler.debugging.level.max" valueType="enumerated"/>
							</tool>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug.1541431814" name="Andes C++ Linker" superClass="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug">
								<option defaultValue="-O0" id="nds.cpp.link.option.optimization.level.1338383029" name="Optimization Level" superClass="nds.cpp.link.option.optimization.level" valueType="string"/>
								<option defaultValue="true" id="nds32le-elf-mculib-v3m.cpp.link.option.noshared.base.689461871" name="No shared libraries (-static)" superClass="nds32le-elf-mculib-v3m.cpp.link.option.noshared.base" valueType="boolean"/>
							</tool>
							<tool command="$(ANDESIGHT_ROOT)/utils/nds_ldsag" commandLinePattern="${COMMAND} ${FLAGS} ${INPUTS} -o $(LDSAG_OUT)" errorParsers="" id="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug.1740365426" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug"/>
						</toolChain>
					</folderInfo>
					<folderInfo id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.576423965.515984178.748773599.opt_master_only" name="/" resourcePath="opt_master_only">
						<toolChain errorParsers="" id="nds.nds32le-elf-mculib-v3m.exe.debug.1792095458" name="nds32le-elf-mculib-v3m" superClass="nds.nds32le-elf-mculib-v3m.exe.debug" unusedChildren="">
							<option id="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET.423936908.313979601.1887125436.1009016960.1709021572.205845821.1568783009.495878407" name="RSE_TARGET" superClass="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET.423936908"/>
							<targetPlatform id="target.nds32le-elf-mculib-v3m.platform.exe.debug.348414451" name="Debug Platform" superClass="target.nds32le-elf-mculib-v3m.platform.exe.debug"/>
							<tool id="tool.nds32le-elf-mculib-v3m.archiver.base.1991709991" name="Andes Archiver" superClass="tool.nds32le-elf-mculib-v3m.archiver.base.150156458"/>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.635282768" name="Andes C++ Compiler" superClass="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.2673065">
								<option id="nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.option.debugging.level.1130850132" name="Debug Level" superClass="nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.option.debugging.level" value="nds.cpp.compiler.debugging.level.max" valueType="enumerated"/>
							</tool>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug.1292460837" name="Andes C++ Linker" superClass="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug.1541431814">
								<option defaultValue="-O0" id="nds.cpp.link.option.optimization.level.336343114" name="Optimization Level" superClass="nds.cpp.link.option.optimization.level" valueType="string"/>
								<option defaultValue="true" id="nds32le-elf-mculib-v3m.cpp.link.option.noshared.base.873705745" name="No shared libraries (-static)" superClass="nds32le-elf-mculib-v3m.cpp.link.option.noshared.base" valueType="boolean"/>
							</tool>
							<tool errorParsers="org.eclipse.cdt.core.GCCErrorParser" id="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.780810840" name="Andes C Compiler" superClass="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.869096700">
								<inputType id="tool.nds.c.compiler.input.110462959" superClass="tool.nds.c.compiler.input"/>
							</tool>
							<tool id="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug.1353185849" name="Andes C Linker" superClass="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug.122367498"/>
							<tool errorParsers="org.eclipse.cdt.core.GASErrorParser" id="tool.nds32le-elf-mculib-v3m.assembler.exe.debug.1840269202" name="Andes Assembler" superClass="tool.nds32le-elf-mculib-v3m.assembler.exe.debug.999108865">
								<inputType id="tool.nds.assembler.input.1242160723" superClass="tool.nds.assembler.input"/>
							</tool>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.nm.exe.debug.2120382109" name="NM (symbol listing)" superClass="tool.nds32le-elf-mculib-v3m.nm.exe.debug.1177644005"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.readelf.exe.debug.769531289" name="Readelf (ELF info listing)" superClass="tool.nds32le-elf-mculib-v3m.readelf.exe.debug.468604787"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.objdump.exe.debug.1131899745" name="Objdump (disassembly)" superClass="tool.nds32le-elf-mculib-v3m.objdump.exe.debug.218477749"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug.1628255803" name="Objcopy (object content copy)" superClass="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug.1294667865"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.size.exe.debug.68149988" name="Size (section size listing)" superClass="tool.nds32le-elf-mculib-v3m.size.exe.debug.2097445274"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug.1056991582" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug.1740365426"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.ldsag.base.620377431" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.base.2117808774"/>
						</toolChain>
					</folderInfo>
					<sourceEntries>
						<entry excluding="opt_master_only|Src_KIC|shr/inc/shr_types.h" flags="VALUE_WORKSPACE_PATH" kind="sourcePath" name=""/>
					</sourceEntries>
				</configuration>
			</storageModule>
			<storageModule moduleId="org.eclipse.cdt.core.externalSettings"/>
		</cconfiguration>
		<cconfiguration id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********.134906153.431926974">
			<storageModule buildSystemId="org.eclipse.cdt.managedbuilder.core.configurationDataProvider" id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********.134906153.431926974" moduleId="org.eclipse.cdt.core.settings" name="PS5017_BICS6_QLC">
				<externalSettings/>
				<extensions>
					<extension id="com.andestech.ide.cdt.managedbuilder.core.CROSS_GNU_ELF" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="org.eclipse.cdt.core.GmakeErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.CWDLocator" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GCCErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GASErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GLDErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
				</extensions>
			</storageModule>
			<storageModule moduleId="cdtBuildSystem" version="4.0.0">
				<configuration artifactExtension="adx" artifactName="${ProjName}" buildArtefactType="nds.buildArtefactType.exe" buildProperties="org.eclipse.cdt.build.core.buildType=org.eclipse.cdt.build.core.buildType.debug,org.eclipse.cdt.build.core.buildArtefactType=nds.buildArtefactType.exe" description="" errorParsers="org.eclipse.cdt.core.GmakeErrorParser;org.eclipse.cdt.core.CWDLocator;org.eclipse.cdt.core.GCCErrorParser;org.eclipse.cdt.core.GASErrorParser;org.eclipse.cdt.core.GLDErrorParser" id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********.134906153.431926974" name="PS5017_BICS6_QLC" parent="config.nds32le-elf-mculib-v3m.exe.debug" postannouncebuildStep="" postbuildStep="" preannouncebuildStep="" prebuildStep="">
					<folderInfo id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********.134906153.431926974." name="/" resourcePath="">
						<toolChain errorParsers="" id="nds.nds32le-elf-mculib-v3m.exe.debug.313321005" name="nds32le-elf-mculib-v3m" superClass="nds.nds32le-elf-mculib-v3m.exe.debug">
							<option id="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET.925884475" name="RSE_TARGET" superClass="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET" value="rse://LOCALHOST/~toolchains/nds32le-elf-mculib-v3m" valueType="string"/>
							<targetPlatform binaryParser="com.andestech.ide.cdt.managedbuilder.core.CROSS_GNU_ELF" id="target.nds32le-elf-mculib-v3m.platform.exe.debug.1539978043" name="Debug Platform" superClass="target.nds32le-elf-mculib-v3m.platform.exe.debug"/>
							<builder buildPath="${workspace_loc:/ps5008_opt/Debug}" errorParsers="org.eclipse.cdt.core.GmakeErrorParser;org.eclipse.cdt.core.CWDLocator" id="target.nds32le-elf-mculib-v3m.builder.exe.debug.1911851320" keepEnvironmentInBuildfile="false" managedBuildOn="true" name="Andes Make Builder" parallelBuildOn="false" parallelizationNumber="-1" superClass="target.nds32le-elf-mculib-v3m.builder.exe.debug"/>
							<tool id="tool.nds32le-elf-mculib-v3m.archiver.base.797384454" name="Andes Archiver" superClass="tool.nds32le-elf-mculib-v3m.archiver.base"/>
							<tool command="$(CROSS_COMPILE)gcc" commandLinePattern="${COMMAND} ${FLAGS} ${OUTPUT_FLAG} ${OUTPUT_PREFIX}${OUTPUT} ${INPUTS}" errorParsers="org.eclipse.cdt.core.GCCErrorParser" id="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.1161894390" name="Andes C Compiler" superClass="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug">
								<option id="nds32le-elf-mculib-v3m.c.compiler.exe.debug.option.debugging.level.2009639695" name="Debug Level" superClass="nds32le-elf-mculib-v3m.c.compiler.exe.debug.option.debugging.level" value="nds.c.debugging.level.max" valueType="enumerated"/>
								<option id="nds.c.compiler.option.include.paths.1010520446" name="Include paths (-I)" superClass="nds.c.compiler.option.include.paths" valueType="includePath">
									<listOptionValue builtIn="false" value="&quot;..\opt\inc&quot;"/>
									<listOptionValue builtIn="false" value="&quot;..\opt&quot;"/>
									<listOptionValue builtIn="false" value="&quot;..\shr\inc&quot;"/>
									<listOptionValue builtIn="false" value="&quot;..\startup\inc&quot;"/>
								</option>
								<option id="nds32le-elf-mculib-v3m.c.compiler.exe.debug.option.optimization.level.178661647" name="Optimization Level" superClass="nds32le-elf-mculib-v3m.c.compiler.exe.debug.option.optimization.level" value="nds.c.optimization.level.more" valueType="enumerated"/>
								<option id="nds.c.compiler.option.optimization.removesections.function.2083459698" name="Remove unused function sections (-ffunction-sections)" superClass="nds.c.compiler.option.optimization.removesections.function" value="true" valueType="boolean"/>
								<option id="nds.c.compiler.option.optimization.removesections.data.1566834422" name="Remove unused data sections (-fdata-sections)" superClass="nds.c.compiler.option.optimization.removesections.data" value="true" valueType="boolean"/>
								<option id="nds.c.compiler.option.cmodel.80299319" name="Code Model" superClass="nds.c.compiler.option.cmodel" value="nds.c.compiler.option.cmodel.medium" valueType="enumerated"/>
								<option id="nds.c.compiler.option.preprocessor.def.symbols.47593494" name="Defined symbols (-D)" superClass="nds.c.compiler.option.preprocessor.def.symbols" valueType="definedSymbols">
									<listOptionValue builtIn="false" value="CATEGORY_CUSTOMER=CUSTOMER_SANDISK"/>
									<listOptionValue builtIn="false" value="CATEGORY_FLASH=FLASH_SANDISK_BICS6_QLC"/>
									<listOptionValue builtIn="false" value="CATEGORY_CONTROLLER=CONTROLLER_PS5017"/>
								</option>
								<inputType id="tool.nds.c.compiler.input.2044093351" superClass="tool.nds.c.compiler.input"/>
							</tool>
							<tool command="$(CROSS_COMPILE)gcc" commandLinePattern="${COMMAND} ${FLAGS} ${OUTPUT_FLAG} ${OUTPUT_PREFIX}${OUTPUT} ${INPUTS}" errorParsers="org.eclipse.cdt.core.GLDErrorParser" id="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug.1887008833" name="Andes C Linker" superClass="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug">
								<option defaultValue="-O0" id="nds.c.link.option.optimization.level.953048183" name="Optimization Level" superClass="nds.c.link.option.optimization.level" value="-O2" valueType="string"/>
								<option defaultValue="true" id="nds32le-elf-mculib-v3m.c.link.option.noshared.base.1616184704" name="No shared libraries (-static)" superClass="nds32le-elf-mculib-v3m.c.link.option.noshared.base" valueType="boolean"/>
								<option defaultValue="" id="nds32le-elf-mculib-v3m.c.link.exe.debug.option.ldscript.1813879273" name="Linker Script (-T)" superClass="nds32le-elf-mculib-v3m.c.link.exe.debug.option.ldscript" value="..\LinkerScript\ps5008_hw.ld" valueType="string"/>
								<option id="nds.c.linker.option.cmodel.763415792" name="Code Model" superClass="nds.c.linker.option.cmodel" value="-mcmodel=medium" valueType="string"/>
								<option id="nds.c.link.option.map.262523024" name="Write a map file. (-Map)" superClass="nds.c.link.option.map" value="true" valueType="boolean"/>
								<option id="nds.c.link.option.virtualhosting.13671852" name="Virtual Hosting (-mvh)" superClass="nds.c.link.option.virtualhosting" value="false" valueType="boolean"/>
								<option id="nds.c.link.option.nostart.1841732312" name="Do not use standard start files (-nostartfiles)" superClass="nds.c.link.option.nostart" value="true" valueType="boolean"/>
								<option id="nds.c.link.option.removesections.287461595" name="Remove unused sections (-ffunction-sections or -fdata-sections)" superClass="nds.c.link.option.removesections" value="true" valueType="boolean"/>
								<inputType id="tool.nds.c.linker.input.2004082724" superClass="tool.nds.c.linker.input">
									<additionalInput kind="additionalinputdependency" paths="$(USER_OBJS)"/>
									<additionalInput kind="additionalinput" paths="$(LIBS)"/>
								</inputType>
							</tool>
							<tool command="$(CROSS_COMPILE)as" commandLinePattern="${COMMAND} ${FLAGS} ${OUTPUT_FLAG} ${OUTPUT_PREFIX}${OUTPUT} ${INPUTS}" errorParsers="org.eclipse.cdt.core.GASErrorParser" id="tool.nds32le-elf-mculib-v3m.assembler.exe.debug.1747991109" name="Andes Assembler" superClass="tool.nds32le-elf-mculib-v3m.assembler.exe.debug">
								<option defaultValue="-g" id="nds.both.asm.option.flags.538283580" name="Assembler flags" superClass="nds.both.asm.option.flags" valueType="string"/>
								<inputType id="tool.nds.assembler.input.1892952272" superClass="tool.nds.assembler.input"/>
							</tool>
							<tool command="$(CROSS_COMPILE)nm" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot; ${OUTPUT_FLAG} ${OUTPUT}" errorParsers="" id="tool.nds32le-elf-mculib-v3m.nm.exe.debug.307013524" name="NM (symbol listing)" superClass="tool.nds32le-elf-mculib-v3m.nm.exe.debug">
								<option id="nds.nm.option.sortsymbol.1281349191" name="Sort symbols numerically by address. (-n)" superClass="nds.nm.option.sortsymbol" value="true" valueType="boolean"/>
								<option id="nds.nm.option.printfilename.1221753263" name="Print name of the input file before every symbol. (-A)" superClass="nds.nm.option.printfilename" value="false" valueType="boolean"/>
							</tool>
							<tool command="$(CROSS_COMPILE)readelf" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot; ${OUTPUT_FLAG} ${OUTPUT}" errorParsers="" id="tool.nds32le-elf-mculib-v3m.readelf.exe.debug.1811068676" name="Readelf (ELF info listing)" superClass="tool.nds32le-elf-mculib-v3m.readelf.exe.debug"/>
							<tool command="$(CROSS_COMPILE)objdump" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot; ${OUTPUT_FLAG} ${OUTPUT}" errorParsers="" id="tool.nds32le-elf-mculib-v3m.objdump.exe.debug.708138814" name="Objdump (disassembly)" superClass="tool.nds32le-elf-mculib-v3m.objdump.exe.debug">
								<option id="nds.objdump.option.disable.725990407" name="Disable. (Do not auto-generate output file.)" superClass="nds.objdump.option.disable" value="false" valueType="boolean"/>
								<option id="nds.objdump.option.linenumber.1239888775" name="Include line numbers and filenames in output. (-l)" superClass="nds.objdump.option.linenumber" value="true" valueType="boolean"/>
								<option id="nds.objdump.option.otherflags.1806863269" name="Other flags" superClass="nds.objdump.option.otherflags" value="-S" valueType="string"/>
							</tool>
							<tool command="$(CROSS_COMPILE)objcopy" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot; ${OUTPUT}" errorParsers="" id="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug.1974933042" name="Objcopy (object content copy)" superClass="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug">
								<option id="nds.objcopy.option.disable.150538059" name="Disable. (Do not auto-generate output file.)" superClass="nds.objcopy.option.disable" value="false" valueType="boolean"/>
								<option id="nds.objcopy.option.stripall.63587361" name="Remove all symbol and relocation information. (-S)" superClass="nds.objcopy.option.stripall" value="false" valueType="boolean"/>
								<option id="nds.objcopy.option.otherflags.2101986094" name="Other flags" superClass="nds.objcopy.option.otherflags" value="-R .flh_iram_fpu.data" valueType="string"/>
							</tool>
							<tool command="$(CROSS_COMPILE)size" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot;" errorParsers="" id="tool.nds32le-elf-mculib-v3m.size.exe.debug.429722815" name="Size (section size listing)" superClass="tool.nds32le-elf-mculib-v3m.size.exe.debug"/>
							<tool command="$(ANDESIGHT_ROOT)/utils/nds_ldsag" commandLinePattern="${COMMAND} ${FLAGS} ${INPUTS} -o $(LDSAG_OUT)" errorParsers="" id="tool.nds32le-elf-mculib-v3m.ldsag.base.2010888247" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.base"/>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.1864877258" name="Andes C++ Compiler" superClass="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug">
								<option id="nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.option.debugging.level.350194181" name="Debug Level" superClass="nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.option.debugging.level" value="nds.cpp.compiler.debugging.level.max" valueType="enumerated"/>
							</tool>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug.1377757904" name="Andes C++ Linker" superClass="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug">
								<option defaultValue="-O0" id="nds.cpp.link.option.optimization.level.662745866" name="Optimization Level" superClass="nds.cpp.link.option.optimization.level" valueType="string"/>
								<option defaultValue="true" id="nds32le-elf-mculib-v3m.cpp.link.option.noshared.base.1934831929" name="No shared libraries (-static)" superClass="nds32le-elf-mculib-v3m.cpp.link.option.noshared.base" valueType="boolean"/>
							</tool>
							<tool command="$(ANDESIGHT_ROOT)/utils/nds_ldsag" commandLinePattern="${COMMAND} ${FLAGS} ${INPUTS} -o $(LDSAG_OUT)" errorParsers="" id="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug.1541410080" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug"/>
						</toolChain>
					</folderInfo>
					<folderInfo id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********.134906153.431926974.PS5017_BISC5" name="/" resourcePath="PS5017_BISC5">
						<toolChain id="nds.nds32le-elf-mculib-v3m.exe.debug.1505373676" name="nds32le-elf-mculib-v3m" superClass="nds.nds32le-elf-mculib-v3m.exe.debug" unusedChildren="">
							<option id="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET.683971888.2125547364.913307565.1366184660" name="RSE_TARGET" superClass="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET.683971888"/>
							<targetPlatform id="target.nds32le-elf-mculib-v3m.platform.exe.debug.1094991526" name="Debug Platform" superClass="target.nds32le-elf-mculib-v3m.platform.exe.debug"/>
							<tool id="tool.nds32le-elf-mculib-v3m.archiver.base.1285386123" name="Andes Archiver" superClass="tool.nds32le-elf-mculib-v3m.archiver.base.797384454"/>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.2123933481" name="Andes C++ Compiler" superClass="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.1864877258"/>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug.1743036679" name="Andes C++ Linker" superClass="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug.1377757904"/>
							<tool id="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.1155499617" name="Andes C Compiler" superClass="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.1161894390">
								<option id="nds.c.compiler.option.preprocessor.def.symbols.2013249035" name="Defined symbols (-D)" superClass="nds.c.compiler.option.preprocessor.def.symbols" valueType="definedSymbols">
									<listOptionValue builtIn="false" value="CATEGORY_CUSTOMER=CUSTOMER_SANDISK"/>
									<listOptionValue builtIn="false" value="CATEGORY_FLASH=FLASH_SANDISK_BICS5_TLC"/>
									<listOptionValue builtIn="false" value="CATEGORY_CONTROLLER=CONTROLLER_PS5017"/>
								</option>
								<inputType id="tool.nds.c.compiler.input.440070717" superClass="tool.nds.c.compiler.input"/>
							</tool>
							<tool id="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug.282622385" name="Andes C Linker" superClass="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug.1887008833"/>
							<tool id="tool.nds32le-elf-mculib-v3m.assembler.exe.debug.1369535182" name="Andes Assembler" superClass="tool.nds32le-elf-mculib-v3m.assembler.exe.debug.1747991109">
								<inputType id="tool.nds.assembler.input.2009799240" superClass="tool.nds.assembler.input"/>
							</tool>
							<tool id="tool.nds32le-elf-mculib-v3m.nm.exe.debug.680458205" name="NM (symbol listing)" superClass="tool.nds32le-elf-mculib-v3m.nm.exe.debug.307013524"/>
							<tool id="tool.nds32le-elf-mculib-v3m.readelf.exe.debug.788071897" name="Readelf (ELF info listing)" superClass="tool.nds32le-elf-mculib-v3m.readelf.exe.debug.1811068676"/>
							<tool id="tool.nds32le-elf-mculib-v3m.objdump.exe.debug.1228096259" name="Objdump (disassembly)" superClass="tool.nds32le-elf-mculib-v3m.objdump.exe.debug.708138814"/>
							<tool id="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug.439155357" name="Objcopy (object content copy)" superClass="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug.1974933042"/>
							<tool id="tool.nds32le-elf-mculib-v3m.size.exe.debug.959281804" name="Size (section size listing)" superClass="tool.nds32le-elf-mculib-v3m.size.exe.debug.429722815"/>
							<tool id="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug.1001860378" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug.1541410080"/>
							<tool id="tool.nds32le-elf-mculib-v3m.ldsag.base.1912209470" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.base.2010888247"/>
						</toolChain>
					</folderInfo>
					<folderInfo id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********.134906153.431926974.opt_master_only" name="/" resourcePath="opt_master_only">
						<toolChain errorParsers="" id="nds.nds32le-elf-mculib-v3m.exe.debug.598300739" name="nds32le-elf-mculib-v3m" superClass="nds.nds32le-elf-mculib-v3m.exe.debug" unusedChildren="">
							<option id="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET.423936908.313979601.1887125436.1009016960.1709021572.1412875331.1151677017.1275424518" name="RSE_TARGET" superClass="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET.423936908"/>
							<targetPlatform id="target.nds32le-elf-mculib-v3m.platform.exe.debug.1805251897" name="Debug Platform" superClass="target.nds32le-elf-mculib-v3m.platform.exe.debug"/>
							<tool id="tool.nds32le-elf-mculib-v3m.archiver.base.110371413" name="Andes Archiver" superClass="tool.nds32le-elf-mculib-v3m.archiver.base.797384454"/>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.1195041373" name="Andes C++ Compiler" superClass="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.1864877258">
								<option id="nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.option.debugging.level.1955159597" name="Debug Level" superClass="nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.option.debugging.level" value="nds.cpp.compiler.debugging.level.max" valueType="enumerated"/>
							</tool>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug.730218866" name="Andes C++ Linker" superClass="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug.1377757904">
								<option defaultValue="-O0" id="nds.cpp.link.option.optimization.level.724617266" name="Optimization Level" superClass="nds.cpp.link.option.optimization.level" valueType="string"/>
								<option defaultValue="true" id="nds32le-elf-mculib-v3m.cpp.link.option.noshared.base.229781683" name="No shared libraries (-static)" superClass="nds32le-elf-mculib-v3m.cpp.link.option.noshared.base" valueType="boolean"/>
							</tool>
							<tool errorParsers="org.eclipse.cdt.core.GCCErrorParser" id="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.1628457803" name="Andes C Compiler" superClass="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.1161894390">
								<inputType id="tool.nds.c.compiler.input.1908529904" superClass="tool.nds.c.compiler.input"/>
							</tool>
							<tool id="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug.1486715763" name="Andes C Linker" superClass="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug.1887008833"/>
							<tool errorParsers="org.eclipse.cdt.core.GASErrorParser" id="tool.nds32le-elf-mculib-v3m.assembler.exe.debug.1239863989" name="Andes Assembler" superClass="tool.nds32le-elf-mculib-v3m.assembler.exe.debug.1747991109">
								<inputType id="tool.nds.assembler.input.2077125478" superClass="tool.nds.assembler.input"/>
							</tool>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.nm.exe.debug.1007816516" name="NM (symbol listing)" superClass="tool.nds32le-elf-mculib-v3m.nm.exe.debug.307013524"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.readelf.exe.debug.2079088406" name="Readelf (ELF info listing)" superClass="tool.nds32le-elf-mculib-v3m.readelf.exe.debug.1811068676"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.objdump.exe.debug.1249612006" name="Objdump (disassembly)" superClass="tool.nds32le-elf-mculib-v3m.objdump.exe.debug.708138814"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug.1247537267" name="Objcopy (object content copy)" superClass="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug.1974933042"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.size.exe.debug.1985019227" name="Size (section size listing)" superClass="tool.nds32le-elf-mculib-v3m.size.exe.debug.429722815"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug.1830425089" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug.1541410080"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.ldsag.base.1329439021" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.base.2010888247"/>
						</toolChain>
					</folderInfo>
					<sourceEntries>
						<entry excluding="opt_master_only|Src_KIC|shr/inc/shr_types.h" flags="VALUE_WORKSPACE_PATH" kind="sourcePath" name=""/>
					</sourceEntries>
				</configuration>
			</storageModule>
			<storageModule moduleId="org.eclipse.cdt.core.externalSettings"/>
		</cconfiguration>
		<cconfiguration id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.576423965.515984178.748773599.**********">
			<storageModule buildSystemId="org.eclipse.cdt.managedbuilder.core.configurationDataProvider" id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.576423965.515984178.748773599.**********" moduleId="org.eclipse.cdt.core.settings" name="PS5017_Samsung_V8">
				<externalSettings/>
				<extensions>
					<extension id="com.andestech.ide.cdt.managedbuilder.core.CROSS_GNU_ELF" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="org.eclipse.cdt.core.GmakeErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.CWDLocator" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GCCErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GASErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GLDErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
				</extensions>
			</storageModule>
			<storageModule moduleId="cdtBuildSystem" version="4.0.0">
				<configuration artifactExtension="adx" artifactName="${ProjName}" buildArtefactType="nds.buildArtefactType.exe" buildProperties="org.eclipse.cdt.build.core.buildType=org.eclipse.cdt.build.core.buildType.debug,org.eclipse.cdt.build.core.buildArtefactType=nds.buildArtefactType.exe" description="" errorParsers="org.eclipse.cdt.core.GmakeErrorParser;org.eclipse.cdt.core.CWDLocator;org.eclipse.cdt.core.GCCErrorParser;org.eclipse.cdt.core.GASErrorParser;org.eclipse.cdt.core.GLDErrorParser" id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.576423965.515984178.748773599.**********" name="PS5017_Samsung_V8" parent="config.nds32le-elf-mculib-v3m.exe.debug" postannouncebuildStep="" postbuildStep="" preannouncebuildStep="" prebuildStep="">
					<folderInfo id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.576423965.515984178.748773599.**********." name="/" resourcePath="">
						<toolChain errorParsers="" id="nds.nds32le-elf-mculib-v3m.exe.debug.1678650454" name="nds32le-elf-mculib-v3m" superClass="nds.nds32le-elf-mculib-v3m.exe.debug">
							<option id="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET.154904954" name="RSE_TARGET" superClass="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET" value="rse://LOCALHOST/~toolchains/nds32le-elf-mculib-v3m" valueType="string"/>
							<targetPlatform binaryParser="com.andestech.ide.cdt.managedbuilder.core.CROSS_GNU_ELF" id="target.nds32le-elf-mculib-v3m.platform.exe.debug.1405662627" name="Debug Platform" superClass="target.nds32le-elf-mculib-v3m.platform.exe.debug"/>
							<builder buildPath="${workspace_loc:/ps5008_opt/Debug}" errorParsers="org.eclipse.cdt.core.GmakeErrorParser;org.eclipse.cdt.core.CWDLocator" id="target.nds32le-elf-mculib-v3m.builder.exe.debug.1658475060" keepEnvironmentInBuildfile="false" managedBuildOn="true" name="Andes Make Builder" parallelBuildOn="false" parallelizationNumber="-1" superClass="target.nds32le-elf-mculib-v3m.builder.exe.debug"/>
							<tool id="tool.nds32le-elf-mculib-v3m.archiver.base.175982725" name="Andes Archiver" superClass="tool.nds32le-elf-mculib-v3m.archiver.base"/>
							<tool command="$(CROSS_COMPILE)gcc" commandLinePattern="${COMMAND} ${FLAGS} ${OUTPUT_FLAG} ${OUTPUT_PREFIX}${OUTPUT} ${INPUTS}" errorParsers="org.eclipse.cdt.core.GCCErrorParser" id="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.1221958212" name="Andes C Compiler" superClass="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug">
								<option id="nds32le-elf-mculib-v3m.c.compiler.exe.debug.option.debugging.level.184753015" name="Debug Level" superClass="nds32le-elf-mculib-v3m.c.compiler.exe.debug.option.debugging.level" value="nds.c.debugging.level.max" valueType="enumerated"/>
								<option id="nds.c.compiler.option.include.paths.725043329" name="Include paths (-I)" superClass="nds.c.compiler.option.include.paths" valueType="includePath">
									<listOptionValue builtIn="false" value="&quot;..\opt\inc&quot;"/>
									<listOptionValue builtIn="false" value="&quot;..\opt&quot;"/>
									<listOptionValue builtIn="false" value="&quot;..\shr\inc&quot;"/>
									<listOptionValue builtIn="false" value="&quot;..\startup\inc&quot;"/>
								</option>
								<option id="nds32le-elf-mculib-v3m.c.compiler.exe.debug.option.optimization.level.969603136" name="Optimization Level" superClass="nds32le-elf-mculib-v3m.c.compiler.exe.debug.option.optimization.level" value="nds.c.optimization.level.more" valueType="enumerated"/>
								<option id="nds.c.compiler.option.optimization.removesections.function.2040807347" name="Remove unused function sections (-ffunction-sections)" superClass="nds.c.compiler.option.optimization.removesections.function" value="true" valueType="boolean"/>
								<option id="nds.c.compiler.option.optimization.removesections.data.1628692490" name="Remove unused data sections (-fdata-sections)" superClass="nds.c.compiler.option.optimization.removesections.data" value="true" valueType="boolean"/>
								<option id="nds.c.compiler.option.cmodel.564897977" name="Code Model" superClass="nds.c.compiler.option.cmodel" value="nds.c.compiler.option.cmodel.medium" valueType="enumerated"/>
								<option id="nds.c.compiler.option.preprocessor.def.symbols.1739081434" name="Defined symbols (-D)" superClass="nds.c.compiler.option.preprocessor.def.symbols" valueType="definedSymbols">
									<listOptionValue builtIn="false" value="CATEGORY_CUSTOMER=CUSTOMER_SAMSUNG"/>
									<listOptionValue builtIn="false" value="CATEGORY_FLASH=FLASH_SAMSUNG_V8_TLC"/>
									<listOptionValue builtIn="false" value="CATEGORY_CONTROLLER=CONTROLLER_PS5017"/>
								</option>
								<inputType id="tool.nds.c.compiler.input.1353657029" superClass="tool.nds.c.compiler.input"/>
							</tool>
							<tool command="$(CROSS_COMPILE)gcc" commandLinePattern="${COMMAND} ${FLAGS} ${OUTPUT_FLAG} ${OUTPUT_PREFIX}${OUTPUT} ${INPUTS}" errorParsers="org.eclipse.cdt.core.GLDErrorParser" id="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug.1821315089" name="Andes C Linker" superClass="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug">
								<option defaultValue="-O0" id="nds.c.link.option.optimization.level.193192131" name="Optimization Level" superClass="nds.c.link.option.optimization.level" value="-O2" valueType="string"/>
								<option defaultValue="true" id="nds32le-elf-mculib-v3m.c.link.option.noshared.base.246060051" name="No shared libraries (-static)" superClass="nds32le-elf-mculib-v3m.c.link.option.noshared.base" valueType="boolean"/>
								<option defaultValue="" id="nds32le-elf-mculib-v3m.c.link.exe.debug.option.ldscript.1208260456" name="Linker Script (-T)" superClass="nds32le-elf-mculib-v3m.c.link.exe.debug.option.ldscript" value="..\LinkerScript\ps5008_hw.ld" valueType="string"/>
								<option id="nds.c.linker.option.cmodel.75391811" name="Code Model" superClass="nds.c.linker.option.cmodel" value="-mcmodel=medium" valueType="string"/>
								<option id="nds.c.link.option.map.1648612423" name="Write a map file. (-Map)" superClass="nds.c.link.option.map" value="true" valueType="boolean"/>
								<option id="nds.c.link.option.virtualhosting.1035026169" name="Virtual Hosting (-mvh)" superClass="nds.c.link.option.virtualhosting" value="false" valueType="boolean"/>
								<option id="nds.c.link.option.nostart.1529098959" name="Do not use standard start files (-nostartfiles)" superClass="nds.c.link.option.nostart" value="true" valueType="boolean"/>
								<option id="nds.c.link.option.removesections.1419686307" name="Remove unused sections (-ffunction-sections or -fdata-sections)" superClass="nds.c.link.option.removesections" value="true" valueType="boolean"/>
								<inputType id="tool.nds.c.linker.input.793190521" superClass="tool.nds.c.linker.input">
									<additionalInput kind="additionalinputdependency" paths="$(USER_OBJS)"/>
									<additionalInput kind="additionalinput" paths="$(LIBS)"/>
								</inputType>
							</tool>
							<tool command="$(CROSS_COMPILE)as" commandLinePattern="${COMMAND} ${FLAGS} ${OUTPUT_FLAG} ${OUTPUT_PREFIX}${OUTPUT} ${INPUTS}" errorParsers="org.eclipse.cdt.core.GASErrorParser" id="tool.nds32le-elf-mculib-v3m.assembler.exe.debug.996343929" name="Andes Assembler" superClass="tool.nds32le-elf-mculib-v3m.assembler.exe.debug">
								<option defaultValue="-g" id="nds.both.asm.option.flags.989657183" name="Assembler flags" superClass="nds.both.asm.option.flags" valueType="string"/>
								<inputType id="tool.nds.assembler.input.2047276987" superClass="tool.nds.assembler.input"/>
							</tool>
							<tool command="$(CROSS_COMPILE)nm" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot; ${OUTPUT_FLAG} ${OUTPUT}" errorParsers="" id="tool.nds32le-elf-mculib-v3m.nm.exe.debug.225671708" name="NM (symbol listing)" superClass="tool.nds32le-elf-mculib-v3m.nm.exe.debug">
								<option id="nds.nm.option.sortsymbol.785953092" name="Sort symbols numerically by address. (-n)" superClass="nds.nm.option.sortsymbol" value="true" valueType="boolean"/>
								<option id="nds.nm.option.printfilename.1580101080" name="Print name of the input file before every symbol. (-A)" superClass="nds.nm.option.printfilename" value="false" valueType="boolean"/>
							</tool>
							<tool command="$(CROSS_COMPILE)readelf" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot; ${OUTPUT_FLAG} ${OUTPUT}" errorParsers="" id="tool.nds32le-elf-mculib-v3m.readelf.exe.debug.736943300" name="Readelf (ELF info listing)" superClass="tool.nds32le-elf-mculib-v3m.readelf.exe.debug"/>
							<tool command="$(CROSS_COMPILE)objdump" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot; ${OUTPUT_FLAG} ${OUTPUT}" errorParsers="" id="tool.nds32le-elf-mculib-v3m.objdump.exe.debug.590074084" name="Objdump (disassembly)" superClass="tool.nds32le-elf-mculib-v3m.objdump.exe.debug">
								<option id="nds.objdump.option.disable.1774848204" name="Disable. (Do not auto-generate output file.)" superClass="nds.objdump.option.disable" value="false" valueType="boolean"/>
								<option id="nds.objdump.option.linenumber.373421632" name="Include line numbers and filenames in output. (-l)" superClass="nds.objdump.option.linenumber" value="true" valueType="boolean"/>
								<option id="nds.objdump.option.otherflags.274265849" name="Other flags" superClass="nds.objdump.option.otherflags" value="-S" valueType="string"/>
							</tool>
							<tool command="$(CROSS_COMPILE)objcopy" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot; ${OUTPUT}" errorParsers="" id="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug.1988201274" name="Objcopy (object content copy)" superClass="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug">
								<option id="nds.objcopy.option.disable.1528457687" name="Disable. (Do not auto-generate output file.)" superClass="nds.objcopy.option.disable" value="false" valueType="boolean"/>
								<option id="nds.objcopy.option.stripall.852420494" name="Remove all symbol and relocation information. (-S)" superClass="nds.objcopy.option.stripall" value="false" valueType="boolean"/>
								<option id="nds.objcopy.option.otherflags.1648955884" name="Other flags" superClass="nds.objcopy.option.otherflags" value="-R .flh_iram_fpu.data" valueType="string"/>
							</tool>
							<tool command="$(CROSS_COMPILE)size" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot;" errorParsers="" id="tool.nds32le-elf-mculib-v3m.size.exe.debug.1658204008" name="Size (section size listing)" superClass="tool.nds32le-elf-mculib-v3m.size.exe.debug"/>
							<tool command="$(ANDESIGHT_ROOT)/utils/nds_ldsag" commandLinePattern="${COMMAND} ${FLAGS} ${INPUTS} -o $(LDSAG_OUT)" errorParsers="" id="tool.nds32le-elf-mculib-v3m.ldsag.base.292319909" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.base"/>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.394604773" name="Andes C++ Compiler" superClass="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug">
								<option id="nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.option.debugging.level.477347660" name="Debug Level" superClass="nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.option.debugging.level" value="nds.cpp.compiler.debugging.level.max" valueType="enumerated"/>
							</tool>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug.536552871" name="Andes C++ Linker" superClass="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug">
								<option defaultValue="-O0" id="nds.cpp.link.option.optimization.level.87335463" name="Optimization Level" superClass="nds.cpp.link.option.optimization.level" valueType="string"/>
								<option defaultValue="true" id="nds32le-elf-mculib-v3m.cpp.link.option.noshared.base.938610889" name="No shared libraries (-static)" superClass="nds32le-elf-mculib-v3m.cpp.link.option.noshared.base" valueType="boolean"/>
							</tool>
							<tool command="$(ANDESIGHT_ROOT)/utils/nds_ldsag" commandLinePattern="${COMMAND} ${FLAGS} ${INPUTS} -o $(LDSAG_OUT)" errorParsers="" id="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug.1321410508" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug"/>
						</toolChain>
					</folderInfo>
					<folderInfo id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.576423965.515984178.748773599.**********.opt_master_only" name="/" resourcePath="opt_master_only">
						<toolChain errorParsers="" id="nds.nds32le-elf-mculib-v3m.exe.debug.273596782" name="nds32le-elf-mculib-v3m" superClass="nds.nds32le-elf-mculib-v3m.exe.debug" unusedChildren="">
							<option id="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET.423936908.313979601.1887125436.1009016960.1709021572.205845821.1568783009.495878407.135551672" name="RSE_TARGET" superClass="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET.423936908"/>
							<targetPlatform id="target.nds32le-elf-mculib-v3m.platform.exe.debug.687092495" name="Debug Platform" superClass="target.nds32le-elf-mculib-v3m.platform.exe.debug"/>
							<tool id="tool.nds32le-elf-mculib-v3m.archiver.base.161819216" name="Andes Archiver" superClass="tool.nds32le-elf-mculib-v3m.archiver.base.175982725"/>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.1846746723" name="Andes C++ Compiler" superClass="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.394604773">
								<option id="nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.option.debugging.level.958703510" name="Debug Level" superClass="nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.option.debugging.level" value="nds.cpp.compiler.debugging.level.max" valueType="enumerated"/>
							</tool>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug.923711758" name="Andes C++ Linker" superClass="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug.536552871">
								<option defaultValue="-O0" id="nds.cpp.link.option.optimization.level.1743253190" name="Optimization Level" superClass="nds.cpp.link.option.optimization.level" valueType="string"/>
								<option defaultValue="true" id="nds32le-elf-mculib-v3m.cpp.link.option.noshared.base.1631406759" name="No shared libraries (-static)" superClass="nds32le-elf-mculib-v3m.cpp.link.option.noshared.base" valueType="boolean"/>
							</tool>
							<tool errorParsers="org.eclipse.cdt.core.GCCErrorParser" id="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.2019466957" name="Andes C Compiler" superClass="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.1221958212">
								<inputType id="tool.nds.c.compiler.input.1388619990" superClass="tool.nds.c.compiler.input"/>
							</tool>
							<tool id="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug.1524244376" name="Andes C Linker" superClass="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug.1821315089"/>
							<tool errorParsers="org.eclipse.cdt.core.GASErrorParser" id="tool.nds32le-elf-mculib-v3m.assembler.exe.debug.1834861814" name="Andes Assembler" superClass="tool.nds32le-elf-mculib-v3m.assembler.exe.debug.996343929">
								<inputType id="tool.nds.assembler.input.1941042733" superClass="tool.nds.assembler.input"/>
							</tool>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.nm.exe.debug.689616908" name="NM (symbol listing)" superClass="tool.nds32le-elf-mculib-v3m.nm.exe.debug.225671708"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.readelf.exe.debug.1591185066" name="Readelf (ELF info listing)" superClass="tool.nds32le-elf-mculib-v3m.readelf.exe.debug.736943300"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.objdump.exe.debug.752600925" name="Objdump (disassembly)" superClass="tool.nds32le-elf-mculib-v3m.objdump.exe.debug.590074084"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug.1555697764" name="Objcopy (object content copy)" superClass="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug.1988201274"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.size.exe.debug.1566028830" name="Size (section size listing)" superClass="tool.nds32le-elf-mculib-v3m.size.exe.debug.1658204008"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug.747343871" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug.1321410508"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.ldsag.base.1361482981" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.base.292319909"/>
						</toolChain>
					</folderInfo>
					<sourceEntries>
						<entry excluding="opt_master_only|Src_KIC|shr/inc/shr_types.h" flags="VALUE_WORKSPACE_PATH" kind="sourcePath" name=""/>
					</sourceEntries>
				</configuration>
			</storageModule>
			<storageModule moduleId="org.eclipse.cdt.core.externalSettings"/>
		</cconfiguration>
		<cconfiguration id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.576423965.**********">
			<storageModule buildSystemId="org.eclipse.cdt.managedbuilder.core.configurationDataProvider" id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.576423965.**********" moduleId="org.eclipse.cdt.core.settings" name="PS5017_Samsung_V5">
				<externalSettings/>
				<extensions>
					<extension id="com.andestech.ide.cdt.managedbuilder.core.CROSS_GNU_ELF" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="org.eclipse.cdt.core.GmakeErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.CWDLocator" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GCCErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GASErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GLDErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
				</extensions>
			</storageModule>
			<storageModule moduleId="cdtBuildSystem" version="4.0.0">
				<configuration artifactExtension="adx" artifactName="${ProjName}" buildArtefactType="nds.buildArtefactType.exe" buildProperties="org.eclipse.cdt.build.core.buildType=org.eclipse.cdt.build.core.buildType.debug,org.eclipse.cdt.build.core.buildArtefactType=nds.buildArtefactType.exe" description="" errorParsers="org.eclipse.cdt.core.GmakeErrorParser;org.eclipse.cdt.core.CWDLocator;org.eclipse.cdt.core.GCCErrorParser;org.eclipse.cdt.core.GASErrorParser;org.eclipse.cdt.core.GLDErrorParser" id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.576423965.**********" name="PS5017_Samsung_V5" parent="config.nds32le-elf-mculib-v3m.exe.debug" postannouncebuildStep="" postbuildStep="" preannouncebuildStep="" prebuildStep="">
					<folderInfo id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.576423965.**********." name="/" resourcePath="">
						<toolChain errorParsers="" id="nds.nds32le-elf-mculib-v3m.exe.debug.223698307" name="nds32le-elf-mculib-v3m" superClass="nds.nds32le-elf-mculib-v3m.exe.debug">
							<option id="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET.1852782849" name="RSE_TARGET" superClass="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET" value="rse://LOCALHOST/~toolchains/nds32le-elf-mculib-v3m" valueType="string"/>
							<targetPlatform binaryParser="com.andestech.ide.cdt.managedbuilder.core.CROSS_GNU_ELF" id="target.nds32le-elf-mculib-v3m.platform.exe.debug.921989782" name="Debug Platform" superClass="target.nds32le-elf-mculib-v3m.platform.exe.debug"/>
							<builder buildPath="${workspace_loc:/ps5008_opt/Debug}" errorParsers="org.eclipse.cdt.core.GmakeErrorParser;org.eclipse.cdt.core.CWDLocator" id="target.nds32le-elf-mculib-v3m.builder.exe.debug.565817486" keepEnvironmentInBuildfile="false" managedBuildOn="true" name="Andes Make Builder" parallelBuildOn="false" parallelizationNumber="-1" superClass="target.nds32le-elf-mculib-v3m.builder.exe.debug"/>
							<tool id="tool.nds32le-elf-mculib-v3m.archiver.base.196191950" name="Andes Archiver" superClass="tool.nds32le-elf-mculib-v3m.archiver.base"/>
							<tool command="$(CROSS_COMPILE)gcc" commandLinePattern="${COMMAND} ${FLAGS} ${OUTPUT_FLAG} ${OUTPUT_PREFIX}${OUTPUT} ${INPUTS}" errorParsers="org.eclipse.cdt.core.GCCErrorParser" id="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.1696366255" name="Andes C Compiler" superClass="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug">
								<option id="nds32le-elf-mculib-v3m.c.compiler.exe.debug.option.debugging.level.1846195697" name="Debug Level" superClass="nds32le-elf-mculib-v3m.c.compiler.exe.debug.option.debugging.level" value="nds.c.debugging.level.max" valueType="enumerated"/>
								<option id="nds.c.compiler.option.include.paths.1054148958" name="Include paths (-I)" superClass="nds.c.compiler.option.include.paths" valueType="includePath">
									<listOptionValue builtIn="false" value="&quot;..\opt\inc&quot;"/>
									<listOptionValue builtIn="false" value="&quot;..\opt&quot;"/>
									<listOptionValue builtIn="false" value="&quot;..\shr\inc&quot;"/>
									<listOptionValue builtIn="false" value="&quot;..\startup\inc&quot;"/>
								</option>
								<option id="nds32le-elf-mculib-v3m.c.compiler.exe.debug.option.optimization.level.870996092" name="Optimization Level" superClass="nds32le-elf-mculib-v3m.c.compiler.exe.debug.option.optimization.level" value="nds.c.optimization.level.more" valueType="enumerated"/>
								<option id="nds.c.compiler.option.optimization.removesections.function.858026165" name="Remove unused function sections (-ffunction-sections)" superClass="nds.c.compiler.option.optimization.removesections.function" value="true" valueType="boolean"/>
								<option id="nds.c.compiler.option.optimization.removesections.data.1633291032" name="Remove unused data sections (-fdata-sections)" superClass="nds.c.compiler.option.optimization.removesections.data" value="true" valueType="boolean"/>
								<option id="nds.c.compiler.option.cmodel.371058380" name="Code Model" superClass="nds.c.compiler.option.cmodel" value="nds.c.compiler.option.cmodel.medium" valueType="enumerated"/>
								<option id="nds.c.compiler.option.preprocessor.def.symbols.2031281082" name="Defined symbols (-D)" superClass="nds.c.compiler.option.preprocessor.def.symbols" valueType="definedSymbols">
									<listOptionValue builtIn="false" value="CATEGORY_CUSTOMER=CUSTOMER_SAMSUNG"/>
									<listOptionValue builtIn="false" value="CATEGORY_FLASH=FLASH_SAMSUNG_V5_TLC"/>
									<listOptionValue builtIn="false" value="CATEGORY_CONTROLLER=CONTROLLER_PS5017"/>
								</option>
								<inputType id="tool.nds.c.compiler.input.226745235" superClass="tool.nds.c.compiler.input"/>
							</tool>
							<tool command="$(CROSS_COMPILE)gcc" commandLinePattern="${COMMAND} ${FLAGS} ${OUTPUT_FLAG} ${OUTPUT_PREFIX}${OUTPUT} ${INPUTS}" errorParsers="org.eclipse.cdt.core.GLDErrorParser" id="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug.1911047442" name="Andes C Linker" superClass="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug">
								<option defaultValue="-O0" id="nds.c.link.option.optimization.level.710692555" name="Optimization Level" superClass="nds.c.link.option.optimization.level" value="-O2" valueType="string"/>
								<option defaultValue="true" id="nds32le-elf-mculib-v3m.c.link.option.noshared.base.1446664684" name="No shared libraries (-static)" superClass="nds32le-elf-mculib-v3m.c.link.option.noshared.base" valueType="boolean"/>
								<option defaultValue="" id="nds32le-elf-mculib-v3m.c.link.exe.debug.option.ldscript.1609063786" name="Linker Script (-T)" superClass="nds32le-elf-mculib-v3m.c.link.exe.debug.option.ldscript" value="..\LinkerScript\ps5008_hw.ld" valueType="string"/>
								<option id="nds.c.linker.option.cmodel.738294231" name="Code Model" superClass="nds.c.linker.option.cmodel" value="-mcmodel=medium" valueType="string"/>
								<option id="nds.c.link.option.map.1386609647" name="Write a map file. (-Map)" superClass="nds.c.link.option.map" value="true" valueType="boolean"/>
								<option id="nds.c.link.option.virtualhosting.1947624005" name="Virtual Hosting (-mvh)" superClass="nds.c.link.option.virtualhosting" value="false" valueType="boolean"/>
								<option id="nds.c.link.option.nostart.357576895" name="Do not use standard start files (-nostartfiles)" superClass="nds.c.link.option.nostart" value="true" valueType="boolean"/>
								<option id="nds.c.link.option.removesections.322012861" name="Remove unused sections (-ffunction-sections or -fdata-sections)" superClass="nds.c.link.option.removesections" value="true" valueType="boolean"/>
								<inputType id="tool.nds.c.linker.input.388866404" superClass="tool.nds.c.linker.input">
									<additionalInput kind="additionalinputdependency" paths="$(USER_OBJS)"/>
									<additionalInput kind="additionalinput" paths="$(LIBS)"/>
								</inputType>
							</tool>
							<tool command="$(CROSS_COMPILE)as" commandLinePattern="${COMMAND} ${FLAGS} ${OUTPUT_FLAG} ${OUTPUT_PREFIX}${OUTPUT} ${INPUTS}" errorParsers="org.eclipse.cdt.core.GASErrorParser" id="tool.nds32le-elf-mculib-v3m.assembler.exe.debug.716018392" name="Andes Assembler" superClass="tool.nds32le-elf-mculib-v3m.assembler.exe.debug">
								<option defaultValue="-g" id="nds.both.asm.option.flags.781309809" name="Assembler flags" superClass="nds.both.asm.option.flags" valueType="string"/>
								<inputType id="tool.nds.assembler.input.1892615213" superClass="tool.nds.assembler.input"/>
							</tool>
							<tool command="$(CROSS_COMPILE)nm" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot; ${OUTPUT_FLAG} ${OUTPUT}" errorParsers="" id="tool.nds32le-elf-mculib-v3m.nm.exe.debug.1596863799" name="NM (symbol listing)" superClass="tool.nds32le-elf-mculib-v3m.nm.exe.debug">
								<option id="nds.nm.option.sortsymbol.1011793598" name="Sort symbols numerically by address. (-n)" superClass="nds.nm.option.sortsymbol" value="true" valueType="boolean"/>
								<option id="nds.nm.option.printfilename.1161535261" name="Print name of the input file before every symbol. (-A)" superClass="nds.nm.option.printfilename" value="false" valueType="boolean"/>
							</tool>
							<tool command="$(CROSS_COMPILE)readelf" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot; ${OUTPUT_FLAG} ${OUTPUT}" errorParsers="" id="tool.nds32le-elf-mculib-v3m.readelf.exe.debug.1088478358" name="Readelf (ELF info listing)" superClass="tool.nds32le-elf-mculib-v3m.readelf.exe.debug"/>
							<tool command="$(CROSS_COMPILE)objdump" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot; ${OUTPUT_FLAG} ${OUTPUT}" errorParsers="" id="tool.nds32le-elf-mculib-v3m.objdump.exe.debug.1878054112" name="Objdump (disassembly)" superClass="tool.nds32le-elf-mculib-v3m.objdump.exe.debug">
								<option id="nds.objdump.option.disable.1198833251" name="Disable. (Do not auto-generate output file.)" superClass="nds.objdump.option.disable" value="false" valueType="boolean"/>
								<option id="nds.objdump.option.linenumber.1431359548" name="Include line numbers and filenames in output. (-l)" superClass="nds.objdump.option.linenumber" value="true" valueType="boolean"/>
								<option id="nds.objdump.option.otherflags.1344897577" name="Other flags" superClass="nds.objdump.option.otherflags" value="-S" valueType="string"/>
							</tool>
							<tool command="$(CROSS_COMPILE)objcopy" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot; ${OUTPUT}" errorParsers="" id="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug.1364672153" name="Objcopy (object content copy)" superClass="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug">
								<option id="nds.objcopy.option.disable.1482781217" name="Disable. (Do not auto-generate output file.)" superClass="nds.objcopy.option.disable" value="false" valueType="boolean"/>
								<option id="nds.objcopy.option.stripall.103497576" name="Remove all symbol and relocation information. (-S)" superClass="nds.objcopy.option.stripall" value="false" valueType="boolean"/>
								<option id="nds.objcopy.option.otherflags.1289308810" name="Other flags" superClass="nds.objcopy.option.otherflags" value="-R .flh_iram_fpu.data" valueType="string"/>
							</tool>
							<tool command="$(CROSS_COMPILE)size" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot;" errorParsers="" id="tool.nds32le-elf-mculib-v3m.size.exe.debug.408735846" name="Size (section size listing)" superClass="tool.nds32le-elf-mculib-v3m.size.exe.debug"/>
							<tool command="$(ANDESIGHT_ROOT)/utils/nds_ldsag" commandLinePattern="${COMMAND} ${FLAGS} ${INPUTS} -o $(LDSAG_OUT)" errorParsers="" id="tool.nds32le-elf-mculib-v3m.ldsag.base.2114889173" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.base"/>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.841549002" name="Andes C++ Compiler" superClass="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug">
								<option id="nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.option.debugging.level.2028687088" name="Debug Level" superClass="nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.option.debugging.level" value="nds.cpp.compiler.debugging.level.max" valueType="enumerated"/>
							</tool>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug.1741299634" name="Andes C++ Linker" superClass="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug">
								<option defaultValue="-O0" id="nds.cpp.link.option.optimization.level.861736850" name="Optimization Level" superClass="nds.cpp.link.option.optimization.level" valueType="string"/>
								<option defaultValue="true" id="nds32le-elf-mculib-v3m.cpp.link.option.noshared.base.337336176" name="No shared libraries (-static)" superClass="nds32le-elf-mculib-v3m.cpp.link.option.noshared.base" valueType="boolean"/>
							</tool>
							<tool command="$(ANDESIGHT_ROOT)/utils/nds_ldsag" commandLinePattern="${COMMAND} ${FLAGS} ${INPUTS} -o $(LDSAG_OUT)" errorParsers="" id="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug.212244929" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug"/>
						</toolChain>
					</folderInfo>
					<folderInfo id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.576423965.**********.opt_master_only" name="/" resourcePath="opt_master_only">
						<toolChain errorParsers="" id="nds.nds32le-elf-mculib-v3m.exe.debug.1912594385" name="nds32le-elf-mculib-v3m" superClass="nds.nds32le-elf-mculib-v3m.exe.debug" unusedChildren="">
							<option id="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET.423936908.313979601.1887125436.1009016960.1709021572.205845821.1433969431" name="RSE_TARGET" superClass="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET.423936908"/>
							<targetPlatform id="target.nds32le-elf-mculib-v3m.platform.exe.debug.1077284324" name="Debug Platform" superClass="target.nds32le-elf-mculib-v3m.platform.exe.debug"/>
							<tool id="tool.nds32le-elf-mculib-v3m.archiver.base.558062348" name="Andes Archiver" superClass="tool.nds32le-elf-mculib-v3m.archiver.base.196191950"/>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.1681422408" name="Andes C++ Compiler" superClass="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.841549002">
								<option id="nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.option.debugging.level.1191471440" name="Debug Level" superClass="nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.option.debugging.level" value="nds.cpp.compiler.debugging.level.max" valueType="enumerated"/>
							</tool>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug.1637878674" name="Andes C++ Linker" superClass="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug.1741299634">
								<option defaultValue="-O0" id="nds.cpp.link.option.optimization.level.1877097521" name="Optimization Level" superClass="nds.cpp.link.option.optimization.level" valueType="string"/>
								<option defaultValue="true" id="nds32le-elf-mculib-v3m.cpp.link.option.noshared.base.376003410" name="No shared libraries (-static)" superClass="nds32le-elf-mculib-v3m.cpp.link.option.noshared.base" valueType="boolean"/>
							</tool>
							<tool errorParsers="org.eclipse.cdt.core.GCCErrorParser" id="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.787255908" name="Andes C Compiler" superClass="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.1696366255">
								<inputType id="tool.nds.c.compiler.input.1588469494" superClass="tool.nds.c.compiler.input"/>
							</tool>
							<tool id="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug.1007694073" name="Andes C Linker" superClass="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug.1911047442"/>
							<tool errorParsers="org.eclipse.cdt.core.GASErrorParser" id="tool.nds32le-elf-mculib-v3m.assembler.exe.debug.531113027" name="Andes Assembler" superClass="tool.nds32le-elf-mculib-v3m.assembler.exe.debug.716018392">
								<inputType id="tool.nds.assembler.input.676942605" superClass="tool.nds.assembler.input"/>
							</tool>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.nm.exe.debug.504753289" name="NM (symbol listing)" superClass="tool.nds32le-elf-mculib-v3m.nm.exe.debug.1596863799"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.readelf.exe.debug.796167121" name="Readelf (ELF info listing)" superClass="tool.nds32le-elf-mculib-v3m.readelf.exe.debug.1088478358"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.objdump.exe.debug.1745602945" name="Objdump (disassembly)" superClass="tool.nds32le-elf-mculib-v3m.objdump.exe.debug.1878054112"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug.1993966908" name="Objcopy (object content copy)" superClass="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug.1364672153"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.size.exe.debug.773748720" name="Size (section size listing)" superClass="tool.nds32le-elf-mculib-v3m.size.exe.debug.408735846"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug.43434969" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug.212244929"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.ldsag.base.1729839457" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.base.2114889173"/>
						</toolChain>
					</folderInfo>
					<sourceEntries>
						<entry excluding="opt_master_only|Src_KIC|shr/inc/shr_types.h" flags="VALUE_WORKSPACE_PATH" kind="sourcePath" name=""/>
					</sourceEntries>
				</configuration>
			</storageModule>
			<storageModule moduleId="org.eclipse.cdt.core.externalSettings"/>
		</cconfiguration>
		<cconfiguration id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********.90222265">
			<storageModule buildSystemId="org.eclipse.cdt.managedbuilder.core.configurationDataProvider" id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********.90222265" moduleId="org.eclipse.cdt.core.settings" name="PS5017_Micron_N48R">
				<externalSettings/>
				<extensions>
					<extension id="com.andestech.ide.cdt.managedbuilder.core.CROSS_GNU_ELF" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="org.eclipse.cdt.core.GmakeErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.CWDLocator" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GCCErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GASErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GLDErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
				</extensions>
			</storageModule>
			<storageModule moduleId="cdtBuildSystem" version="4.0.0">
				<configuration artifactExtension="adx" artifactName="${ProjName}" buildArtefactType="nds.buildArtefactType.exe" buildProperties="org.eclipse.cdt.build.core.buildType=org.eclipse.cdt.build.core.buildType.debug,org.eclipse.cdt.build.core.buildArtefactType=nds.buildArtefactType.exe" description="" errorParsers="org.eclipse.cdt.core.GmakeErrorParser;org.eclipse.cdt.core.CWDLocator;org.eclipse.cdt.core.GCCErrorParser;org.eclipse.cdt.core.GASErrorParser;org.eclipse.cdt.core.GLDErrorParser" id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********.90222265" name="PS5017_Micron_N48R" parent="config.nds32le-elf-mculib-v3m.exe.debug" postannouncebuildStep="" postbuildStep="" preannouncebuildStep="" prebuildStep="">
					<folderInfo id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********.90222265." name="/" resourcePath="">
						<toolChain errorParsers="" id="nds.nds32le-elf-mculib-v3m.exe.debug.1035442805" name="nds32le-elf-mculib-v3m" superClass="nds.nds32le-elf-mculib-v3m.exe.debug">
							<option id="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET.1532198072" name="RSE_TARGET" superClass="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET" value="rse://LOCALHOST/~toolchains/nds32le-elf-mculib-v3m" valueType="string"/>
							<targetPlatform binaryParser="com.andestech.ide.cdt.managedbuilder.core.CROSS_GNU_ELF" id="target.nds32le-elf-mculib-v3m.platform.exe.debug.650848821" name="Debug Platform" superClass="target.nds32le-elf-mculib-v3m.platform.exe.debug"/>
							<builder buildPath="${workspace_loc:/ps5008_opt/Debug}" errorParsers="org.eclipse.cdt.core.GmakeErrorParser;org.eclipse.cdt.core.CWDLocator" id="target.nds32le-elf-mculib-v3m.builder.exe.debug.814729524" keepEnvironmentInBuildfile="false" managedBuildOn="true" name="Andes Make Builder" parallelBuildOn="false" parallelizationNumber="-1" superClass="target.nds32le-elf-mculib-v3m.builder.exe.debug"/>
							<tool id="tool.nds32le-elf-mculib-v3m.archiver.base.1229490694" name="Andes Archiver" superClass="tool.nds32le-elf-mculib-v3m.archiver.base"/>
							<tool command="$(CROSS_COMPILE)gcc" commandLinePattern="${COMMAND} ${FLAGS} ${OUTPUT_FLAG} ${OUTPUT_PREFIX}${OUTPUT} ${INPUTS}" errorParsers="org.eclipse.cdt.core.GCCErrorParser" id="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.1007303811" name="Andes C Compiler" superClass="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug">
								<option id="nds32le-elf-mculib-v3m.c.compiler.exe.debug.option.debugging.level.1782502319" name="Debug Level" superClass="nds32le-elf-mculib-v3m.c.compiler.exe.debug.option.debugging.level" value="nds.c.debugging.level.max" valueType="enumerated"/>
								<option id="nds.c.compiler.option.include.paths.484200298" name="Include paths (-I)" superClass="nds.c.compiler.option.include.paths" valueType="includePath">
									<listOptionValue builtIn="false" value="&quot;..\opt\inc&quot;"/>
									<listOptionValue builtIn="false" value="&quot;..\opt&quot;"/>
									<listOptionValue builtIn="false" value="&quot;..\shr\inc&quot;"/>
									<listOptionValue builtIn="false" value="&quot;..\startup\inc&quot;"/>
								</option>
								<option id="nds32le-elf-mculib-v3m.c.compiler.exe.debug.option.optimization.level.418722114" name="Optimization Level" superClass="nds32le-elf-mculib-v3m.c.compiler.exe.debug.option.optimization.level" value="nds.c.optimization.level.more" valueType="enumerated"/>
								<option id="nds.c.compiler.option.optimization.removesections.function.338885648" name="Remove unused function sections (-ffunction-sections)" superClass="nds.c.compiler.option.optimization.removesections.function" value="true" valueType="boolean"/>
								<option id="nds.c.compiler.option.optimization.removesections.data.1580487127" name="Remove unused data sections (-fdata-sections)" superClass="nds.c.compiler.option.optimization.removesections.data" value="true" valueType="boolean"/>
								<option id="nds.c.compiler.option.cmodel.100545238" name="Code Model" superClass="nds.c.compiler.option.cmodel" value="nds.c.compiler.option.cmodel.medium" valueType="enumerated"/>
								<option id="nds.c.compiler.option.preprocessor.def.symbols.237335650" name="Defined symbols (-D)" superClass="nds.c.compiler.option.preprocessor.def.symbols" valueType="definedSymbols">
									<listOptionValue builtIn="false" value="CATEGORY_CUSTOMER=CUSTOMER_MICRON"/>
									<listOptionValue builtIn="false" value="CATEGORY_FLASH=FLASH_N48R_QLC"/>
									<listOptionValue builtIn="false" value="CATEGORY_CONTROLLER=CONTROLLER_PS5017"/>
								</option>
								<inputType id="tool.nds.c.compiler.input.1753702568" superClass="tool.nds.c.compiler.input"/>
							</tool>
							<tool command="$(CROSS_COMPILE)gcc" commandLinePattern="${COMMAND} ${FLAGS} ${OUTPUT_FLAG} ${OUTPUT_PREFIX}${OUTPUT} ${INPUTS}" errorParsers="org.eclipse.cdt.core.GLDErrorParser" id="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug.254707323" name="Andes C Linker" superClass="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug">
								<option defaultValue="-O0" id="nds.c.link.option.optimization.level.1241706895" name="Optimization Level" superClass="nds.c.link.option.optimization.level" value="-O2" valueType="string"/>
								<option defaultValue="true" id="nds32le-elf-mculib-v3m.c.link.option.noshared.base.1392082977" name="No shared libraries (-static)" superClass="nds32le-elf-mculib-v3m.c.link.option.noshared.base" valueType="boolean"/>
								<option defaultValue="" id="nds32le-elf-mculib-v3m.c.link.exe.debug.option.ldscript.1923341204" name="Linker Script (-T)" superClass="nds32le-elf-mculib-v3m.c.link.exe.debug.option.ldscript" value="..\LinkerScript\ps5008_hw.ld" valueType="string"/>
								<option id="nds.c.linker.option.cmodel.19934643" name="Code Model" superClass="nds.c.linker.option.cmodel" value="-mcmodel=medium" valueType="string"/>
								<option id="nds.c.link.option.map.570923570" name="Write a map file. (-Map)" superClass="nds.c.link.option.map" value="true" valueType="boolean"/>
								<option id="nds.c.link.option.virtualhosting.1919623716" name="Virtual Hosting (-mvh)" superClass="nds.c.link.option.virtualhosting" value="false" valueType="boolean"/>
								<option id="nds.c.link.option.nostart.485165062" name="Do not use standard start files (-nostartfiles)" superClass="nds.c.link.option.nostart" value="true" valueType="boolean"/>
								<option id="nds.c.link.option.removesections.2108385974" name="Remove unused sections (-ffunction-sections or -fdata-sections)" superClass="nds.c.link.option.removesections" value="true" valueType="boolean"/>
								<inputType id="tool.nds.c.linker.input.601810593" superClass="tool.nds.c.linker.input">
									<additionalInput kind="additionalinputdependency" paths="$(USER_OBJS)"/>
									<additionalInput kind="additionalinput" paths="$(LIBS)"/>
								</inputType>
							</tool>
							<tool command="$(CROSS_COMPILE)as" commandLinePattern="${COMMAND} ${FLAGS} ${OUTPUT_FLAG} ${OUTPUT_PREFIX}${OUTPUT} ${INPUTS}" errorParsers="org.eclipse.cdt.core.GASErrorParser" id="tool.nds32le-elf-mculib-v3m.assembler.exe.debug.798079577" name="Andes Assembler" superClass="tool.nds32le-elf-mculib-v3m.assembler.exe.debug">
								<option defaultValue="-g" id="nds.both.asm.option.flags.1226406242" name="Assembler flags" superClass="nds.both.asm.option.flags" valueType="string"/>
								<inputType id="tool.nds.assembler.input.162266516" superClass="tool.nds.assembler.input"/>
							</tool>
							<tool command="$(CROSS_COMPILE)nm" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot; ${OUTPUT_FLAG} ${OUTPUT}" errorParsers="" id="tool.nds32le-elf-mculib-v3m.nm.exe.debug.1886335830" name="NM (symbol listing)" superClass="tool.nds32le-elf-mculib-v3m.nm.exe.debug">
								<option id="nds.nm.option.sortsymbol.1016931315" name="Sort symbols numerically by address. (-n)" superClass="nds.nm.option.sortsymbol" value="true" valueType="boolean"/>
								<option id="nds.nm.option.printfilename.650778350" name="Print name of the input file before every symbol. (-A)" superClass="nds.nm.option.printfilename" value="false" valueType="boolean"/>
							</tool>
							<tool command="$(CROSS_COMPILE)readelf" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot; ${OUTPUT_FLAG} ${OUTPUT}" errorParsers="" id="tool.nds32le-elf-mculib-v3m.readelf.exe.debug.382889690" name="Readelf (ELF info listing)" superClass="tool.nds32le-elf-mculib-v3m.readelf.exe.debug"/>
							<tool command="$(CROSS_COMPILE)objdump" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot; ${OUTPUT_FLAG} ${OUTPUT}" errorParsers="" id="tool.nds32le-elf-mculib-v3m.objdump.exe.debug.1731656013" name="Objdump (disassembly)" superClass="tool.nds32le-elf-mculib-v3m.objdump.exe.debug">
								<option id="nds.objdump.option.disable.921111217" name="Disable. (Do not auto-generate output file.)" superClass="nds.objdump.option.disable" value="false" valueType="boolean"/>
								<option id="nds.objdump.option.linenumber.531675204" name="Include line numbers and filenames in output. (-l)" superClass="nds.objdump.option.linenumber" value="true" valueType="boolean"/>
								<option id="nds.objdump.option.otherflags.1806799889" name="Other flags" superClass="nds.objdump.option.otherflags" value="-S" valueType="string"/>
							</tool>
							<tool command="$(CROSS_COMPILE)objcopy" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot; ${OUTPUT}" errorParsers="" id="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug.1089619695" name="Objcopy (object content copy)" superClass="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug">
								<option id="nds.objcopy.option.disable.1859105686" name="Disable. (Do not auto-generate output file.)" superClass="nds.objcopy.option.disable" value="false" valueType="boolean"/>
								<option id="nds.objcopy.option.stripall.2006045257" name="Remove all symbol and relocation information. (-S)" superClass="nds.objcopy.option.stripall" value="false" valueType="boolean"/>
								<option id="nds.objcopy.option.otherflags.1624638636" name="Other flags" superClass="nds.objcopy.option.otherflags" value="-R .flh_iram_fpu.data" valueType="string"/>
							</tool>
							<tool command="$(CROSS_COMPILE)size" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot;" errorParsers="" id="tool.nds32le-elf-mculib-v3m.size.exe.debug.786309388" name="Size (section size listing)" superClass="tool.nds32le-elf-mculib-v3m.size.exe.debug"/>
							<tool command="$(ANDESIGHT_ROOT)/utils/nds_ldsag" commandLinePattern="${COMMAND} ${FLAGS} ${INPUTS} -o $(LDSAG_OUT)" errorParsers="" id="tool.nds32le-elf-mculib-v3m.ldsag.base.1580433417" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.base"/>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.810049210" name="Andes C++ Compiler" superClass="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug">
								<option id="nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.option.debugging.level.1244006394" name="Debug Level" superClass="nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.option.debugging.level" value="nds.cpp.compiler.debugging.level.max" valueType="enumerated"/>
							</tool>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug.10630005" name="Andes C++ Linker" superClass="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug">
								<option defaultValue="-O0" id="nds.cpp.link.option.optimization.level.766164164" name="Optimization Level" superClass="nds.cpp.link.option.optimization.level" valueType="string"/>
								<option defaultValue="true" id="nds32le-elf-mculib-v3m.cpp.link.option.noshared.base.1358714070" name="No shared libraries (-static)" superClass="nds32le-elf-mculib-v3m.cpp.link.option.noshared.base" valueType="boolean"/>
							</tool>
							<tool command="$(ANDESIGHT_ROOT)/utils/nds_ldsag" commandLinePattern="${COMMAND} ${FLAGS} ${INPUTS} -o $(LDSAG_OUT)" errorParsers="" id="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug.2125376547" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug"/>
						</toolChain>
					</folderInfo>
					<folderInfo id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********.90222265.opt_master_only" name="/" resourcePath="opt_master_only">
						<toolChain errorParsers="" id="nds.nds32le-elf-mculib-v3m.exe.debug.662060998" name="nds32le-elf-mculib-v3m" superClass="nds.nds32le-elf-mculib-v3m.exe.debug" unusedChildren="">
							<option id="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET.423936908.313979601.1887125436.1009016960.1709021572.8634972.1680738685" name="RSE_TARGET" superClass="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET.423936908"/>
							<targetPlatform id="target.nds32le-elf-mculib-v3m.platform.exe.debug.1509554523" name="Debug Platform" superClass="target.nds32le-elf-mculib-v3m.platform.exe.debug"/>
							<tool id="tool.nds32le-elf-mculib-v3m.archiver.base.522747441" name="Andes Archiver" superClass="tool.nds32le-elf-mculib-v3m.archiver.base.1229490694"/>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.564719192" name="Andes C++ Compiler" superClass="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.810049210">
								<option id="nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.option.debugging.level.432076220" name="Debug Level" superClass="nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.option.debugging.level" value="nds.cpp.compiler.debugging.level.max" valueType="enumerated"/>
							</tool>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug.419603261" name="Andes C++ Linker" superClass="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug.10630005">
								<option defaultValue="-O0" id="nds.cpp.link.option.optimization.level.637024007" name="Optimization Level" superClass="nds.cpp.link.option.optimization.level" valueType="string"/>
								<option defaultValue="true" id="nds32le-elf-mculib-v3m.cpp.link.option.noshared.base.2111162400" name="No shared libraries (-static)" superClass="nds32le-elf-mculib-v3m.cpp.link.option.noshared.base" valueType="boolean"/>
							</tool>
							<tool errorParsers="org.eclipse.cdt.core.GCCErrorParser" id="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.1924301718" name="Andes C Compiler" superClass="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.1007303811">
								<inputType id="tool.nds.c.compiler.input.423490947" superClass="tool.nds.c.compiler.input"/>
							</tool>
							<tool id="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug.1143071640" name="Andes C Linker" superClass="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug.254707323"/>
							<tool errorParsers="org.eclipse.cdt.core.GASErrorParser" id="tool.nds32le-elf-mculib-v3m.assembler.exe.debug.697179898" name="Andes Assembler" superClass="tool.nds32le-elf-mculib-v3m.assembler.exe.debug.798079577">
								<inputType id="tool.nds.assembler.input.1511603916" superClass="tool.nds.assembler.input"/>
							</tool>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.nm.exe.debug.708854328" name="NM (symbol listing)" superClass="tool.nds32le-elf-mculib-v3m.nm.exe.debug.1886335830"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.readelf.exe.debug.618699782" name="Readelf (ELF info listing)" superClass="tool.nds32le-elf-mculib-v3m.readelf.exe.debug.382889690"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.objdump.exe.debug.870229002" name="Objdump (disassembly)" superClass="tool.nds32le-elf-mculib-v3m.objdump.exe.debug.1731656013"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug.1327311842" name="Objcopy (object content copy)" superClass="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug.1089619695"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.size.exe.debug.1607411083" name="Size (section size listing)" superClass="tool.nds32le-elf-mculib-v3m.size.exe.debug.786309388"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug.1741020847" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug.2125376547"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.ldsag.base.345547674" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.base.1580433417"/>
						</toolChain>
					</folderInfo>
					<sourceEntries>
						<entry excluding="opt_master_only|Src_KIC|shr/inc/shr_types.h" flags="VALUE_WORKSPACE_PATH" kind="sourcePath" name=""/>
					</sourceEntries>
				</configuration>
			</storageModule>
			<storageModule moduleId="org.eclipse.cdt.core.externalSettings"/>
		</cconfiguration>
		<cconfiguration id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********">
			<storageModule buildSystemId="org.eclipse.cdt.managedbuilder.core.configurationDataProvider" id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********" moduleId="org.eclipse.cdt.core.settings" name="PS5017_Intel_N38A">
				<externalSettings/>
				<extensions>
					<extension id="com.andestech.ide.cdt.managedbuilder.core.CROSS_GNU_ELF" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="org.eclipse.cdt.core.GmakeErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.CWDLocator" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GCCErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GASErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GLDErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
				</extensions>
			</storageModule>
			<storageModule moduleId="cdtBuildSystem" version="4.0.0">
				<configuration artifactExtension="adx" artifactName="${ProjName}" buildArtefactType="nds.buildArtefactType.exe" buildProperties="org.eclipse.cdt.build.core.buildType=org.eclipse.cdt.build.core.buildType.debug,org.eclipse.cdt.build.core.buildArtefactType=nds.buildArtefactType.exe" description="" errorParsers="org.eclipse.cdt.core.GmakeErrorParser;org.eclipse.cdt.core.CWDLocator;org.eclipse.cdt.core.GCCErrorParser;org.eclipse.cdt.core.GASErrorParser;org.eclipse.cdt.core.GLDErrorParser" id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********" name="PS5017_Intel_N38A" parent="config.nds32le-elf-mculib-v3m.exe.debug" postannouncebuildStep="" postbuildStep="" preannouncebuildStep="" prebuildStep="">
					<folderInfo id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********." name="/" resourcePath="">
						<toolChain errorParsers="" id="nds.nds32le-elf-mculib-v3m.exe.debug.430495590" name="nds32le-elf-mculib-v3m" superClass="nds.nds32le-elf-mculib-v3m.exe.debug">
							<option id="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET.982482784" name="RSE_TARGET" superClass="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET" value="rse://LOCALHOST/~toolchains/nds32le-elf-mculib-v3m" valueType="string"/>
							<targetPlatform binaryParser="com.andestech.ide.cdt.managedbuilder.core.CROSS_GNU_ELF" id="target.nds32le-elf-mculib-v3m.platform.exe.debug.1284871886" name="Debug Platform" superClass="target.nds32le-elf-mculib-v3m.platform.exe.debug"/>
							<builder buildPath="${workspace_loc:/ps5008_opt/Debug}" errorParsers="org.eclipse.cdt.core.GmakeErrorParser;org.eclipse.cdt.core.CWDLocator" id="target.nds32le-elf-mculib-v3m.builder.exe.debug.1503252362" keepEnvironmentInBuildfile="false" managedBuildOn="true" name="Andes Make Builder" parallelBuildOn="false" parallelizationNumber="-1" superClass="target.nds32le-elf-mculib-v3m.builder.exe.debug"/>
							<tool id="tool.nds32le-elf-mculib-v3m.archiver.base.1194398390" name="Andes Archiver" superClass="tool.nds32le-elf-mculib-v3m.archiver.base"/>
							<tool command="$(CROSS_COMPILE)gcc" commandLinePattern="${COMMAND} ${FLAGS} ${OUTPUT_FLAG} ${OUTPUT_PREFIX}${OUTPUT} ${INPUTS}" errorParsers="org.eclipse.cdt.core.GCCErrorParser" id="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.1916723169" name="Andes C Compiler" superClass="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug">
								<option id="nds32le-elf-mculib-v3m.c.compiler.exe.debug.option.debugging.level.632051741" name="Debug Level" superClass="nds32le-elf-mculib-v3m.c.compiler.exe.debug.option.debugging.level" value="nds.c.debugging.level.max" valueType="enumerated"/>
								<option id="nds.c.compiler.option.include.paths.1434635544" name="Include paths (-I)" superClass="nds.c.compiler.option.include.paths" valueType="includePath">
									<listOptionValue builtIn="false" value="&quot;..\opt\inc&quot;"/>
									<listOptionValue builtIn="false" value="&quot;..\opt&quot;"/>
									<listOptionValue builtIn="false" value="&quot;..\shr\inc&quot;"/>
									<listOptionValue builtIn="false" value="&quot;..\startup\inc&quot;"/>
								</option>
								<option id="nds32le-elf-mculib-v3m.c.compiler.exe.debug.option.optimization.level.1057598785" name="Optimization Level" superClass="nds32le-elf-mculib-v3m.c.compiler.exe.debug.option.optimization.level" value="nds.c.optimization.level.more" valueType="enumerated"/>
								<option id="nds.c.compiler.option.optimization.removesections.function.600580696" name="Remove unused function sections (-ffunction-sections)" superClass="nds.c.compiler.option.optimization.removesections.function" value="true" valueType="boolean"/>
								<option id="nds.c.compiler.option.optimization.removesections.data.1767200448" name="Remove unused data sections (-fdata-sections)" superClass="nds.c.compiler.option.optimization.removesections.data" value="true" valueType="boolean"/>
								<option id="nds.c.compiler.option.cmodel.1632248268" name="Code Model" superClass="nds.c.compiler.option.cmodel" value="nds.c.compiler.option.cmodel.medium" valueType="enumerated"/>
								<option id="nds.c.compiler.option.preprocessor.def.symbols.1286734140" name="Defined symbols (-D)" superClass="nds.c.compiler.option.preprocessor.def.symbols" valueType="definedSymbols">
									<listOptionValue builtIn="false" value="CATEGORY_CUSTOMER=CUSTOMER_HYNIX"/>
									<listOptionValue builtIn="false" value="CATEGORY_FLASH=FLASH_INTEL_N38A_QLC"/>
									<listOptionValue builtIn="false" value="CATEGORY_CONTROLLER=CONTROLLER_PS5017"/>
								</option>
								<inputType id="tool.nds.c.compiler.input.55991804" superClass="tool.nds.c.compiler.input"/>
							</tool>
							<tool command="$(CROSS_COMPILE)gcc" commandLinePattern="${COMMAND} ${FLAGS} ${OUTPUT_FLAG} ${OUTPUT_PREFIX}${OUTPUT} ${INPUTS}" errorParsers="org.eclipse.cdt.core.GLDErrorParser" id="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug.1347808613" name="Andes C Linker" superClass="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug">
								<option defaultValue="-O0" id="nds.c.link.option.optimization.level.1522725675" name="Optimization Level" superClass="nds.c.link.option.optimization.level" value="-O2" valueType="string"/>
								<option defaultValue="true" id="nds32le-elf-mculib-v3m.c.link.option.noshared.base.536118649" name="No shared libraries (-static)" superClass="nds32le-elf-mculib-v3m.c.link.option.noshared.base" valueType="boolean"/>
								<option defaultValue="" id="nds32le-elf-mculib-v3m.c.link.exe.debug.option.ldscript.548546527" name="Linker Script (-T)" superClass="nds32le-elf-mculib-v3m.c.link.exe.debug.option.ldscript" value="..\LinkerScript\ps5008_hw.ld" valueType="string"/>
								<option id="nds.c.linker.option.cmodel.1592529919" name="Code Model" superClass="nds.c.linker.option.cmodel" value="-mcmodel=medium" valueType="string"/>
								<option id="nds.c.link.option.map.2046009769" name="Write a map file. (-Map)" superClass="nds.c.link.option.map" value="true" valueType="boolean"/>
								<option id="nds.c.link.option.virtualhosting.1033311803" name="Virtual Hosting (-mvh)" superClass="nds.c.link.option.virtualhosting" value="false" valueType="boolean"/>
								<option id="nds.c.link.option.nostart.2123273521" name="Do not use standard start files (-nostartfiles)" superClass="nds.c.link.option.nostart" value="true" valueType="boolean"/>
								<option id="nds.c.link.option.removesections.1827165527" name="Remove unused sections (-ffunction-sections or -fdata-sections)" superClass="nds.c.link.option.removesections" value="true" valueType="boolean"/>
								<inputType id="tool.nds.c.linker.input.307150504" superClass="tool.nds.c.linker.input">
									<additionalInput kind="additionalinputdependency" paths="$(USER_OBJS)"/>
									<additionalInput kind="additionalinput" paths="$(LIBS)"/>
								</inputType>
							</tool>
							<tool command="$(CROSS_COMPILE)as" commandLinePattern="${COMMAND} ${FLAGS} ${OUTPUT_FLAG} ${OUTPUT_PREFIX}${OUTPUT} ${INPUTS}" errorParsers="org.eclipse.cdt.core.GASErrorParser" id="tool.nds32le-elf-mculib-v3m.assembler.exe.debug.56818500" name="Andes Assembler" superClass="tool.nds32le-elf-mculib-v3m.assembler.exe.debug">
								<option defaultValue="-g" id="nds.both.asm.option.flags.780341007" name="Assembler flags" superClass="nds.both.asm.option.flags" valueType="string"/>
								<inputType id="tool.nds.assembler.input.1711578391" superClass="tool.nds.assembler.input"/>
							</tool>
							<tool command="$(CROSS_COMPILE)nm" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot; ${OUTPUT_FLAG} ${OUTPUT}" errorParsers="" id="tool.nds32le-elf-mculib-v3m.nm.exe.debug.147103474" name="NM (symbol listing)" superClass="tool.nds32le-elf-mculib-v3m.nm.exe.debug">
								<option id="nds.nm.option.sortsymbol.1103439714" name="Sort symbols numerically by address. (-n)" superClass="nds.nm.option.sortsymbol" value="true" valueType="boolean"/>
								<option id="nds.nm.option.printfilename.364154967" name="Print name of the input file before every symbol. (-A)" superClass="nds.nm.option.printfilename" value="false" valueType="boolean"/>
							</tool>
							<tool command="$(CROSS_COMPILE)readelf" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot; ${OUTPUT_FLAG} ${OUTPUT}" errorParsers="" id="tool.nds32le-elf-mculib-v3m.readelf.exe.debug.2104916035" name="Readelf (ELF info listing)" superClass="tool.nds32le-elf-mculib-v3m.readelf.exe.debug"/>
							<tool command="$(CROSS_COMPILE)objdump" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot; ${OUTPUT_FLAG} ${OUTPUT}" errorParsers="" id="tool.nds32le-elf-mculib-v3m.objdump.exe.debug.734026724" name="Objdump (disassembly)" superClass="tool.nds32le-elf-mculib-v3m.objdump.exe.debug">
								<option id="nds.objdump.option.disable.1344521935" name="Disable. (Do not auto-generate output file.)" superClass="nds.objdump.option.disable" value="false" valueType="boolean"/>
								<option id="nds.objdump.option.linenumber.402446879" name="Include line numbers and filenames in output. (-l)" superClass="nds.objdump.option.linenumber" value="true" valueType="boolean"/>
								<option id="nds.objdump.option.otherflags.1612974401" name="Other flags" superClass="nds.objdump.option.otherflags" value="-S" valueType="string"/>
							</tool>
							<tool command="$(CROSS_COMPILE)objcopy" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot; ${OUTPUT}" errorParsers="" id="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug.855498636" name="Objcopy (object content copy)" superClass="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug">
								<option id="nds.objcopy.option.disable.1030669183" name="Disable. (Do not auto-generate output file.)" superClass="nds.objcopy.option.disable" value="false" valueType="boolean"/>
								<option id="nds.objcopy.option.stripall.2028374360" name="Remove all symbol and relocation information. (-S)" superClass="nds.objcopy.option.stripall" value="false" valueType="boolean"/>
								<option id="nds.objcopy.option.otherflags.1652637345" name="Other flags" superClass="nds.objcopy.option.otherflags" value="-R .flh_iram_fpu.data" valueType="string"/>
							</tool>
							<tool command="$(CROSS_COMPILE)size" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot;" errorParsers="" id="tool.nds32le-elf-mculib-v3m.size.exe.debug.342016628" name="Size (section size listing)" superClass="tool.nds32le-elf-mculib-v3m.size.exe.debug"/>
							<tool command="$(ANDESIGHT_ROOT)/utils/nds_ldsag" commandLinePattern="${COMMAND} ${FLAGS} ${INPUTS} -o $(LDSAG_OUT)" errorParsers="" id="tool.nds32le-elf-mculib-v3m.ldsag.base.169923824" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.base"/>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.1889204249" name="Andes C++ Compiler" superClass="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug">
								<option id="nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.option.debugging.level.1664243236" name="Debug Level" superClass="nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.option.debugging.level" value="nds.cpp.compiler.debugging.level.max" valueType="enumerated"/>
							</tool>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug.140586007" name="Andes C++ Linker" superClass="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug">
								<option defaultValue="-O0" id="nds.cpp.link.option.optimization.level.1710764903" name="Optimization Level" superClass="nds.cpp.link.option.optimization.level" valueType="string"/>
								<option defaultValue="true" id="nds32le-elf-mculib-v3m.cpp.link.option.noshared.base.764551762" name="No shared libraries (-static)" superClass="nds32le-elf-mculib-v3m.cpp.link.option.noshared.base" valueType="boolean"/>
							</tool>
							<tool command="$(ANDESIGHT_ROOT)/utils/nds_ldsag" commandLinePattern="${COMMAND} ${FLAGS} ${INPUTS} -o $(LDSAG_OUT)" errorParsers="" id="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug.843062014" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug"/>
						</toolChain>
					</folderInfo>
					<folderInfo id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********.opt_master_only" name="/" resourcePath="opt_master_only">
						<toolChain errorParsers="" id="nds.nds32le-elf-mculib-v3m.exe.debug.408836532" name="nds32le-elf-mculib-v3m" superClass="nds.nds32le-elf-mculib-v3m.exe.debug" unusedChildren="">
							<option id="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET.423936908.313979601.1887125436.1009016960.1709021572.1287899003" name="RSE_TARGET" superClass="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET.423936908"/>
							<targetPlatform id="target.nds32le-elf-mculib-v3m.platform.exe.debug.587371030" name="Debug Platform" superClass="target.nds32le-elf-mculib-v3m.platform.exe.debug"/>
							<tool id="tool.nds32le-elf-mculib-v3m.archiver.base.1464691666" name="Andes Archiver" superClass="tool.nds32le-elf-mculib-v3m.archiver.base.1194398390"/>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.1198845898" name="Andes C++ Compiler" superClass="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.1889204249">
								<option id="nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.option.debugging.level.1987047521" name="Debug Level" superClass="nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.option.debugging.level" value="nds.cpp.compiler.debugging.level.max" valueType="enumerated"/>
							</tool>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug.2111809244" name="Andes C++ Linker" superClass="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug.140586007">
								<option defaultValue="-O0" id="nds.cpp.link.option.optimization.level.718719561" name="Optimization Level" superClass="nds.cpp.link.option.optimization.level" valueType="string"/>
								<option defaultValue="true" id="nds32le-elf-mculib-v3m.cpp.link.option.noshared.base.1440371389" name="No shared libraries (-static)" superClass="nds32le-elf-mculib-v3m.cpp.link.option.noshared.base" valueType="boolean"/>
							</tool>
							<tool errorParsers="org.eclipse.cdt.core.GCCErrorParser" id="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.1546236559" name="Andes C Compiler" superClass="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.1916723169">
								<inputType id="tool.nds.c.compiler.input.1453226069" superClass="tool.nds.c.compiler.input"/>
							</tool>
							<tool id="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug.1346666934" name="Andes C Linker" superClass="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug.1347808613"/>
							<tool errorParsers="org.eclipse.cdt.core.GASErrorParser" id="tool.nds32le-elf-mculib-v3m.assembler.exe.debug.52281860" name="Andes Assembler" superClass="tool.nds32le-elf-mculib-v3m.assembler.exe.debug.56818500">
								<inputType id="tool.nds.assembler.input.149789500" superClass="tool.nds.assembler.input"/>
							</tool>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.nm.exe.debug.1710923912" name="NM (symbol listing)" superClass="tool.nds32le-elf-mculib-v3m.nm.exe.debug.147103474"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.readelf.exe.debug.2081065542" name="Readelf (ELF info listing)" superClass="tool.nds32le-elf-mculib-v3m.readelf.exe.debug.2104916035"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.objdump.exe.debug.1307299451" name="Objdump (disassembly)" superClass="tool.nds32le-elf-mculib-v3m.objdump.exe.debug.734026724"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug.695062359" name="Objcopy (object content copy)" superClass="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug.855498636"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.size.exe.debug.743174419" name="Size (section size listing)" superClass="tool.nds32le-elf-mculib-v3m.size.exe.debug.342016628"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug.1681399542" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug.843062014"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.ldsag.base.69854683" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.base.169923824"/>
						</toolChain>
					</folderInfo>
					<sourceEntries>
						<entry excluding="opt_master_only|Src_KIC|shr/inc/shr_types.h" flags="VALUE_WORKSPACE_PATH" kind="sourcePath" name=""/>
					</sourceEntries>
				</configuration>
			</storageModule>
			<storageModule moduleId="org.eclipse.cdt.core.externalSettings"/>
		</cconfiguration>
		<cconfiguration id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********.126570765">
			<storageModule buildSystemId="org.eclipse.cdt.managedbuilder.core.configurationDataProvider" id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********.126570765" moduleId="org.eclipse.cdt.core.settings" name="PS5017_Micron_B37R">
				<externalSettings/>
				<extensions>
					<extension id="com.andestech.ide.cdt.managedbuilder.core.CROSS_GNU_ELF" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="org.eclipse.cdt.core.GmakeErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.CWDLocator" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GCCErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GASErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GLDErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
				</extensions>
			</storageModule>
			<storageModule moduleId="cdtBuildSystem" version="4.0.0">
				<configuration artifactExtension="adx" artifactName="${ProjName}" buildArtefactType="nds.buildArtefactType.exe" buildProperties="org.eclipse.cdt.build.core.buildType=org.eclipse.cdt.build.core.buildType.debug,org.eclipse.cdt.build.core.buildArtefactType=nds.buildArtefactType.exe" description="" errorParsers="org.eclipse.cdt.core.GmakeErrorParser;org.eclipse.cdt.core.CWDLocator;org.eclipse.cdt.core.GCCErrorParser;org.eclipse.cdt.core.GASErrorParser;org.eclipse.cdt.core.GLDErrorParser" id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********.126570765" name="PS5017_Micron_B37R" parent="config.nds32le-elf-mculib-v3m.exe.debug" postannouncebuildStep="" postbuildStep="" preannouncebuildStep="" prebuildStep="">
					<folderInfo id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********.126570765." name="/" resourcePath="">
						<toolChain errorParsers="" id="nds.nds32le-elf-mculib-v3m.exe.debug.1907922540" name="nds32le-elf-mculib-v3m" superClass="nds.nds32le-elf-mculib-v3m.exe.debug">
							<option id="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET.192440568" name="RSE_TARGET" superClass="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET" value="rse://LOCALHOST/~toolchains/nds32le-elf-mculib-v3m" valueType="string"/>
							<targetPlatform binaryParser="com.andestech.ide.cdt.managedbuilder.core.CROSS_GNU_ELF" id="target.nds32le-elf-mculib-v3m.platform.exe.debug.686959975" name="Debug Platform" superClass="target.nds32le-elf-mculib-v3m.platform.exe.debug"/>
							<builder buildPath="${workspace_loc:/ps5008_opt/Debug}" errorParsers="org.eclipse.cdt.core.GmakeErrorParser;org.eclipse.cdt.core.CWDLocator" id="target.nds32le-elf-mculib-v3m.builder.exe.debug.202569877" keepEnvironmentInBuildfile="false" managedBuildOn="true" name="Andes Make Builder" parallelBuildOn="false" parallelizationNumber="-1" superClass="target.nds32le-elf-mculib-v3m.builder.exe.debug"/>
							<tool id="tool.nds32le-elf-mculib-v3m.archiver.base.635744532" name="Andes Archiver" superClass="tool.nds32le-elf-mculib-v3m.archiver.base"/>
							<tool command="$(CROSS_COMPILE)gcc" commandLinePattern="${COMMAND} ${FLAGS} ${OUTPUT_FLAG} ${OUTPUT_PREFIX}${OUTPUT} ${INPUTS}" errorParsers="org.eclipse.cdt.core.GCCErrorParser" id="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.2035273137" name="Andes C Compiler" superClass="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug">
								<option id="nds32le-elf-mculib-v3m.c.compiler.exe.debug.option.debugging.level.983080380" name="Debug Level" superClass="nds32le-elf-mculib-v3m.c.compiler.exe.debug.option.debugging.level" value="nds.c.debugging.level.max" valueType="enumerated"/>
								<option id="nds.c.compiler.option.include.paths.444032233" name="Include paths (-I)" superClass="nds.c.compiler.option.include.paths" valueType="includePath">
									<listOptionValue builtIn="false" value="&quot;..\opt\inc&quot;"/>
									<listOptionValue builtIn="false" value="&quot;..\opt&quot;"/>
									<listOptionValue builtIn="false" value="&quot;..\shr\inc&quot;"/>
									<listOptionValue builtIn="false" value="&quot;..\startup\inc&quot;"/>
								</option>
								<option id="nds32le-elf-mculib-v3m.c.compiler.exe.debug.option.optimization.level.53754903" name="Optimization Level" superClass="nds32le-elf-mculib-v3m.c.compiler.exe.debug.option.optimization.level" value="nds.c.optimization.level.more" valueType="enumerated"/>
								<option id="nds.c.compiler.option.optimization.removesections.function.1349710669" name="Remove unused function sections (-ffunction-sections)" superClass="nds.c.compiler.option.optimization.removesections.function" value="true" valueType="boolean"/>
								<option id="nds.c.compiler.option.optimization.removesections.data.890505689" name="Remove unused data sections (-fdata-sections)" superClass="nds.c.compiler.option.optimization.removesections.data" value="true" valueType="boolean"/>
								<option id="nds.c.compiler.option.cmodel.1875008086" name="Code Model" superClass="nds.c.compiler.option.cmodel" value="nds.c.compiler.option.cmodel.medium" valueType="enumerated"/>
								<option id="nds.c.compiler.option.preprocessor.def.symbols.299730535" name="Defined symbols (-D)" superClass="nds.c.compiler.option.preprocessor.def.symbols" valueType="definedSymbols">
									<listOptionValue builtIn="false" value="CATEGORY_CUSTOMER=CUSTOMER_MICRON"/>
									<listOptionValue builtIn="false" value="CATEGORY_FLASH=FLASH_B37R_TLC"/>
									<listOptionValue builtIn="false" value="CATEGORY_CONTROLLER=CONTROLLER_PS5017"/>
								</option>
								<inputType id="tool.nds.c.compiler.input.612098387" superClass="tool.nds.c.compiler.input"/>
							</tool>
							<tool command="$(CROSS_COMPILE)gcc" commandLinePattern="${COMMAND} ${FLAGS} ${OUTPUT_FLAG} ${OUTPUT_PREFIX}${OUTPUT} ${INPUTS}" errorParsers="org.eclipse.cdt.core.GLDErrorParser" id="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug.347668452" name="Andes C Linker" superClass="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug">
								<option defaultValue="-O0" id="nds.c.link.option.optimization.level.285427198" name="Optimization Level" superClass="nds.c.link.option.optimization.level" value="-O2" valueType="string"/>
								<option defaultValue="true" id="nds32le-elf-mculib-v3m.c.link.option.noshared.base.1009823002" name="No shared libraries (-static)" superClass="nds32le-elf-mculib-v3m.c.link.option.noshared.base" valueType="boolean"/>
								<option defaultValue="" id="nds32le-elf-mculib-v3m.c.link.exe.debug.option.ldscript.1768907432" name="Linker Script (-T)" superClass="nds32le-elf-mculib-v3m.c.link.exe.debug.option.ldscript" value="..\LinkerScript\ps5008_hw.ld" valueType="string"/>
								<option id="nds.c.linker.option.cmodel.1434178842" name="Code Model" superClass="nds.c.linker.option.cmodel" value="-mcmodel=medium" valueType="string"/>
								<option id="nds.c.link.option.map.69671173" name="Write a map file. (-Map)" superClass="nds.c.link.option.map" value="true" valueType="boolean"/>
								<option id="nds.c.link.option.virtualhosting.1644266843" name="Virtual Hosting (-mvh)" superClass="nds.c.link.option.virtualhosting" value="false" valueType="boolean"/>
								<option id="nds.c.link.option.nostart.411255086" name="Do not use standard start files (-nostartfiles)" superClass="nds.c.link.option.nostart" value="true" valueType="boolean"/>
								<option id="nds.c.link.option.removesections.855446711" name="Remove unused sections (-ffunction-sections or -fdata-sections)" superClass="nds.c.link.option.removesections" value="true" valueType="boolean"/>
								<inputType id="tool.nds.c.linker.input.1892837031" superClass="tool.nds.c.linker.input">
									<additionalInput kind="additionalinputdependency" paths="$(USER_OBJS)"/>
									<additionalInput kind="additionalinput" paths="$(LIBS)"/>
								</inputType>
							</tool>
							<tool command="$(CROSS_COMPILE)as" commandLinePattern="${COMMAND} ${FLAGS} ${OUTPUT_FLAG} ${OUTPUT_PREFIX}${OUTPUT} ${INPUTS}" errorParsers="org.eclipse.cdt.core.GASErrorParser" id="tool.nds32le-elf-mculib-v3m.assembler.exe.debug.204998352" name="Andes Assembler" superClass="tool.nds32le-elf-mculib-v3m.assembler.exe.debug">
								<option defaultValue="-g" id="nds.both.asm.option.flags.565473247" name="Assembler flags" superClass="nds.both.asm.option.flags" valueType="string"/>
								<inputType id="tool.nds.assembler.input.1982494065" superClass="tool.nds.assembler.input"/>
							</tool>
							<tool command="$(CROSS_COMPILE)nm" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot; ${OUTPUT_FLAG} ${OUTPUT}" errorParsers="" id="tool.nds32le-elf-mculib-v3m.nm.exe.debug.1790364954" name="NM (symbol listing)" superClass="tool.nds32le-elf-mculib-v3m.nm.exe.debug">
								<option id="nds.nm.option.sortsymbol.1721639643" name="Sort symbols numerically by address. (-n)" superClass="nds.nm.option.sortsymbol" value="true" valueType="boolean"/>
								<option id="nds.nm.option.printfilename.791169932" name="Print name of the input file before every symbol. (-A)" superClass="nds.nm.option.printfilename" value="false" valueType="boolean"/>
							</tool>
							<tool command="$(CROSS_COMPILE)readelf" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot; ${OUTPUT_FLAG} ${OUTPUT}" errorParsers="" id="tool.nds32le-elf-mculib-v3m.readelf.exe.debug.382593486" name="Readelf (ELF info listing)" superClass="tool.nds32le-elf-mculib-v3m.readelf.exe.debug"/>
							<tool command="$(CROSS_COMPILE)objdump" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot; ${OUTPUT_FLAG} ${OUTPUT}" errorParsers="" id="tool.nds32le-elf-mculib-v3m.objdump.exe.debug.1935229629" name="Objdump (disassembly)" superClass="tool.nds32le-elf-mculib-v3m.objdump.exe.debug">
								<option id="nds.objdump.option.disable.2014551479" name="Disable. (Do not auto-generate output file.)" superClass="nds.objdump.option.disable" value="false" valueType="boolean"/>
								<option id="nds.objdump.option.linenumber.1624211291" name="Include line numbers and filenames in output. (-l)" superClass="nds.objdump.option.linenumber" value="true" valueType="boolean"/>
								<option id="nds.objdump.option.otherflags.530981723" name="Other flags" superClass="nds.objdump.option.otherflags" value="-S" valueType="string"/>
							</tool>
							<tool command="$(CROSS_COMPILE)objcopy" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot; ${OUTPUT}" errorParsers="" id="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug.1529343392" name="Objcopy (object content copy)" superClass="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug">
								<option id="nds.objcopy.option.disable.2073195954" name="Disable. (Do not auto-generate output file.)" superClass="nds.objcopy.option.disable" value="false" valueType="boolean"/>
								<option id="nds.objcopy.option.stripall.9815926" name="Remove all symbol and relocation information. (-S)" superClass="nds.objcopy.option.stripall" value="false" valueType="boolean"/>
								<option id="nds.objcopy.option.otherflags.739447749" name="Other flags" superClass="nds.objcopy.option.otherflags" value="-R .flh_iram_fpu.data" valueType="string"/>
							</tool>
							<tool command="$(CROSS_COMPILE)size" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot;" errorParsers="" id="tool.nds32le-elf-mculib-v3m.size.exe.debug.1417384934" name="Size (section size listing)" superClass="tool.nds32le-elf-mculib-v3m.size.exe.debug"/>
							<tool command="$(ANDESIGHT_ROOT)/utils/nds_ldsag" commandLinePattern="${COMMAND} ${FLAGS} ${INPUTS} -o $(LDSAG_OUT)" errorParsers="" id="tool.nds32le-elf-mculib-v3m.ldsag.base.2612951" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.base"/>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.104962663" name="Andes C++ Compiler" superClass="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug">
								<option id="nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.option.debugging.level.1036123653" name="Debug Level" superClass="nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.option.debugging.level" value="nds.cpp.compiler.debugging.level.max" valueType="enumerated"/>
							</tool>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug.648635736" name="Andes C++ Linker" superClass="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug">
								<option defaultValue="-O0" id="nds.cpp.link.option.optimization.level.1616794102" name="Optimization Level" superClass="nds.cpp.link.option.optimization.level" valueType="string"/>
								<option defaultValue="true" id="nds32le-elf-mculib-v3m.cpp.link.option.noshared.base.60675134" name="No shared libraries (-static)" superClass="nds32le-elf-mculib-v3m.cpp.link.option.noshared.base" valueType="boolean"/>
							</tool>
							<tool command="$(ANDESIGHT_ROOT)/utils/nds_ldsag" commandLinePattern="${COMMAND} ${FLAGS} ${INPUTS} -o $(LDSAG_OUT)" errorParsers="" id="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug.753942797" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug"/>
						</toolChain>
					</folderInfo>
					<folderInfo id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********.126570765.opt_master_only" name="/" resourcePath="opt_master_only">
						<toolChain errorParsers="" id="nds.nds32le-elf-mculib-v3m.exe.debug.8349944" name="nds32le-elf-mculib-v3m" superClass="nds.nds32le-elf-mculib-v3m.exe.debug" unusedChildren="">
							<option id="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET.423936908.313979601.1887125436.1009016960.1709021572.8634972.1817683872" name="RSE_TARGET" superClass="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET.423936908"/>
							<targetPlatform id="target.nds32le-elf-mculib-v3m.platform.exe.debug.1235923990" name="Debug Platform" superClass="target.nds32le-elf-mculib-v3m.platform.exe.debug"/>
							<tool id="tool.nds32le-elf-mculib-v3m.archiver.base.717536262" name="Andes Archiver" superClass="tool.nds32le-elf-mculib-v3m.archiver.base.635744532"/>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.1769296793" name="Andes C++ Compiler" superClass="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.104962663">
								<option id="nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.option.debugging.level.49346488" name="Debug Level" superClass="nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.option.debugging.level" value="nds.cpp.compiler.debugging.level.max" valueType="enumerated"/>
							</tool>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug.540713888" name="Andes C++ Linker" superClass="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug.648635736">
								<option defaultValue="-O0" id="nds.cpp.link.option.optimization.level.918305039" name="Optimization Level" superClass="nds.cpp.link.option.optimization.level" valueType="string"/>
								<option defaultValue="true" id="nds32le-elf-mculib-v3m.cpp.link.option.noshared.base.801751547" name="No shared libraries (-static)" superClass="nds32le-elf-mculib-v3m.cpp.link.option.noshared.base" valueType="boolean"/>
							</tool>
							<tool errorParsers="org.eclipse.cdt.core.GCCErrorParser" id="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.1962220068" name="Andes C Compiler" superClass="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.2035273137">
								<inputType id="tool.nds.c.compiler.input.2083380424" superClass="tool.nds.c.compiler.input"/>
							</tool>
							<tool id="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug.209575458" name="Andes C Linker" superClass="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug.347668452"/>
							<tool errorParsers="org.eclipse.cdt.core.GASErrorParser" id="tool.nds32le-elf-mculib-v3m.assembler.exe.debug.582801969" name="Andes Assembler" superClass="tool.nds32le-elf-mculib-v3m.assembler.exe.debug.204998352">
								<inputType id="tool.nds.assembler.input.1273053593" superClass="tool.nds.assembler.input"/>
							</tool>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.nm.exe.debug.992533909" name="NM (symbol listing)" superClass="tool.nds32le-elf-mculib-v3m.nm.exe.debug.1790364954"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.readelf.exe.debug.7295558" name="Readelf (ELF info listing)" superClass="tool.nds32le-elf-mculib-v3m.readelf.exe.debug.382593486"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.objdump.exe.debug.1686191202" name="Objdump (disassembly)" superClass="tool.nds32le-elf-mculib-v3m.objdump.exe.debug.1935229629"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug.810067931" name="Objcopy (object content copy)" superClass="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug.1529343392"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.size.exe.debug.1269188002" name="Size (section size listing)" superClass="tool.nds32le-elf-mculib-v3m.size.exe.debug.1417384934"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug.1957067271" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug.753942797"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.ldsag.base.471473902" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.base.2612951"/>
						</toolChain>
					</folderInfo>
					<sourceEntries>
						<entry excluding="opt_master_only|Src_KIC|shr/inc/shr_types.h" flags="VALUE_WORKSPACE_PATH" kind="sourcePath" name=""/>
					</sourceEntries>
				</configuration>
			</storageModule>
			<storageModule moduleId="org.eclipse.cdt.core.externalSettings"/>
		</cconfiguration>
		<cconfiguration id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.826799903.**********">
			<storageModule buildSystemId="org.eclipse.cdt.managedbuilder.core.configurationDataProvider" id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.826799903.**********" moduleId="org.eclipse.cdt.core.settings" name="PS5017_Hynix_V9">
				<externalSettings/>
				<extensions>
					<extension id="com.andestech.ide.cdt.managedbuilder.core.CROSS_GNU_ELF" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="org.eclipse.cdt.core.GmakeErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.CWDLocator" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GCCErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GASErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GLDErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
				</extensions>
			</storageModule>
			<storageModule moduleId="cdtBuildSystem" version="4.0.0">
				<configuration artifactExtension="adx" artifactName="${ProjName}" buildArtefactType="nds.buildArtefactType.exe" buildProperties="org.eclipse.cdt.build.core.buildType=org.eclipse.cdt.build.core.buildType.debug,org.eclipse.cdt.build.core.buildArtefactType=nds.buildArtefactType.exe" description="" errorParsers="org.eclipse.cdt.core.GmakeErrorParser;org.eclipse.cdt.core.CWDLocator;org.eclipse.cdt.core.GCCErrorParser;org.eclipse.cdt.core.GASErrorParser;org.eclipse.cdt.core.GLDErrorParser" id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.826799903.**********" name="PS5017_Hynix_V9" parent="config.nds32le-elf-mculib-v3m.exe.debug" postannouncebuildStep="" postbuildStep="" preannouncebuildStep="" prebuildStep="">
					<folderInfo id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.826799903.**********." name="/" resourcePath="">
						<toolChain errorParsers="" id="nds.nds32le-elf-mculib-v3m.exe.debug.132846770" name="nds32le-elf-mculib-v3m" superClass="nds.nds32le-elf-mculib-v3m.exe.debug">
							<option id="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET.1624793720" name="RSE_TARGET" superClass="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET" value="rse://LOCALHOST/~toolchains/nds32le-elf-mculib-v3m" valueType="string"/>
							<targetPlatform binaryParser="com.andestech.ide.cdt.managedbuilder.core.CROSS_GNU_ELF" id="target.nds32le-elf-mculib-v3m.platform.exe.debug.1066475520" name="Debug Platform" superClass="target.nds32le-elf-mculib-v3m.platform.exe.debug"/>
							<builder buildPath="${workspace_loc:/ps5008_opt/Debug}" errorParsers="org.eclipse.cdt.core.GmakeErrorParser;org.eclipse.cdt.core.CWDLocator" id="target.nds32le-elf-mculib-v3m.builder.exe.debug.1891612578" keepEnvironmentInBuildfile="false" managedBuildOn="true" name="Andes Make Builder" parallelBuildOn="false" parallelizationNumber="-1" superClass="target.nds32le-elf-mculib-v3m.builder.exe.debug"/>
							<tool id="tool.nds32le-elf-mculib-v3m.archiver.base.2066571245" name="Andes Archiver" superClass="tool.nds32le-elf-mculib-v3m.archiver.base"/>
							<tool command="$(CROSS_COMPILE)gcc" commandLinePattern="${COMMAND} ${FLAGS} ${OUTPUT_FLAG} ${OUTPUT_PREFIX}${OUTPUT} ${INPUTS}" errorParsers="org.eclipse.cdt.core.GCCErrorParser" id="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.710774616" name="Andes C Compiler" superClass="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug">
								<option id="nds32le-elf-mculib-v3m.c.compiler.exe.debug.option.debugging.level.1923797005" name="Debug Level" superClass="nds32le-elf-mculib-v3m.c.compiler.exe.debug.option.debugging.level" value="nds.c.debugging.level.max" valueType="enumerated"/>
								<option id="nds.c.compiler.option.include.paths.1275492459" name="Include paths (-I)" superClass="nds.c.compiler.option.include.paths" valueType="includePath">
									<listOptionValue builtIn="false" value="&quot;..\opt\inc&quot;"/>
									<listOptionValue builtIn="false" value="&quot;..\opt&quot;"/>
									<listOptionValue builtIn="false" value="&quot;..\shr\inc&quot;"/>
									<listOptionValue builtIn="false" value="&quot;..\startup\inc&quot;"/>
								</option>
								<option id="nds32le-elf-mculib-v3m.c.compiler.exe.debug.option.optimization.level.1619261612" name="Optimization Level" superClass="nds32le-elf-mculib-v3m.c.compiler.exe.debug.option.optimization.level" value="nds.c.optimization.level.more" valueType="enumerated"/>
								<option id="nds.c.compiler.option.optimization.removesections.function.231201619" name="Remove unused function sections (-ffunction-sections)" superClass="nds.c.compiler.option.optimization.removesections.function" value="true" valueType="boolean"/>
								<option id="nds.c.compiler.option.optimization.removesections.data.1228861545" name="Remove unused data sections (-fdata-sections)" superClass="nds.c.compiler.option.optimization.removesections.data" value="true" valueType="boolean"/>
								<option id="nds.c.compiler.option.cmodel.1552794719" name="Code Model" superClass="nds.c.compiler.option.cmodel" value="nds.c.compiler.option.cmodel.medium" valueType="enumerated"/>
								<option id="nds.c.compiler.option.preprocessor.def.symbols.556067659" name="Defined symbols (-D)" superClass="nds.c.compiler.option.preprocessor.def.symbols" valueType="definedSymbols">
									<listOptionValue builtIn="false" value="CATEGORY_CUSTOMER=CUSTOMER_HYNIX"/>
									<listOptionValue builtIn="false" value="CATEGORY_FLASH=FLASH_HYNIX_V9_TLC"/>
									<listOptionValue builtIn="false" value="CATEGORY_CONTROLLER=CONTROLLER_PS5017"/>
								</option>
								<inputType id="tool.nds.c.compiler.input.1696719823" superClass="tool.nds.c.compiler.input"/>
							</tool>
							<tool command="$(CROSS_COMPILE)gcc" commandLinePattern="${COMMAND} ${FLAGS} ${OUTPUT_FLAG} ${OUTPUT_PREFIX}${OUTPUT} ${INPUTS}" errorParsers="org.eclipse.cdt.core.GLDErrorParser" id="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug.1901040673" name="Andes C Linker" superClass="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug">
								<option defaultValue="-O0" id="nds.c.link.option.optimization.level.2101628599" name="Optimization Level" superClass="nds.c.link.option.optimization.level" value="-O2" valueType="string"/>
								<option defaultValue="true" id="nds32le-elf-mculib-v3m.c.link.option.noshared.base.1545320356" name="No shared libraries (-static)" superClass="nds32le-elf-mculib-v3m.c.link.option.noshared.base" valueType="boolean"/>
								<option defaultValue="" id="nds32le-elf-mculib-v3m.c.link.exe.debug.option.ldscript.2105861350" name="Linker Script (-T)" superClass="nds32le-elf-mculib-v3m.c.link.exe.debug.option.ldscript" value="..\LinkerScript\ps5008_hw.ld" valueType="string"/>
								<option id="nds.c.linker.option.cmodel.1606125058" name="Code Model" superClass="nds.c.linker.option.cmodel" value="-mcmodel=medium" valueType="string"/>
								<option id="nds.c.link.option.map.1604395130" name="Write a map file. (-Map)" superClass="nds.c.link.option.map" value="true" valueType="boolean"/>
								<option id="nds.c.link.option.virtualhosting.1026030900" name="Virtual Hosting (-mvh)" superClass="nds.c.link.option.virtualhosting" value="false" valueType="boolean"/>
								<option id="nds.c.link.option.nostart.520322609" name="Do not use standard start files (-nostartfiles)" superClass="nds.c.link.option.nostart" value="true" valueType="boolean"/>
								<option id="nds.c.link.option.removesections.1458347419" name="Remove unused sections (-ffunction-sections or -fdata-sections)" superClass="nds.c.link.option.removesections" value="true" valueType="boolean"/>
								<inputType id="tool.nds.c.linker.input.1504674279" superClass="tool.nds.c.linker.input">
									<additionalInput kind="additionalinputdependency" paths="$(USER_OBJS)"/>
									<additionalInput kind="additionalinput" paths="$(LIBS)"/>
								</inputType>
							</tool>
							<tool command="$(CROSS_COMPILE)as" commandLinePattern="${COMMAND} ${FLAGS} ${OUTPUT_FLAG} ${OUTPUT_PREFIX}${OUTPUT} ${INPUTS}" errorParsers="org.eclipse.cdt.core.GASErrorParser" id="tool.nds32le-elf-mculib-v3m.assembler.exe.debug.1195370024" name="Andes Assembler" superClass="tool.nds32le-elf-mculib-v3m.assembler.exe.debug">
								<option defaultValue="-g" id="nds.both.asm.option.flags.1347892843" name="Assembler flags" superClass="nds.both.asm.option.flags" valueType="string"/>
								<inputType id="tool.nds.assembler.input.188877485" superClass="tool.nds.assembler.input"/>
							</tool>
							<tool command="$(CROSS_COMPILE)nm" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot; ${OUTPUT_FLAG} ${OUTPUT}" errorParsers="" id="tool.nds32le-elf-mculib-v3m.nm.exe.debug.218976735" name="NM (symbol listing)" superClass="tool.nds32le-elf-mculib-v3m.nm.exe.debug">
								<option id="nds.nm.option.sortsymbol.1362122283" name="Sort symbols numerically by address. (-n)" superClass="nds.nm.option.sortsymbol" value="true" valueType="boolean"/>
								<option id="nds.nm.option.printfilename.1473505823" name="Print name of the input file before every symbol. (-A)" superClass="nds.nm.option.printfilename" value="false" valueType="boolean"/>
							</tool>
							<tool command="$(CROSS_COMPILE)readelf" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot; ${OUTPUT_FLAG} ${OUTPUT}" errorParsers="" id="tool.nds32le-elf-mculib-v3m.readelf.exe.debug.430535656" name="Readelf (ELF info listing)" superClass="tool.nds32le-elf-mculib-v3m.readelf.exe.debug"/>
							<tool command="$(CROSS_COMPILE)objdump" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot; ${OUTPUT_FLAG} ${OUTPUT}" errorParsers="" id="tool.nds32le-elf-mculib-v3m.objdump.exe.debug.919162853" name="Objdump (disassembly)" superClass="tool.nds32le-elf-mculib-v3m.objdump.exe.debug">
								<option id="nds.objdump.option.disable.586349476" name="Disable. (Do not auto-generate output file.)" superClass="nds.objdump.option.disable" value="false" valueType="boolean"/>
								<option id="nds.objdump.option.linenumber.656690919" name="Include line numbers and filenames in output. (-l)" superClass="nds.objdump.option.linenumber" value="true" valueType="boolean"/>
								<option id="nds.objdump.option.otherflags.1451121366" name="Other flags" superClass="nds.objdump.option.otherflags" value="-S" valueType="string"/>
							</tool>
							<tool command="$(CROSS_COMPILE)objcopy" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot; ${OUTPUT}" errorParsers="" id="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug.684135588" name="Objcopy (object content copy)" superClass="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug">
								<option id="nds.objcopy.option.disable.932065388" name="Disable. (Do not auto-generate output file.)" superClass="nds.objcopy.option.disable" value="false" valueType="boolean"/>
								<option id="nds.objcopy.option.stripall.2087398694" name="Remove all symbol and relocation information. (-S)" superClass="nds.objcopy.option.stripall" value="false" valueType="boolean"/>
								<option id="nds.objcopy.option.otherflags.1568355769" name="Other flags" superClass="nds.objcopy.option.otherflags" value="-R .flh_iram_fpu.data" valueType="string"/>
							</tool>
							<tool command="$(CROSS_COMPILE)size" commandLinePattern="${COMMAND} ${FLAGS} &quot;${BuildArtifactFilePrefix}${BuildArtifactFileName}&quot;" errorParsers="" id="tool.nds32le-elf-mculib-v3m.size.exe.debug.65642215" name="Size (section size listing)" superClass="tool.nds32le-elf-mculib-v3m.size.exe.debug"/>
							<tool command="$(ANDESIGHT_ROOT)/utils/nds_ldsag" commandLinePattern="${COMMAND} ${FLAGS} ${INPUTS} -o $(LDSAG_OUT)" errorParsers="" id="tool.nds32le-elf-mculib-v3m.ldsag.base.1756771585" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.base"/>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.994983107" name="Andes C++ Compiler" superClass="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug">
								<option id="nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.option.debugging.level.1658130957" name="Debug Level" superClass="nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.option.debugging.level" value="nds.cpp.compiler.debugging.level.max" valueType="enumerated"/>
							</tool>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug.1030599472" name="Andes C++ Linker" superClass="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug">
								<option defaultValue="-O0" id="nds.cpp.link.option.optimization.level.1161846668" name="Optimization Level" superClass="nds.cpp.link.option.optimization.level" valueType="string"/>
								<option defaultValue="true" id="nds32le-elf-mculib-v3m.cpp.link.option.noshared.base.616825359" name="No shared libraries (-static)" superClass="nds32le-elf-mculib-v3m.cpp.link.option.noshared.base" valueType="boolean"/>
							</tool>
							<tool command="$(ANDESIGHT_ROOT)/utils/nds_ldsag" commandLinePattern="${COMMAND} ${FLAGS} ${INPUTS} -o $(LDSAG_OUT)" errorParsers="" id="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug.1728183682" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug"/>
						</toolChain>
					</folderInfo>
					<folderInfo id="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.826799903.**********.opt_master_only" name="/" resourcePath="opt_master_only">
						<toolChain errorParsers="" id="nds.nds32le-elf-mculib-v3m.exe.debug.312265259" name="nds32le-elf-mculib-v3m" superClass="nds.nds32le-elf-mculib-v3m.exe.debug" unusedChildren="">
							<option id="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET.423936908.313979601.1887125436.1009016960.1709021572.1742216434.1750921474" name="RSE_TARGET" superClass="nds32le-elf-mculib-v3m.managedbuild.option.toolchain.RSE_TARGET.423936908"/>
							<targetPlatform id="target.nds32le-elf-mculib-v3m.platform.exe.debug.668644021" name="Debug Platform" superClass="target.nds32le-elf-mculib-v3m.platform.exe.debug"/>
							<tool id="tool.nds32le-elf-mculib-v3m.archiver.base.1501765777" name="Andes Archiver" superClass="tool.nds32le-elf-mculib-v3m.archiver.base.2066571245"/>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.112999629" name="Andes C++ Compiler" superClass="tool.nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.994983107">
								<option id="nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.option.debugging.level.1599315964" name="Debug Level" superClass="nds32le-elf-mculib-v3m.cpp.compiler.exe.debug.option.debugging.level" value="nds.cpp.compiler.debugging.level.max" valueType="enumerated"/>
							</tool>
							<tool id="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug.1915461721" name="Andes C++ Linker" superClass="tool.nds32le-elf-mculib-v3m.cpp.linker.exe.debug.1030599472">
								<option defaultValue="-O0" id="nds.cpp.link.option.optimization.level.962023457" name="Optimization Level" superClass="nds.cpp.link.option.optimization.level" valueType="string"/>
								<option defaultValue="true" id="nds32le-elf-mculib-v3m.cpp.link.option.noshared.base.1686653153" name="No shared libraries (-static)" superClass="nds32le-elf-mculib-v3m.cpp.link.option.noshared.base" valueType="boolean"/>
							</tool>
							<tool errorParsers="org.eclipse.cdt.core.GCCErrorParser" id="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.73430359" name="Andes C Compiler" superClass="tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.710774616">
								<inputType id="tool.nds.c.compiler.input.2030588251" superClass="tool.nds.c.compiler.input"/>
							</tool>
							<tool id="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug.143563118" name="Andes C Linker" superClass="tool.nds32le-elf-mculib-v3m.c.linker.exe.debug.1901040673"/>
							<tool errorParsers="org.eclipse.cdt.core.GASErrorParser" id="tool.nds32le-elf-mculib-v3m.assembler.exe.debug.1462969510" name="Andes Assembler" superClass="tool.nds32le-elf-mculib-v3m.assembler.exe.debug.1195370024">
								<inputType id="tool.nds.assembler.input.334735733" superClass="tool.nds.assembler.input"/>
							</tool>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.nm.exe.debug.945580781" name="NM (symbol listing)" superClass="tool.nds32le-elf-mculib-v3m.nm.exe.debug.218976735"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.readelf.exe.debug.321691914" name="Readelf (ELF info listing)" superClass="tool.nds32le-elf-mculib-v3m.readelf.exe.debug.430535656"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.objdump.exe.debug.815451397" name="Objdump (disassembly)" superClass="tool.nds32le-elf-mculib-v3m.objdump.exe.debug.919162853"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug.255220172" name="Objcopy (object content copy)" superClass="tool.nds32le-elf-mculib-v3m.objcopy.exe.debug.684135588"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.size.exe.debug.1085146140" name="Size (section size listing)" superClass="tool.nds32le-elf-mculib-v3m.size.exe.debug.65642215"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug.1491928746" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.exe.debug.1728183682"/>
							<tool errorParsers="" id="tool.nds32le-elf-mculib-v3m.ldsag.base.605264847" name="LdSaG Tool" superClass="tool.nds32le-elf-mculib-v3m.ldsag.base.1756771585"/>
						</toolChain>
					</folderInfo>
					<sourceEntries>
						<entry excluding="opt_master_only|Src_KIC|shr/inc/shr_types.h" flags="VALUE_WORKSPACE_PATH" kind="sourcePath" name=""/>
					</sourceEntries>
				</configuration>
			</storageModule>
			<storageModule moduleId="org.eclipse.cdt.core.externalSettings"/>
		</cconfiguration>
	</storageModule>
	<storageModule moduleId="cdtBuildSystem" version="4.0.0">
		<project id="ps5008_cop0.target.nds32le-elf-mculib-v3m.exe.187441575" name="Andes Executable" projectType="target.nds32le-elf-mculib-v3m.exe"/>
	</storageModule>
	<storageModule moduleId="refreshScope" versionNumber="1">
		<resource resourceType="PROJECT" workspacePath="/ps5011_opt"/>
	</storageModule>
	<storageModule moduleId="org.eclipse.cdt.internal.ui.text.commentOwnerProjectMappings"/>
	<storageModule moduleId="org.eclipse.cdt.make.core.buildtargets"/>
	<storageModule moduleId="org.eclipse.cdt.core.LanguageSettingsProviders"/>
	<storageModule moduleId="scannerConfiguration">
		<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
		<scannerConfigBuildInfo instanceId="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********;config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********.64541172;tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.**********;tool.nds.c.compiler.input.613629388">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.andestech.ide.cdt.managedbuilder.core.ndsManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="config.nds32le-elf-mculib-v3m.exe.debug.558489182.**********;config.nds32le-elf-mculib-v3m.exe.debug.558489182.**********.Debug;tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.**********;tool.nds.c.compiler.input.**********">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.andestech.ide.cdt.managedbuilder.core.ndsManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="config.nds32le-elf-mculib-v3m.exe.debug.558489182.1010111223;config.nds32le-elf-mculib-v3m.exe.debug.558489182.1010111223.;tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.36221856;tool.nds.c.compiler.input.1880496397">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.andestech.ide.cdt.managedbuilder.core.ndsManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********.134906153.431926974;config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********.134906153.431926974.PS5017_BISC5;tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.1155499617;tool.nds.c.compiler.input.440070717">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.andestech.ide.cdt.managedbuilder.core.ndsManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.1735301301;config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.1735301301.;tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.148412858;tool.nds.c.compiler.input.664584193">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.andestech.ide.cdt.managedbuilder.core.ndsManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.826799903;config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.826799903.opt_master_only;tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.1166732605;tool.nds.c.compiler.input.187274958">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.andestech.ide.cdt.managedbuilder.core.ndsManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.143054784;config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.143054784.;tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.1149625668;tool.nds.c.compiler.input.1616321795">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.andestech.ide.cdt.managedbuilder.core.ndsManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="config.nds32le-elf-mculib-v3m.exe.debug.558489182;config.nds32le-elf-mculib-v3m.exe.debug.558489182.1407643390;tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.1769911472;tool.nds.c.compiler.input.130281648">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.andestech.ide.cdt.managedbuilder.core.ndsManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214;config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.opt;tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.165581667;tool.nds.c.compiler.input.1476567895">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.andestech.ide.cdt.managedbuilder.core.ndsManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.143054784;config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.143054784.opt_master_only;tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.461363463;tool.nds.c.compiler.input.1184367463">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.andestech.ide.cdt.managedbuilder.core.ndsManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214;config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.opt_master_only;tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.1698523508;tool.nds.c.compiler.input.1182619732">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.andestech.ide.cdt.managedbuilder.core.ndsManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.1735301301;config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.1735301301.opt_master_only;tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.890351291;tool.nds.c.compiler.input.1647624536">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.andestech.ide.cdt.managedbuilder.core.ndsManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="config.nds32le-elf-mculib-v3m.exe.debug.558489182;config.nds32le-elf-mculib-v3m.exe.debug.558489182.1187918110;tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.1769911472.1624632216;tool.nds.c.compiler.input.486529388">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.andestech.ide.cdt.managedbuilder.core.ndsManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="config.nds32le-elf-mculib-v3m.exe.debug.558489182.1010111223.1960062964.1018779769;config.nds32le-elf-mculib-v3m.exe.debug.558489182.1010111223.1960062964.1018779769.;tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.1264636935;tool.nds.c.compiler.input.1115136042">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.andestech.ide.cdt.managedbuilder.core.ndsManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301;config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.opt_master_only;tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.1420744797;tool.nds.c.compiler.input.119893064">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.andestech.ide.cdt.managedbuilder.core.ndsManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.576423965.**********;config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.576423965.**********.;tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.1696366255;tool.nds.c.compiler.input.226745235">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.andestech.ide.cdt.managedbuilder.core.ndsManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********;config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********.;tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.1245613064;tool.nds.c.compiler.input.738708871">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.andestech.ide.cdt.managedbuilder.core.ndsManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="config.nds32le-elf-mculib-v3m.exe.debug.558489182;config.nds32le-elf-mculib-v3m.exe.debug.558489182.1808637935;tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.2035847599;tool.nds.c.compiler.input.1831478022">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.andestech.ide.cdt.managedbuilder.core.ndsManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="config.nds32le-elf-mculib-v3m.exe.debug.558489182.**********;config.nds32le-elf-mculib-v3m.exe.debug.558489182.**********.;tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.336557405;tool.nds.c.compiler.input.1628121216">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.andestech.ide.cdt.managedbuilder.core.ndsManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********;config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********.opt_master_only;tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.1793820117;tool.nds.c.compiler.input.182660323">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.andestech.ide.cdt.managedbuilder.core.ndsManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="config.nds32le-elf-mculib-v3m.exe.debug.558489182;config.nds32le-elf-mculib-v3m.exe.debug.558489182.1991332789;tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.1769911472.1142737521;tool.nds.c.compiler.input.1352646424">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.andestech.ide.cdt.managedbuilder.core.ndsManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="config.nds32le-elf-mculib-v3m.exe.debug.558489182;config.nds32le-elf-mculib-v3m.exe.debug.558489182.834589634;tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.1769911472.280322190;tool.nds.c.compiler.input.446960211">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.andestech.ide.cdt.managedbuilder.core.ndsManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="config.nds32le-elf-mculib-v3m.exe.debug.558489182;config.nds32le-elf-mculib-v3m.exe.debug.558489182.;tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.865410372;tool.nds.c.compiler.input.187176510">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.andestech.ide.cdt.managedbuilder.core.ndsManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="config.nds32le-elf-mculib-v3m.exe.debug.558489182.1010111223.1960062964;config.nds32le-elf-mculib-v3m.exe.debug.558489182.1010111223.1960062964.Debug;tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.795912743;tool.nds.c.compiler.input.579431628">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.andestech.ide.cdt.managedbuilder.core.ndsManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="config.nds32le-elf-mculib-v3m.exe.debug.558489182.1010111223;config.nds32le-elf-mculib-v3m.exe.debug.558489182.1010111223.opt_master_only;tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.1329360568;tool.nds.c.compiler.input.1387935300">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.andestech.ide.cdt.managedbuilder.core.ndsManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="config.nds32le-elf-mculib-v3m.exe.debug.606486972;config.nds32le-elf-mculib-v3m.exe.debug.606486972.;tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.1850607006;tool.nds.c.compiler.input.410635395">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.andestech.ide.cdt.managedbuilder.core.ndsManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="config.nds32le-elf-mculib-v3m.exe.debug.558489182;config.nds32le-elf-mculib-v3m.exe.debug.558489182.1479050175;tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.1769911472.456056647;tool.nds.c.compiler.input.836333065">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.andestech.ide.cdt.managedbuilder.core.ndsManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********.134906153.**********;config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********.134906153.**********.PS5017_BISC5;tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.253891993;tool.nds.c.compiler.input.333341659">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.andestech.ide.cdt.managedbuilder.core.ndsManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********;config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********.opt_master_only;tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.1612613713;tool.nds.c.compiler.input.2090576446">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.andestech.ide.cdt.managedbuilder.core.ndsManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********.90222265;config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********.90222265.;tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.1007303811;tool.nds.c.compiler.input.1753702568">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.andestech.ide.cdt.managedbuilder.core.ndsManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********.112201386;config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********.112201386.opt_master_only;tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.1033577195;tool.nds.c.compiler.input.1553364048">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.andestech.ide.cdt.managedbuilder.core.ndsManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="config.nds32le-elf-mculib-v3m.exe.debug.558489182.1010111223.1960062964;config.nds32le-elf-mculib-v3m.exe.debug.558489182.1010111223.1960062964.;tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.419387623;tool.nds.c.compiler.input.235452247">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.andestech.ide.cdt.managedbuilder.core.ndsManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.576423965.515984178.748773599.1360426470;config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.576423965.515984178.748773599.1360426470.opt_master_only;tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.2034074495;tool.nds.c.compiler.input.2095930730">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.andestech.ide.cdt.managedbuilder.core.ndsManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********.112201386;config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********.112201386.opt;tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.1164557109;tool.nds.c.compiler.input.1275877617">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.andestech.ide.cdt.managedbuilder.core.ndsManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="config.nds32le-elf-mculib-v3m.exe.debug.558489182.1010111223.1960062964.1018779769;config.nds32le-elf-mculib-v3m.exe.debug.558489182.1010111223.1960062964.1018779769.opt_master_only;tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.1882845213;tool.nds.c.compiler.input.2076612931">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.andestech.ide.cdt.managedbuilder.core.ndsManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="config.nds32le-elf-mculib-v3m.exe.debug.558489182;config.nds32le-elf-mculib-v3m.exe.debug.558489182.1973793986;tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.1155503518;tool.nds.c.compiler.input.739683265">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.andestech.ide.cdt.managedbuilder.core.ndsManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="config.nds32le-elf-mculib-v3m.exe.release.373857262;config.nds32le-elf-mculib-v3m.exe.release.373857262.;tool.nds32le-elf-mculib-v3m.c.compiler.exe.release.231070255;tool.nds.c.compiler.input.762599518">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.andestech.ide.cdt.managedbuilder.core.ndsManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********.134906153.431926974;config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********.134906153.431926974.;tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.1161894390;tool.nds.c.compiler.input.2044093351">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.andestech.ide.cdt.managedbuilder.core.ndsManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="config.nds32le-elf-mculib-v3m.exe.debug.558489182.1010111223.1960062964;config.nds32le-elf-mculib-v3m.exe.debug.558489182.1010111223.1960062964.opt_master_only;tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.1898767507;tool.nds.c.compiler.input.1512611448">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.andestech.ide.cdt.managedbuilder.core.ndsManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="config.nds32le-elf-mculib-v3m.exe.debug.558489182;config.nds32le-elf-mculib-v3m.exe.debug.558489182.526298947;tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.1590758402;tool.nds.c.compiler.input.2141734219">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.andestech.ide.cdt.managedbuilder.core.ndsManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="config.nds32le-elf-mculib-v3m.exe.debug.558489182;config.nds32le-elf-mculib-v3m.exe.debug.558489182.1205491574;tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.1769911472.1524763902;tool.nds.c.compiler.input.1285714037">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.andestech.ide.cdt.managedbuilder.core.ndsManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="config.nds32le-elf-mculib-v3m.exe.release.1835681302;config.nds32le-elf-mculib-v3m.exe.release.1835681302.;tool.nds32le-elf-mculib-v3m.c.compiler.exe.release.1100528084;tool.nds.c.compiler.input.520990876">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.andestech.ide.cdt.managedbuilder.core.ndsManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.776018286;config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.776018286.opt_master_only;tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.154726502;tool.nds.c.compiler.input.907907968">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.andestech.ide.cdt.managedbuilder.core.ndsManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="config.nds32le-elf-mculib-v3m.exe.debug.558489182.1010111223.1919973696;config.nds32le-elf-mculib-v3m.exe.debug.558489182.1010111223.1919973696.opt_master_only;tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.474411784;tool.nds.c.compiler.input.612247115">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.andestech.ide.cdt.managedbuilder.core.ndsManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="config.nds32le-elf-mculib-v3m.exe.debug.558489182.1010111223.1960062964.1018779769;config.nds32le-elf-mculib-v3m.exe.debug.558489182.1010111223.1960062964.1018779769.Debug;tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.2091391502;tool.nds.c.compiler.input.1264978259">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.andestech.ide.cdt.managedbuilder.core.ndsManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="config.nds32le-elf-mculib-v3m.exe.debug.558489182.1010111223;config.nds32le-elf-mculib-v3m.exe.debug.558489182.1010111223.Debug;tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.898637588;tool.nds.c.compiler.input.1469313236">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.andestech.ide.cdt.managedbuilder.core.ndsManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301;config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.;tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.1972503159;tool.nds.c.compiler.input.186281490">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.andestech.ide.cdt.managedbuilder.core.ndsManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.1735301301;config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.1735301301.Debug;tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.1012318567;tool.nds.c.compiler.input.1682283478">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.andestech.ide.cdt.managedbuilder.core.ndsManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="config.nds32le-elf-mculib-v3m.exe.debug.558489182.**********;config.nds32le-elf-mculib-v3m.exe.debug.558489182.**********.opt;tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.414192221;tool.nds.c.compiler.input.7912603">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.andestech.ide.cdt.managedbuilder.core.ndsManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********.134906153.**********;config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********.134906153.**********.opt_master_only;tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.712680518;tool.nds.c.compiler.input.1747549753">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.andestech.ide.cdt.managedbuilder.core.ndsManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="config.nds32le-elf-mculib-v3m.exe.debug.558489182.1010111223.1919973696;config.nds32le-elf-mculib-v3m.exe.debug.558489182.1010111223.1919973696.;tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.649929548;tool.nds.c.compiler.input.1705778263">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.andestech.ide.cdt.managedbuilder.core.ndsManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********;config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********.624963808;tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.1179133390;tool.nds.c.compiler.input.462383104">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.andestech.ide.cdt.managedbuilder.core.ndsManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.576423965.515984178.748773599.1360426470;config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.576423965.515984178.748773599.1360426470.;tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.1645610730;tool.nds.c.compiler.input.1704717437">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.andestech.ide.cdt.managedbuilder.core.ndsManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214;config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.;tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.1645845889;tool.nds.c.compiler.input.1624265290">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.andestech.ide.cdt.managedbuilder.core.ndsManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********;config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********.;tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.1083288925;tool.nds.c.compiler.input.1911842201">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.andestech.ide.cdt.managedbuilder.core.ndsManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.826799903;config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.826799903.;tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.601670068;tool.nds.c.compiler.input.185037919">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.andestech.ide.cdt.managedbuilder.core.ndsManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********;config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********.;tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.1999229441;tool.nds.c.compiler.input.1162489236">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.andestech.ide.cdt.managedbuilder.core.ndsManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********.134906153.431926974;config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********.134906153.431926974.opt_master_only;tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.1628457803;tool.nds.c.compiler.input.1908529904">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.andestech.ide.cdt.managedbuilder.core.ndsManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********;config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********.;tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.827151844;tool.nds.c.compiler.input.2142548829">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.andestech.ide.cdt.managedbuilder.core.ndsManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="config.nds32le-elf-mculib-v3m.exe.debug.558489182.**********;config.nds32le-elf-mculib-v3m.exe.debug.558489182.**********.opt_master_only;tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.2108868529;tool.nds.c.compiler.input.1322300630">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.andestech.ide.cdt.managedbuilder.core.ndsManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********;config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********.opt_master_only;tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.1858877655;tool.nds.c.compiler.input.521222713">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.andestech.ide.cdt.managedbuilder.core.ndsManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.143054784;config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.143054784.1144287085;tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.1094839586;tool.nds.c.compiler.input.283615334">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.andestech.ide.cdt.managedbuilder.core.ndsManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********;config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********.opt_master_only;tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.497063530;tool.nds.c.compiler.input.859981540">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.andestech.ide.cdt.managedbuilder.core.ndsManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********.112201386;config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********.112201386.;tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.2036747734;tool.nds.c.compiler.input.986503196">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.andestech.ide.cdt.managedbuilder.core.ndsManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.776018286;config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.776018286.;tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.170321817;tool.nds.c.compiler.input.194897094">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.andestech.ide.cdt.managedbuilder.core.ndsManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="config.nds32le-elf-mculib-v3m.exe.debug.558489182.1010111223.1919973696;config.nds32le-elf-mculib-v3m.exe.debug.558489182.1010111223.1919973696.Debug;tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.1680154239;tool.nds.c.compiler.input.52904228">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.andestech.ide.cdt.managedbuilder.core.ndsManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********;config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********.182389461;tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.681795810;tool.nds.c.compiler.input.226782767">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.andestech.ide.cdt.managedbuilder.core.ndsManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214;config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.Debug;tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.294861122;tool.nds.c.compiler.input.1328086040">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.andestech.ide.cdt.managedbuilder.core.ndsManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********.90222265;config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********.90222265.opt_master_only;tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.1924301718;tool.nds.c.compiler.input.423490947">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.andestech.ide.cdt.managedbuilder.core.ndsManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.576423965.**********;config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.576423965.**********.opt_master_only;tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.787255908;tool.nds.c.compiler.input.1588469494">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.andestech.ide.cdt.managedbuilder.core.ndsManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********.134906153.**********;config.nds32le-elf-mculib-v3m.exe.debug.558489182.780951214.**********.362038301.**********.134906153.**********.;tool.nds32le-elf-mculib-v3m.c.compiler.exe.debug.1037714393;tool.nds.c.compiler.input.881008052">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.andestech.ide.cdt.managedbuilder.core.ndsManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
	</storageModule>
</cproject>
