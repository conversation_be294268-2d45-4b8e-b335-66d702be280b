/**************************************************************************/
/*                                                                        */
/*  Copyright (C) 2000-2016, Phison Electronics Corp., Inc.               */
/*  All rights reserved                                                   */
/*                                                                        */
/*  The content of this document is confidential and shall be applied     */
/*  subject to the terms and conditions of the license agreement and      */
/*  other applicable laws. Any unauthorized access, use or disclosure     */
/*  of this document is strictly prohibited and may be punishable         */
/*  under laws.                                                           */
/*                                                                        */
/*  opt_global.h                                                          */
/*                                                                        */
/*                                                                        */
/*                                                                        */
/**************************************************************************/

#ifndef OPT_GLOBAL_H_
#define OPT_GLOBAL_H_
#include "opt_env.h"
/*
 * features
 */
#define OPT_SUPPORT_PROVIDE_WRITE_OPT_STATUS_       (1)
#define IOR_EN  (S17_EN)


/*
 *  version number
 */
#if (IOR_EN)
#define CONTROLLER_VERSION_MAJOR		  ('S')
#else	/* IOR_EN */
#define CONTROLLER_VERSION_MAJOR		  ('E')
#endif	/* IOR_EN */

#if (PS5021_EN)
#define CONTROLLER_VERSION_MINOR		  ('L')
#elif (S17_EN)
#define CONTROLLER_VERSION_MINOR		  ('H')
#else /* (PS5021_EN) */
#define CONTROLLER_VERSION_MINOR		  ('D')
#endif	/* (PS5021_EN) */
#define FW_MODE						   ('A')

#if (OPT_CATEGORY_CUSTOMER == CUSTOMER_MICRON)
#define CUSTOMER_CODE					  ('N')
#else /* (CUSTOMER_NICKS_EN) */
#define CUSTOMER_CODE					  ('M')
#endif /* (CUSTOMER_NICKS_EN) */

#if (CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_TLC)
#define FW_ATTRIBUTE                               ('0')
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_QLC)	//Reip Porting 3D-V7 QLC Add
#define FW_ATTRIBUTE                               ('0')
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_BICS5_3D_TLC)
#define FW_ATTRIBUTE                               ('0')
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_BICS6_3D_TLC)
#define FW_ATTRIBUTE                               ('0')
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_BICS6_3D_QLC)    //zerio bics6 qlc add
#define FW_ATTRIBUTE                               ('0')
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_BICS8_3D_TLC)    //zerio BICS8 Add
#define FW_ATTRIBUTE                               ('0')
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_YMTC_3D_TLC)
#define FW_ATTRIBUTE                               ('0')
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_YMTC_3D_QLC)//ems add--karl
#define FW_ATTRIBUTE                               ('0')
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_SAMSUNG_3D_TLC)
#define FW_ATTRIBUTE                               ('0')
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_INTEL_3D_QLC)
#define FW_ATTRIBUTE                               ('0')
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_SAMSUNG_3D_QLC)//ssv7 qlc mst add--yingxing
#define FW_ATTRIBUTE                               ('0')
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_TOSHIBA_3D_TLC)
#define FW_ATTRIBUTE                               ('0')
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_TLC)
#if (MicronFlashID4 ==  IM_B16A_ID4)
#define FW_ATTRIBUTE                               ('2')	//1: B17
#elif (MicronFlashID4 ==  IM_B27B_ID4)
#define FW_ATTRIBUTE                               ('6')
#elif (MicronFlashID4 ==  IM_140S_ID4)	//B47R
#define FW_ATTRIBUTE                               ('9')
#endif

#elif  (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC) /*(CONFIG_FLASH_TYPE == FLASH_TYPE_TOSHIBA_3D_TLC)*/

#if (MicronFlashID4 == IM_N18A_ID4)
#define FW_ATTRIBUTE                               ('5')
#elif  (MicronFlashID4 == IM_N28A_ID4)
#define FW_ATTRIBUTE                               ('7')
#elif  (MicronFlashID4 == IM_140S_ID4)	//N48R//zerio n48r add
#define FW_ATTRIBUTE                               ('F')
#endif /*(MicronFlashID4 == IM_N18A_ID4)*/

#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_TOSHIBA_SLC) /*(CONFIG_FLASH_TYPE == FLASH_TYPE_TOSHIBA_3D_TLC)*/
#define FW_ATTRIBUTE                               ('S')
#else /*(CONFIG_FLASH_TYPE == FLASH_TYPE_TOSHIBA_3D_TLC)*/
#error "CONFIG_FLASH_TYPE"
#endif /*(CONFIG_FLASH_TYPE == FLASH_TYPE_TOSHIBA_3D_TLC)*/

#define VERSION_MAJOR					  ('0')
#define VERSION_MINOR                               ('E')
#define VERSION_TAG                                 ('A')

#define SUB_CODE_VERSION_1                       ('2')
#define SUB_CODE_VERSION_2                       ('4')//Base TSB -G

// for cop0_comm_info_struct
#define OPT_MARK_BYTES	((S17_EN) ? 8 : 0)// ulVersion1 + ulVersion2
#define OPT_PRDH_BYTES  ((READ_DISTURB_PRDH_EN)? (PS5021_EN ? 14 : 8) : 0)// ubARMModifyOPTDRequest + ubAndesModifyOPTDRequest + uwOpenTableUnit + ubPECG
#define OPT_WFI_BYTES   (PS5021_EN ? 1 : 0)            // ubCallWfi
/*
* constant
*/
typedef enum {
	OPEN_GR_UNIT,
	OPEN_GCGR_UNIT,
	OPEN_SPOR_UNIT,
	OPEN_UNIT_TYPE_NUM
} OPEN_WL_UNIT_TYPE;

/*
 * debug
 */
//Since E13 micron N48R nicks code overflow, unsupport assert debug
#if ((PS5013_EN) && (OPT_CATEGORY_CUSTOMER == CUSTOMER_MICRON))
#define OPT_SUPPORT_ASSERT_DEBUG                    (FALSE)
#else /* ((PS5013_EN) && (OPT_CATEGORY_CUSTOMER == CUSTOMER_MICRON)) */
#define OPT_SUPPORT_ASSERT_DEBUG                    (TRUE)
#endif /* ((PS5013_EN) && (OPT_CATEGORY_CUSTOMER == CUSTOMER_MICRON)) */
#define OPT_SUPPORT_DCCM_DEBUG                      (FALSE)
#define OPT_SUPPORT_DCCM_BANKING_DEBUG              (FALSE)
#define OPT_SUPPORT_DCCM_DEBUG_RECORD_Q_POP         (FALSE)
#define OPT_SUPPORT_DCCM_BEHAVIOR                   (FALSE)

#define IOR_FLOW_DEBUG       (FALSE&&IOR_EN)
#define PS_READ_LOOKUP_PTR_CHECK_DEBUG      (FALSE)
#define OPT_BACKUP_PREVIOUS_JOB_DEBUG	(FALSE)
#define JOB_BACKUP_LENGTH   (2)
#define JOB_BACKUP_QUEUE_NUM	(8)

#define SUPPORT_BVCI_WR_IF                          (TRUE)

#define MICRON_TLC_UNIT_WLBYPASS_DEBUG	(FALSE)

#define FULL_PLANE_CACHE_READ	(FALSE && (CONFIG_FLASH_TYPE != FLASH_TYPE_MICRON_3D_QLC))

#define N18_PROGRAM_ORDER_CHECK_EN (MicronFlashID4 == IM_N18A_ID4)

#include "misc/types.h"
#endif /* OPT_GLOBAL_H_ */

