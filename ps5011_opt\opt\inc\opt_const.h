/**************************************************************************/
/*                                                                        */
/*  Copyright (C) 2000-2016, Phison Electronics Corp., Inc.               */
/*  All rights reserved                                                   */
/*                                                                        */
/*  The content of this document is confidential and shall be applied     */
/*  subject to the terms and conditions of the license agreement and      */
/*  other applicable laws. Any unauthorized access, use or disclosure     */
/*  of this document is strictly prohibited and may be punishable         */
/*  under laws.                                                           */
/*                                                                        */
/*  opt_const.h                                                           */
/*                                                                        */
/*                                                                        */
/*                                                                        */
/**************************************************************************/

#ifndef _OPT_CONST_H_
#define _OPT_CONST_H_

#include "misc/types.h"

#ifdef _OPT_CONST_C_
#define EXTERN
#else
#define EXTERN extern
#endif

#if ((CONFIG_NAND_FLASH_TYPE == NAND_FLASH_TYPE_3D_QLC) || \
	 (CONFIG_NAND_FLASH_TYPE == NAND_FLASH_TYPE_2D_TLC) || \
	 (CONFIG_NAND_FLASH_TYPE == NAND_FLASH_TYPE_3D_TLC) || \
	 (CONFIG_NAND_FLASH_TYPE == NAND_FLASH_TYPE_SLC))

enum {
	TLC_PROG_FIRST_PLANE = 0,
	TLC_PROG_OTHER_PLANE = 1,
	TLC_PROG_LAST_PLANE = 2,
};

extern const U16 gwFPU_slc_read[2][4];
#if (CONFIG_FLASH_TYPE == FLASH_TYPE_SAMSUNG_3D_TLC)
extern const U16 gwFPU_xlc_read[FSP_PAGE_CNT][2][4];

extern const U16 gwFPU_xlc_1P_prog[FSP_PAGE_CNT+2][NAND_MAX_PLANE];
extern const U16 gwFPU_xlc_1P_prog_gc[FSP_PAGE_CNT+2][NAND_MAX_PLANE];

extern const U16 gwFPU_xlc_mP_prog[FSP_PAGE_CNT+2][NAND_MAX_PLANE+1];
extern const U16 gwFPU_xlc_mP_prog_gc[FSP_PAGE_CNT+2][NAND_MAX_PLANE+1];

#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_SAMSUNG_3D_QLC)
extern const U16 gwFPU_xlc_read[FSP_PAGE_CNT][2][4];

extern const U16 gwFPU_xlc_1P_prog[FSP_PAGE_CNT+1][NAND_MAX_PLANE];
extern const U16 gwFPU_xlc_1P_prog_gc[FSP_PAGE_CNT+1][NAND_MAX_PLANE];

extern const U16 gwFPU_xlc_mP_prog[FSP_PAGE_CNT+1][NAND_MAX_PLANE+1];
extern const U16 gwFPU_xlc_mP_prog_gc[FSP_PAGE_CNT+1][NAND_MAX_PLANE+1];

#else
extern const U16 gwFPU_xlc_read[FSP_PAGE_CNT][2][4];

extern const U16 gwFPU_xlc_1P_prog[FSP_PAGE_CNT];
extern const U16 gwFPU_xlc_1P_prog_gc[FSP_PAGE_CNT];

extern const U16 gwFPU_xlc_mP_prog[FSP_PAGE_CNT][3];
extern const U16 gwFPU_xlc_mP_prog_gc[FSP_PAGE_CNT][3];

#endif

extern const U16 guwFPUEndCacheRead[2];
extern const U16 guwFPUMicronSnapRead[2];
#endif /* ((CONFIG_NAND_FLASH_TYPE == NAND_FLASH_TYPE_3D_QLC) || (CONFIG_NAND_FLASH_TYPE == NAND_FLASH_TYPE_2D_TLC) || (CONFIG_NAND_FLASH_TYPE == NAND_FLASH_TYPE_3D_TLC) || (CONFIG_NAND_FLASH_TYPE == NAND_FLASH_TYPE_SLC)) */

extern const U8 bit_vld_2_bit_cnt[16];
extern const U8 select_group_id[16];	//Reip
extern const U8 page_read_split[16];
extern const U8 gubMicronSnapReadBMP[16];
extern const U8 gubMicronIWLGroupBMP[16];
extern const U8 multi_plane_prog_split[16];
extern const U8 gather_status_to_parser_state[][2][2][2];
#if (MicronFlashID4 == IM_N28A_ID4)
extern const U16 guwWordLineBypass[3][3];
extern const U16 guwValleyCheckCmd[2];
#endif /* (MicronFlashID4 == IM_N28A_ID4) */
#if (MicronFlashID4 == IM_140S_ID4)
extern const U16 guwIWLReadBinOffsetTable;
extern const U16 guwReadBinOffsetTable[2][4];
extern const U16 guwSnapReadBinOffsetTable;
#endif /* (MicronFlashID4 == IM_140S_ID4) */
#undef EXTERN
#endif /* #ifndef _OPT_CONST_H_ */
